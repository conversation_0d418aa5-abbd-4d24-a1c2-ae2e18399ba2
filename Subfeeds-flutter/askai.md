# 笔记修改与删除概述 
 ## 点击底部菜单栏的第三个note按钮，从底部弹出弹窗：
  - 使用 showModalBottomSheet，背景颜色： backgroundColor: Theme.of(context).brightness == Brightness.dark ? const Color(0xFF212534) : const Color(0xFFFFFFFF),弹窗的整体背景颜色都是这个，包括头部与尾部
  - 展示笔记列表：
   - 1.展示高亮的内容
     2.展示用户输入的笔记内容
     3.具备删除对应笔记（将mark标签从文章中删除）
     4.修改对应笔记的笔记内容，高亮颜色（修改mark标签的属性值）
    - 高亮颜色选择icon:assets/feeds/highlight.svg
     - 点击后在icon上方弹出高亮颜色选择菜单，菜单布局为一行布局，高亮颜色选择菜单整体背景颜色为#525252，由6种颜色的圆形色块构成：
       - #ffea9d
       - #c1ff9d
       - #9dd5ff
       - #af9dff
       - #ea9dff
       - #ff9d9d
## 注意
 - 笔记记录实际上是将所选文本使用mark标签包裹：
  <mark data-markjs="true" class="add-note-mark" data-popover-id="popover-1742262630915-rxz3tyaif" data-content="测试" data-date="3-18-2025" data-color="#eda7ff" style="background-color: rgb(237, 167, 255);">The New York Times</mark>
   其中data-popover-id为生成的uuid，data-content为用户输入的笔记内容，data-color为用户选择的高亮颜色，style中将用户的高亮颜色设置到background-color属性中

