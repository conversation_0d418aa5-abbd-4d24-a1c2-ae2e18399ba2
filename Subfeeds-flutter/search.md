# 搜索结果页面相关
 以assets/images/search-background.png为页面背景
 ## 页面布局 
    ### 顶部AppBar
     #### 自定义返回icon：assets/feeds/back.svg，svg使用圆角按钮包裹，背景颜色为theme下的cardColor
     #### 自定义页面标题： 根据传递的值，显示不同的标题
       - type = 1 ，标题为“Explore（搜索）”
       - type = 2 ，标题为传递的分类名称 // TODO： 分类名称需要根据传递的分类id，从分类列表中获取
    ### 分类tag列表（只有当页面传递param中的type值为2时，才需要显示）
     - 根据传递的一级分类id，显示一级分类下的二级分类的列表
    ### 搜索框 搜索icon + 占位文字，自动填充传递过来的值type = 1 时，取prop中的searchValue并自动填充到搜索框中
    ### 搜索结果列表
     - 列表整体为滚动卡片列表，40圆角，背景颜色为theme下的cardColor
     - 滚动加载
     - 空状态：assets/images/search-empty.png + 提示文字“Hm... we couldn’t find any results for(嗯...没有找到相关内容)”
     - 根据搜索框中的值，显示搜索结果列表
     - 列表头部左侧文字Results，右侧文字为搜索结果的数量‘11 found（11个结果）’
     - 列表项为rss源 + rss源下的文章列表（横线滚动，显示3个）
      - rss源
       - rss源布局为左右布局，左侧为rss源图片 + rss标题 + rss创建时间，右侧为follow按钮（根据搜索列表的状态显示follow与unfollow）
      - 对应rss源的文章列表
        - 文章列表为横向滚动列表，显示3个
        - 文章列表项为文章标题 + 文章图片 + 发布时间
        - 文章列表项点击事件，跳转到文章详情页面
      - rss源右侧按钮点击事件，点击后发送订阅或者取消订阅的请求
      - 文章列表项点击事件，跳转到文章详情页面
## 相关搜索接口示例
 接口已经实现：feeds_repository.dart
 分类请求与搜索请求返回的数据结构一致：
  {
    "code": 200,
    "msg": null,
    "data": {
        "total": 28,
        "pageList": [
            {
                "id": "5157",
                "name": "BBC Sport", /rss源名称
                "enable": null,
                "creator": "system",
                "createTime": 1740647419, // rss源创建时间戳
                "updator": "ruby",
                "updateTime": 1741255422174,
                "remark": null,
                "language": "en",
                "description": null,
                "img": "https://www.subfeeds.com/prod-apihttps://www.subfeeds.com/prod-api/img/2d1ee342b82665d223cb0575d287dee8.jpg",
                "type": 1,
                "status": 1,
                "isParam": null,
                "link": "https://feeds.bbci.co.uk/sport/rss.xml",
                "higherPath": null,
                "document": null,
                "count": null,
                "originUrl": "http://feeds.bbci.co.uk", // rss源链接 
                "domainHash": null,
                "isSub": 0, //是否订阅 0 未订阅 1 已订阅
                "category": "3", //分类id
                "popular": null,
                "imgType": null,
                "errCount": null,
                "errMsg": null,
                "statusCode": null,
                "charset": "UTF-8",
                "articleCount": null,
                "articleList": [ // 文章列表
                    {
                        "id": "744638",
                        "name": null,
                        "enable": null,
                        "creator": null,
                        "createTime": 1741577146,
                        "updator": null,
                        "updateTime": null,
                        "remark": null,
                        "title": "'Desire and intent' - Rooney full of praise for Bournemouth's front three", //文章标题
                        "link": "https://www.bbc.com/sport/football/videos/czrn1pj2pn3o",
                        "guid": null,
                        "description": "Wayne Rooney analyses the performance of Bournemouth's forwards during a 2-2 draw against Tottenham Hotspur.", //文章内容
                        "pubDate": "2025-03-10T03:25:49.000+00:00", // 发布时间
                        "status": 0,
                        "source": null,
                        "groupHash": null,
                        "img": null, //文章封面图片
                        "feedsId": "5157",
                        "feedsName": null,
                        "userId": null,
                        "hasImg": null,
                        "isCollect": null,
                        "isLaterRead": null,
                        "isRead": null,
                        "userFilter": null,
                        "statusFilter": null,
                        "popular": null
                    },
                    {
                        "id": "744620",
                        "name": null,
                        "enable": null,
                        "creator": null,
                        "createTime": 1741577146,
                        "updator": null,
                        "updateTime": null,
                        "remark": null,
                        "title": "'Spectacular handling!' - centurion George assists England's sixth try",
                        "link": "https://www.bbc.com/sport/rugby-union/videos/cdjyr9en0dno",
                        "guid": null,
                        "description": "Watch as Jamie George, on his 100th appearance for England, assists Ollie Sleightholme as he scores their sixth try against Italy in the Six Nations.",
                        "pubDate": "2025-03-10T03:25:49.000+00:00",
                        "status": 0,
                        "source": null,
                        "groupHash": null,
                        "img": null,
                        "feedsId": "5157",
                        "feedsName": null,
                        "userId": null,
                        "hasImg": null,
                        "isCollect": null,
                        "isLaterRead": null,
                        "isRead": null,
                        "userFilter": null,
                        "statusFilter": null,
                        "popular": null
                    },
                    {
                        "id": "744640",
                        "name": null,
                        "enable": null,
                        "creator": null,
                        "createTime": 1741577146,
                        "updator": null,
                        "updateTime": null,
                        "remark": null,
                        "title": "'That'll be it!' - Jadeja hits four to seal Champions Trophy victory for India",
                        "link": "https://www.bbc.com/sport/cricket/videos/c8x41p9en0do",
                        "guid": null,
                        "description": "Ravindra Jadeja hits a four to seal India's third Champions Trophy title as they beat New Zealand by four wickets in the 2025 ICC Champions Trophy final in Dubai.",
                        "pubDate": "2025-03-10T03:25:49.000+00:00",
                        "status": 0,
                        "source": null,
                        "groupHash": null,
                        "img": null,
                        "feedsId": "5157",
                        "feedsName": null,
                        "userId": null,
                        "hasImg": null,
                        "isCollect": null,
                        "isLaterRead": null,
                        "isRead": null,
                        "userFilter": null,
                        "statusFilter": null,
                        "popular": null
                    }
                ]
            },
        ]
    }
}
