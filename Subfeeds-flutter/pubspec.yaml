name: subfeeds
description: "SubFeeds是一款现代化的RSS阅读器应用，旨在为用户提供简洁、高效、智能的内容聚合和阅读体验。"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.0+1

environment:
  sdk: '>=3.2.6 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  
  # WebView
  webview_flutter: ^4.7.0
  webview_flutter_android: ^3.15.0
  webview_flutter_wkwebview: ^3.12.0
  flutter_inappwebview: ^6.0.0
  
  # 状态管理
  get: ^4.6.6
  
  # HTML 处理
  flutter_widget_from_html_core: ^0.16.0
  fwfh_cached_network_image: ^0.16.0
  fwfh_url_launcher: ^0.16.0
  
  # 本地存储
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  path_provider: ^2.1.1
  
  # 网络请求和RSS解析
  http: ^1.1.2
  http_parser: ^4.0.2
  dart_rss: ^3.0.3
  
  # 图片加载与缓存
  cached_network_image: ^3.3.0
  
  # UI组件
  flutter_svg: ^2.0.9  
  google_fonts: ^6.1.0
  shimmer: ^3.0.0
  pull_to_refresh: ^2.0.0
  
  # 路径处理
  path: ^1.8.3
  
  # 国际化
  intl: ^0.19.0

  # 瀑布流布局
  flutter_staggered_grid_view: ^0.7.0

  # 骨架屏加载效果
  skeletonizer: ^1.4.2

  # 侧边栏菜单
  flutter_side_menu: ^0.5.41
  
  # 第三方登录
  google_sign_in: ^6.1.6
  sign_in_with_apple: ^6.1.4
  crypto: ^3.0.3
  
  # 表单验证
  form_field_validator: ^1.1.0
  
  # 动画
  flutter_animate: ^4.5.0
  get_storage: ^2.1.1
  url_launcher: ^6.3.1
  synchronized: ^3.3.1
  image_picker: ^1.0.7
  flutter_slidable: ^4.0.0
  device_info_plus: ^11.3.3
  package_info_plus: ^8.3.0
  youtube_player_flutter: ^9.1.1
  flutter_html: ^3.0.0
  video_player: ^2.9.5
  chewie: ^1.11.1
  carousel_slider: ^5.1.1
  flutter_markdown: ^0.7.7+1
  flutter_highlight: ^0.7.0
  uuid: ^4.5.1

  # 图片保存和分享
  image_gallery_saver: ^2.0.3
  share_plus: ^10.1.2
  permission_handler: ^11.3.1
  fluttertoast: ^8.2.8
dev_dependencies:
  flutter_test:
    sdk: flutter
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lints
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3
# 在flutter配置下面添加
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/launcher/ic_launcher.png"
  image_path_ios: "assets/launcher/ic_launcher.png"
  adaptive_icon_foreground: "assets/launcher/ic_launcher_foreground.png"
  adaptive_icon_background: "assets/launcher/ic_launcher_background.png"
  remove_alpha_ios: true
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/launcher/ic_launcher.png"
    background_color: "#FFFFFF"
    theme_color: "#FFFFFF"
  windows:
    generate: true
    image_path: "assets/launcher/ic_launcher.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/launcher/ic_launcher.png"
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:∂
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/share/
    - assets/feeds/
    - assets/setting/
    - assets/pay/
    - assets/video/
    - assets/launcher/
    - assets/nav/
    - assets/home/
    - assets/home/<USER>/
  #   - images/a_dot_ham.jpeg∂ƒ≈∂
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
   
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SourceSerif4
      fonts:
        - asset: assets/fonts/SourceSerif4-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/SourceSerif4-Bold.ttf
          weight: 700
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
    - family: Montserrat
      fonts:
        - asset: assets/fonts/Montserrat-Regular.ttf
          weight: 400
        - asset: assets/fonts/Montserrat-Medium.ttf
          weight: 500
        - asset: assets/fonts/Montserrat-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Montserrat-Bold.ttf
          weight: 700
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

  # This section designates the resources that are generated by flutter_gen.
  # generate: true

