# Uncomment this line to define a global platform for your project
platform :ios, '12.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

def install_plugin_pods(application_path = nil, relative_symlink_dir, platform)
  # Find stable version of CocoaPods.
  pod 'GoogleSignIn', '~> 7.1.0'
  pod 'google_sign_in_ios', :path => File.join('.symlinks', 'plugins', 'google_sign_in_ios')
  
  flutter_install_plugin_pods(application_path, relative_symlink_dir)
end

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  pod 'GoogleSignIn', '~> 7.1.0'
  pod 'AppAuth', '~> 1.7.6'
  pod 'GTMAppAuth', '~> 4.1.1'
  
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  
  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    
    target.build_configurations.each do |config|
      # 设置iOS部署目标
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
      
      # 基本构建设置
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      config.build_settings['SWIFT_VERSION'] = '5.0'
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'i386'
      
      # 确保模块能够被找到
      config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
      config.build_settings['DEFINES_MODULE'] = 'YES'
      config.build_settings['CLANG_ENABLE_MODULES'] = 'YES'
      
      # 为所有目标添加模块映射设置
      config.build_settings['SWIFT_INSTALL_OBJC_HEADER'] = 'YES'
      config.build_settings['SWIFT_OBJC_INTERFACE_HEADER_NAME'] = '$(PRODUCT_NAME)-Swift.h'
      
      if target.name.include?('google_sign_in_ios')
        config.build_settings['DEFINES_MODULE'] = 'YES'
        config.build_settings['PRODUCT_MODULE_NAME'] = target.name.gsub('-', '_')
        config.build_settings['SWIFT_INSTALL_OBJC_HEADER'] = 'YES'
      end
    end
  end
end
