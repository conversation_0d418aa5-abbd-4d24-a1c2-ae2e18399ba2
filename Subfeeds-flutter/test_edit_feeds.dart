import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/dialogs/edit_feeds_bottom_sheet.dart';

/// 测试编辑订阅源功能的简单页面
class TestEditFeedsPage extends StatelessWidget {
  const TestEditFeedsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 创建一个模拟的控制器
    final controller = Get.put(FeedsController());
    
    // 添加一些测试数据
    controller.foldersList.addAll([
      {
        'id': '1',
        'groupName': '科技新闻',
        'userFeedsPos': [],
      },
      {
        'id': '2', 
        'groupName': '体育资讯',
        'userFeedsPos': [],
      },
      {
        'id': '3',
        'groupName': '财经新闻', 
        'userFeedsPos': [],
      },
    ]);
    
    controller.feedsList.addAll([
      {
        'id': '101',
        'feedsName': 'TechCrunch',
        'rssFeeds': {
          'img': 'https://techcrunch.com/favicon.ico',
          'originUrl': 'https://techcrunch.com',
        },
        'unreadCount': 5,
      },
      {
        'id': '102',
        'feedsName': 'The Verge',
        'rssFeeds': {
          'img': 'https://www.theverge.com/favicon.ico',
          'originUrl': 'https://www.theverge.com',
        },
        'unreadCount': 12,
      },
      {
        'id': '103',
        'feedsName': 'Ars Technica',
        'rssFeeds': {
          'img': 'https://arstechnica.com/favicon.ico',
          'originUrl': 'https://arstechnica.com',
        },
        'unreadCount': 3,
      },
    ]);

    return Scaffold(
      appBar: AppBar(
        title: const Text('测试编辑订阅源'),
        backgroundColor: Colors.blue,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              '点击下面的按钮测试编辑功能',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                EditFeedsBottomSheet.show(context, controller);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: const Text(
                '打开编辑弹窗',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              '功能说明：',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('• 支持Tab切换查看文件夹和订阅源'),
                  Text('• 点击"编辑"进入选择模式'),
                  Text('• 支持单选和全选功能'),
                  Text('• 支持批量删除文件夹'),
                  Text('• 支持批量取消订阅'),
                  Text('• 样式与原有feed_item保持一致'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 主函数，用于独立测试
void main() {
  runApp(GetMaterialApp(
    title: '编辑订阅源测试',
    home: const TestEditFeedsPage(),
    translations: AppTranslations(),
    locale: const Locale('zh', 'CN'),
    fallbackLocale: const Locale('en', 'US'),
  ));
}

/// 简化的翻译类
class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    'zh_CN': {
      'cancel': '取消',
      'edit': '编辑',
      'select_all': '全选',
      'deselect_all': '取消全选',
      'folders': '文件夹',
      'unsubscribed_feeds': '未订阅源',
      'delete_folders': '删除文件夹',
      'unsubscribe': '取消订阅',
      'delete': '删除',
      'are_you_sure_to_delete_folders': '确定要删除这些文件夹吗？此操作将取消文件夹中的所有订阅源。',
      'are_you_sure_to_unsubscribe_feeds': '确定要取消订阅这些订阅源吗？',
    },
    'en_US': {
      'cancel': 'Cancel',
      'edit': 'Edit',
      'select_all': 'Select All',
      'deselect_all': 'Deselect All',
      'folders': 'Folders',
      'unsubscribed_feeds': 'Unsubscribed Feeds',
      'delete_folders': 'Delete Folders',
      'unsubscribe': 'Unsubscribe',
      'delete': 'Delete',
      'are_you_sure_to_delete_folders': 'Are you sure to delete these folders? This action will cancel all feeds in these folders.',
      'are_you_sure_to_unsubscribe_feeds': 'Are you sure to unsubscribe these feeds?',
    },
  };
}
