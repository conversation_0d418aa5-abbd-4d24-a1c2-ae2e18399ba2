# Feeds主页的feeds列表弹窗选项添加对应的点击事件：
  弹窗静态布局已经实现，需要添加的是点击事件 ，相关接口已经封装好在feeds_repository.dart中
  - feeds源的弹窗列表：
   1. Rename(重命名)
    - 弹出重命名弹窗，显示feeds的logo + 重命名输入框
    - 底部添加确定与取消按钮
    - 点击确定后发送修改feeds信息的请求，参数示例：
     {"id":90, "feedsName":"Li4n0's Not"}
   2. Properties(属性)
    - 从底部弹出弹窗，显示所选feeds的信息
    - 顶部展示logo + feeds标题 + 文章未读数量
    - 内容部分展示feeds链接（link字段） feeds源链接（originUrl字段） 订阅时间（updatedTime字段 时间戳）
   3。 Mark all as read(标记所有为已读)
    - 点击后将该feeds源下的所有文章标记为已读
    - 需要判断当前feeds的未读文章数量，未读文章数量为0则不做任何操作，大于0则弹出操作确定按钮，提示用户”是否将该feeds下的所有文章标记为已读？“，用户点击确定发送请求，否则不做任何操作，请求函数:updateUserFeedsIsReadStatus
   4. Move to folder(移动到文件夹)
    - 需要判断用户是否有文件夹，没有的话提示用户去创建文件夹
    - 有文件夹则从底部弹出文件夹列表，点击对应文件夹后发送移动文件夹请求。
    - 不同列表之间的移动feeds需要传递不同的参数，可以参考feeds_controller中拖动的相关请求参数
   5。 Unfollow（取消订阅）
    - 将对应feeds从订阅列表移除，弹出操作确定按钮，需要注意从未分组的订阅列表移除feeds与从文件夹列表移除订阅是两个不同的请求
    - 发送deleteRss 请求
   - 长按文件夹的弹窗列表条目： 
    1. 标题：Rename(重命名)
    - 弹出重命名弹窗，显示重命名输入框
    - 底部添加确定与取消按钮
    - 点击确定后发送修改文件夹信息的请求：updateFeedsGroup，请求示例：
      {"id":"793","groupName":" 123123123"}
    2。Unfollow（取消订阅）
     - 将对应feeds从订阅列表移除，弹出操作确定按钮，需要注意从未分组的订阅列表移除feeds与从文件夹列表移除订阅是两个不同的请求
     - 发送deleteGroupFeeds请求