import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Drawer控制器 - 用于管理Drawer的状态
class PersistentDrawerController extends GetxController {
  // Drawer是否打开
  final isOpen = false.obs;

  // 当前选中的菜单项
  final selectedIndex = 0.obs;

  // Drawer的GlobalKey
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  // 打开Drawer
  void openDrawer() {
    if (scaffoldKey.currentState?.isDrawerOpen == false) {
      scaffoldKey.currentState?.openDrawer();
      isOpen.value = true;
    }
  }

  // 关闭Drawer
  void closeDrawer() {
    if (scaffoldKey.currentState?.isDrawerOpen == true) {
      scaffoldKey.currentState?.closeDrawer();
      isOpen.value = false;
    }
  }

  // 切换Drawer的开关状态
  void toggleDrawer() {
    if (scaffoldKey.currentState?.isDrawerOpen == true) {
      closeDrawer();
    } else {
      openDrawer();
    }
  }

  // 选择菜单项
  void selectMenuItem(int index) {
    selectedIndex.value = index;
    // 在移动设备上通常选择后会关闭抽屉
    closeDrawer();
  }
}

/// 测试页面 - 带有持久化Drawer的页面
class PersistentDrawerTestPage extends StatelessWidget {
  PersistentDrawerTestPage({Key? key}) : super(key: key);

  // 使用GetX注入控制器
  final PersistentDrawerController controller =
      Get.put(PersistentDrawerController());

  // 将Drawer内容提升为静态final变量，确保只创建一次
  static final Widget _persistentDrawer =
      GetBuilder<PersistentDrawerController>(
    builder: (controller) => Drawer(
      elevation: 16.0,
      child: Container(
        color: Get.theme.brightness == Brightness.dark
            ? const Color(0xFF25293b)
            : Colors.white,
        child: Column(
          children: [
            // 抽屉头部
            Obx(() => UserAccountsDrawerHeader(
                  decoration: BoxDecoration(
                    color: Get.theme.colorScheme.primary,
                  ),
                  accountName: Text(
                    'SubFeeds Test User',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  accountEmail: Text(
                    '<EMAIL>',
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                  currentAccountPicture: CircleAvatar(
                    backgroundColor: Colors.white,
                    child: Text(
                      'T',
                      style: TextStyle(
                        fontSize: 24.0,
                        color: Get.theme.colorScheme.primary,
                      ),
                    ),
                  ),
                )),

            // 抽屉菜单项
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  _buildDrawerItem(0, 'Home', Icons.home),
                  _buildDrawerItem(1, 'Feeds', Icons.rss_feed),
                  _buildDrawerItem(2, 'Favorites', Icons.favorite),
                  _buildDrawerItem(3, 'Reading History', Icons.history),
                  _buildDrawerItem(4, 'Profile', Icons.person),
                  const Divider(),
                  _buildDrawerItem(5, 'Settings', Icons.settings),
                  _buildDrawerItem(6, 'Help & Feedback', Icons.help),
                ],
              ),
            ),

            // 抽屉底部
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Text(
                'Version 1.0.0',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );

  // 静态方法构建菜单项
  static Widget _buildDrawerItem(int index, String title, IconData icon) {
    return GetBuilder<PersistentDrawerController>(
      builder: (controller) => Obx(() => ListTile(
            title: Text(
              title,
              style: TextStyle(
                color: controller.selectedIndex.value == index
                    ? Get.theme.colorScheme.primary
                    : Get.theme.brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                fontWeight: controller.selectedIndex.value == index
                    ? FontWeight.bold
                    : FontWeight.normal,
              ),
            ),
            leading: Icon(
              icon,
              color: controller.selectedIndex.value == index
                  ? Get.theme.colorScheme.primary
                  : Get.theme.brightness == Brightness.dark
                      ? Colors.white70
                      : Colors.black54,
            ),
            selected: controller.selectedIndex.value == index,
            selectedTileColor: controller.selectedIndex.value == index
                ? Get.theme.colorScheme.primary.withOpacity(0.1)
                : null,
            onTap: () => controller.selectMenuItem(index),
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: controller.scaffoldKey,
      appBar: AppBar(
        title: const Text('Persistent Drawer Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.brightness_4),
            onPressed: () => Get.changeThemeMode(
              Get.isDarkMode ? ThemeMode.light : ThemeMode.dark,
            ),
          ),
        ],
      ),
      drawer: _persistentDrawer, // 使用静态持久化drawer
      body: Obx(() => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Selected Menu Item:',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                Text(
                  _getPageTitle(controller.selectedIndex.value),
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: controller.toggleDrawer,
                  child: Text(
                    controller.isOpen.value ? 'Close Drawer' : 'Open Drawer',
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'The drawer state is preserved when it is opened and closed.\nTry selecting different menu items.',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          )),
      floatingActionButton: FloatingActionButton(
        onPressed: controller.openDrawer,
        child: const Icon(Icons.menu),
        tooltip: 'Open Drawer',
      ),
    );
  }

  // 获取页面标题
  String _getPageTitle(int index) {
    switch (index) {
      case 0:
        return 'Home';
      case 1:
        return 'Feeds';
      case 2:
        return 'Favorites';
      case 3:
        return 'Reading History';
      case 4:
        return 'Profile';
      case 5:
        return 'Settings';
      case 6:
        return 'Help & Feedback';
      default:
        return 'Unknown';
    }
  }
}
