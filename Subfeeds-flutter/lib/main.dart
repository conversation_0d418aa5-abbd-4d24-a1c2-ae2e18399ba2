import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:subfeeds/app/controllers/language_controller.dart';
import 'package:subfeeds/app/controllers/theme_controller.dart';

import 'package:subfeeds/app/core/theme/app_theme.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:subfeeds/app/translations/app_translations.dart';
import 'package:subfeeds/app/data/services/service_initializer.dart';
import 'package:subfeeds/app/presentation/widgets/animated_theme_wrapper.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:subfeeds/app/di/init_bindings.dart';

void main() {
  // 设置全局错误处理
  runZonedGuarded(() async {
    try {
      // 确保Flutter初始化完成
      WidgetsFlutterBinding.ensureInitialized();
      debugPrint('Flutter初始化完成');

      // 初始化InAppWebView
      if (Platform.isAndroid) {
        await AndroidInAppWebViewController.setWebContentsDebuggingEnabled(
            true);
      }

      // 初始化GetStorage（等待完成）
      await GetStorage.init();
      debugPrint('GetStorage初始化完成');

      // 设置错误处理
      FlutterError.onError = (FlutterErrorDetails details) {
        debugPrint('Flutter错误: ${details.exception}');
        debugPrint('堆栈跟踪: ${details.stack}');
      };

      // 运行应用

      runApp(const MyApp());

      // 在应用启动后异步执行其他初始化操作
      _asyncInit();
    } catch (e, stackTrace) {
      debugPrint('应用初始化失败: $e');
      debugPrint('堆栈跟踪: $stackTrace');
      runApp(const FallbackApp());
    }
  }, (error, stackTrace) {
    debugPrint('未捕获的异常: $error');
    debugPrint('堆栈跟踪: $stackTrace');
  });
}

/// 异步执行初始化操作
Future<void> _asyncInit() async {
  try {
    // 设置屏幕方向（不等待完成）
    unawaited(_setScreenOrientation());

    // 设置系统UI样式（不等待完成）
    _setSystemUIStyle();

    // 在后台初始化服务
    unawaited(_initServicesInBackground());
  } catch (e) {
    debugPrint('异步初始化失败: $e');
  }
}

/// 设置屏幕方向
Future<void> _setScreenOrientation() async {
  try {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]).timeout(
      const Duration(seconds: 1),
      onTimeout: () {
        debugPrint('设置屏幕方向超时');
      },
    );
  } catch (e) {
    debugPrint('设置屏幕方向失败: $e');
  }
}

/// 设置系统UI样式
void _setSystemUIStyle() {
  try {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ));
  } catch (e) {
    debugPrint('设置系统UI样式失败: $e');
  }
}

/// 在后台初始化服务
Future<void> _initServicesInBackground() async {
  try {
    await Future.delayed(const Duration(milliseconds: 100)); // 给UI一些时间先加载
    await ServiceInitializer().initializeServices().timeout(
      const Duration(seconds: 3),
      onTimeout: () {
        debugPrint('服务初始化超时');
      },
    );
  } catch (e) {
    debugPrint('后台服务初始化失败: $e');
  }
}

/// 主应用
class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    try {
      // 注入控制器
      final themeController = Get.put(ThemeController(), permanent: true);
      Get.put(LanguageController(), permanent: true);
      Get.put(UserController(), permanent: true);
      debugPrint('所有控制器初始化完成');

      return Obx(() => GetMaterialApp(
            title: 'SubFeeds',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeController.currentThemeMode.value,
            initialRoute: AppPages.INITIAL,
            getPages: AppPages.routes,
            initialBinding: InitBindings(),
            defaultTransition: Transition.fadeIn,
            locale: Locale(
              Get.find<LanguageController>().languageCode.value,
              Get.find<LanguageController>().countryCode.value,
            ),
            fallbackLocale: const Locale('zh', 'CN'),
            translationsKeys: AppTranslation.translations,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('zh', 'CN'), // 简体中文
              Locale('en', 'US'), // 英文
            ],
          ));
    } catch (e) {
      debugPrint('构建MyApp失败: $e');
      return const FallbackApp();
    }
  }
}

/// 备用应用（在主应用初始化失败时使用）
class FallbackApp extends StatelessWidget {
  const FallbackApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SubFeeds',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.purple,
        useMaterial3: true,
      ),
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.warning_amber_rounded,
                  size: 64, color: Colors.orange),
              const SizedBox(height: 16),
              const Text(
                '应用初始化中...',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  // 重新启动应用
                  runApp(const MyApp());
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
