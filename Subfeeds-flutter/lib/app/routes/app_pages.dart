import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/about/about_screen.dart';
import 'package:subfeeds/app/presentation/screens/auth/auth_binding.dart';
import 'package:subfeeds/app/presentation/screens/auth/forgot_password_screen.dart';
import 'package:subfeeds/app/presentation/screens/auth/login_screen.dart';
import 'package:subfeeds/app/presentation/screens/auth/login_selection_screen.dart';
import 'package:subfeeds/app/presentation/screens/auth/register_screen.dart';
import 'package:subfeeds/app/presentation/screens/article/article_binding.dart';
import 'package:subfeeds/app/presentation/screens/article/article_screen.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>';
import 'package:subfeeds/app/presentation/screens/home/<USER>';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/controller/discover_controller.dart';
import 'package:subfeeds/app/presentation/screens/legal/privacy_policy_webview_screen.dart';
import 'package:subfeeds/app/presentation/screens/legal/terms_of_service_webview_screen.dart';
import 'package:subfeeds/app/presentation/screens/onboarding/onboarding_binding.dart';
import 'package:subfeeds/app/presentation/screens/onboarding/onboarding_screen.dart';
import 'package:subfeeds/app/presentation/screens/profile/change_password_screen.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart';
import 'package:subfeeds/app/presentation/screens/search/search_screen.dart';
import 'package:subfeeds/app/presentation/screens/settings/settings_binding.dart';
import 'package:subfeeds/app/presentation/screens/settings/settings_screen.dart';
import 'package:subfeeds/app/presentation/screens/settings/premium_plan_screen.dart';
import 'package:subfeeds/app/presentation/screens/splash/splash_binding.dart';
import 'package:subfeeds/app/presentation/screens/splash/splash_screen.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/footpoint_screen.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/footpoint_binding.dart';
import 'package:subfeeds/app/presentation/screens/feeds_articles/feeds_articles_screen.dart';
import 'package:subfeeds/app/presentation/screens/feeds_articles/feeds_articles_controller.dart';
import 'package:subfeeds/app/presentation/screens/news/special_news_screen.dart';
import 'package:subfeeds/app/presentation/screens/news/special_news_detail_screen.dart';
import 'package:subfeeds/temp_test.dart';
import 'package:subfeeds/app/data/repositories/article_repository.dart';
import 'package:subfeeds/app/presentation/screens/feeds/unfollow_feeds_screen.dart';
import 'package:subfeeds/app/presentation/screens/add_feeds/add_feeds.dart';

part 'app_routes.dart';

/// 应用路由配置
class AppPages {
  /// 避免实例化
  AppPages._();

  /// 初始路由
  static const INITIAL = Routes.SPLASH;

  /// 路由页面
  static final routes = [
    GetPage(
      name: Routes.SPLASH,
      page: () => const SplashScreen(),
      binding: SplashBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.LOGIN_SELECTION,
      page: () => const LoginSelectionScreen(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.LOGIN,
      page: () => const LoginScreen(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.ADD_FEEDS,
      page: () => AddFeedsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.REGISTER,
      page: () => const RegisterScreen(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.FORGOT_PASSWORD,
      page: () => const ForgotPasswordScreen(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.HOME,
      page: () => const HomeScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => HomeController());
        Get.lazyPut(() => FeedsController());
        Get.lazyPut(() => DiscoverController());
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.ONBOARDING,
      page: () => const OnboardingScreen(),
      binding: OnboardingBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.SEARCH,
      page: () => const SearchScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => SearchController());
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.ARTICLE,
      page: () => ArticleScreen(),
      binding: ArticleBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.SETTINGS,
      page: () => const SettingsScreen(),
      binding: SettingsBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.ABOUT,
      page: () => const AboutScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.PRIVACY_POLICY,
      page: () => const PrivacyPolicyWebViewScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.TERMS_OF_SERVICE,
      page: () => const TermsOfServiceWebViewScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.CHANGE_PASSWORD,
      page: () => const ChangePasswordScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.FOOTPOINT,
      page: () {
        final type = Get.arguments != null
            ? Get.arguments as FootpointType
            : FootpointType.history;
        return FootpointScreen(initialType: type);
      },
      binding: FootpointBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.FEEDS_ARTICLES,
      page: () => FeedsArticlesScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => FeedsArticlesController());
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.DRAWER_TEST,
      page: () => PersistentDrawerTestPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: UNFOLLOW_FEEDS,
      page: () {
        final arguments = Get.arguments;
        return UnfollowFeedsScreen(feed: arguments['feed']);
      },
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
      binding: BindingsBuilder(() {
        Get.lazyPut<ArticleRepository>(() => ArticleRepository());
      }),
    ),
    GetPage(
      name: Routes.SPECIAL_NEWS,
      page: () => const SpecialNewsScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.SPECIAL_NEWS_DETAIL,
      page: () {
        final arguments = Get.arguments;
        return SpecialNewsDetailScreen(
            type: arguments['type'], source: arguments['source']);
      },
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
    GetPage(
      name: Routes.PREMIUM_PLAN,
      page: () => const PremiumPlanScreen(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 100),
    ),
  ];

  /// 足迹页
  static const FOOTPOINT = '/footpoint';

  /// 新添加的路由
  static const UNFOLLOW_FEEDS = '/unfollow-feeds';

  /// 特殊新闻页
  static const SPECIAL_NEWS = '/special-news';

  /// 特殊新闻详情页
  static const SPECIAL_NEWS_DETAIL = '/special-news-detail';
}
