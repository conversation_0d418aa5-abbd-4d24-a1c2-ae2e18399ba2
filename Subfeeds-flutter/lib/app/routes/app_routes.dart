part of 'app_pages.dart';

/// 应用路由常量
abstract class Routes {
  /// 禁止实例化
  Routes._();

  /// 启动页
  static const SPLASH = '/splash';

  /// 引导页
  static const ONBOARDING = '/onboarding';

  /// 首页
  static const HOME = '/home';

  /// 搜索页
  static const SEARCH = '/search';

  /// 文章详情页
  static const ARTICLE = '/article';

  /// 设置页
  static const SETTINGS = '/settings';

  /// 收藏页
  static const FAVORITES = '/favorites';

  /// 关于页
  static const ABOUT = '/about';

  /// 隐私政策页
  static const PRIVACY_POLICY = '/privacy_policy';

  /// 用户协议页
  static const TERMS_OF_SERVICE = '/terms_of_service';

  /// 个人资料页
  static const PROFILE = '/profile';

  /// 登录选择页
  static const LOGIN_SELECTION = '/login_selection';

  /// 登录页
  static const LOGIN = '/login';

  /// 注册页
  static const REGISTER = '/register';

  // 推荐订阅呀feeds页
  static const UNFOLLOW_FEEDS = '/unfollow-feeds';

  /// 忘记密码页
  static const FORGOT_PASSWORD = '/forgot_password';

  /// 修改密码页
  static const CHANGE_PASSWORD = '/change_password';

  /// 足迹页
  static const FOOTPOINT = '/footpoint';

  /// Feeds文章列表页
  static const FEEDS_ARTICLES = '/feeds_articles';

  /// 特殊新闻页
  static const SPECIAL_NEWS = '/special-news';

  /// 特殊新闻详情页
  static const SPECIAL_NEWS_DETAIL = '/special-news-detail';

  /// 持久化Drawer测试页
  static const DRAWER_TEST = '/drawer_test';

  /// 付费计划页
  static const PREMIUM_PLAN = '/premium_plan';

  /// 添加订阅页
  static const ADD_FEEDS = '/add-feeds';
}
