import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/data/services/auth_guard_service.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

/// 认证中间件
///
/// 用于保护需要登录才能访问的路由。
/// 如果用户未登录，会自动重定向到登录页面，
/// 并在登录成功后返回到原始目标页面。
class AuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    final userController = Get.find<UserController>();

    // 如果用户已登录，允许访问
    if (userController.isLoggedIn) {
      return null;
    }

    // 如果用户未登录，重定向到登录选择页面
    // 并保存原始路由以便登录后返回
    return RouteSettings(
      name: Routes.LOGIN_SELECTION,
      arguments: {'returnRoute': route},
    );
  }
}

/// 可选认证中间件
///
/// 用于那些可以在未登录状态下访问，但登录后会有更多功能的页面。
/// 不会阻止访问，但会提供登录状态信息。
class OptionalAuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 2;

  @override
  RouteSettings? redirect(String? route) {
    // 不阻止访问，只是检查登录状态
    final userController = Get.find<UserController>();

    // 可以在这里添加一些逻辑，比如预加载用户数据
    if (!userController.isLoggedIn) {
      debugPrint('用户未登录访问可选认证页面: $route');
    }

    return null; // 允许访问
  }
}

/// 高级功能中间件
///
/// 用于保护高级功能页面，如高级设置、数据同步等。
/// 除了检查登录状态，还可以检查用户权限或订阅状态。
class PremiumFeatureMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    final userController = Get.find<UserController>();

    // 首先检查是否登录
    if (!userController.isLoggedIn) {
      return RouteSettings(
        name: Routes.LOGIN_SELECTION,
        arguments: {
          'returnRoute': route,
          'message': 'auth_required_premium_access'.tr,
        },
      );
    }

    // 这里可以添加高级功能权限检查
    // 例如检查用户是否有高级订阅
    // if (!userController.hasPremiumSubscription) {
    //   return RouteSettings(
    //     name: Routes.PREMIUM_PLAN,
    //     arguments: {'returnRoute': route},
    //   );
    // }

    return null; // 允许访问
  }
}

/// 管理员中间件
///
/// 用于保护管理员功能页面。
class AdminMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    final userController = Get.find<UserController>();

    // 检查是否登录
    if (!userController.isLoggedIn) {
      return RouteSettings(
        name: Routes.LOGIN_SELECTION,
        arguments: {
          'returnRoute': route,
          'message': 'auth_required_admin_access'.tr,
        },
      );
    }

    // 检查是否是管理员
    // if (!userController.isAdmin) {
    //   return RouteSettings(
    //     name: Routes.HOME,
    //     arguments: {'error': 'access_denied'.tr},
    //   );
    // }

    return null; // 允许访问
  }
}

/// 认证中间件工厂
///
/// 提供便捷的方法来创建不同类型的认证中间件。
class AuthMiddlewareFactory {
  /// 创建基本认证中间件
  static AuthMiddleware basic() => AuthMiddleware();

  /// 创建可选认证中间件
  static OptionalAuthMiddleware optional() => OptionalAuthMiddleware();

  /// 创建高级功能中间件
  static PremiumFeatureMiddleware premium() => PremiumFeatureMiddleware();

  /// 创建管理员中间件
  static AdminMiddleware admin() => AdminMiddleware();

  /// 创建自定义认证中间件
  static CustomAuthMiddleware custom({
    required bool Function() authCheck,
    required String redirectRoute,
    Map<String, dynamic>? redirectArguments,
    String? errorMessage,
  }) {
    return CustomAuthMiddleware(
      authCheck: authCheck,
      redirectRoute: redirectRoute,
      redirectArguments: redirectArguments,
      errorMessage: errorMessage,
    );
  }
}

/// 自定义认证中间件
///
/// 允许自定义认证逻辑和重定向行为。
class CustomAuthMiddleware extends GetMiddleware {
  final bool Function() authCheck;
  final String redirectRoute;
  final Map<String, dynamic>? redirectArguments;
  final String? errorMessage;

  CustomAuthMiddleware({
    required this.authCheck,
    required this.redirectRoute,
    this.redirectArguments,
    this.errorMessage,
  });

  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    if (authCheck()) {
      return null; // 通过认证，允许访问
    }

    // 认证失败，重定向
    final arguments = Map<String, dynamic>.from(redirectArguments ?? {});
    arguments['returnRoute'] = route;

    if (errorMessage != null) {
      arguments['message'] = errorMessage;
    }

    return RouteSettings(
      name: redirectRoute,
      arguments: arguments,
    );
  }
}

/// 认证中间件助手类
///
/// 提供一些实用的方法来处理认证相关的逻辑。
class AuthMiddlewareHelper {
  /// 检查用户是否已登录
  static bool isLoggedIn() {
    try {
      final userController = Get.find<UserController>();
      return userController.isLoggedIn;
    } catch (e) {
      debugPrint('检查登录状态失败: $e');
      return false;
    }
  }

  /// 检查用户是否有高级权限
  static bool hasPremiumAccess() {
    try {
      final userController = Get.find<UserController>();
      return userController.isLoggedIn; // 暂时简化为已登录即有权限
      // 实际实现中可以检查用户的订阅状态
      // return userController.isLoggedIn && userController.hasPremiumSubscription;
    } catch (e) {
      debugPrint('检查高级权限失败: $e');
      return false;
    }
  }

  /// 检查用户是否是管理员
  static bool isAdmin() {
    try {
      final userController = Get.find<UserController>();
      // 这里可以检查用户的角色
      return userController.isLoggedIn; // 暂时简化
      // return userController.isLoggedIn && userController.user.value?.role == 'admin';
    } catch (e) {
      debugPrint('检查管理员权限失败: $e');
      return false;
    }
  }

  /// 获取登录后的返回路由
  static String? getReturnRoute() {
    final arguments = Get.arguments;
    if (arguments is Map<String, dynamic>) {
      return arguments['returnRoute'] as String?;
    }
    return null;
  }

  /// 处理登录成功后的导航
  static void handleLoginSuccess() {
    final returnRoute = getReturnRoute();
    if (returnRoute != null && returnRoute.isNotEmpty) {
      Get.offAllNamed(returnRoute);
    } else {
      Get.offAllNamed(Routes.HOME);
    }
  }
}
