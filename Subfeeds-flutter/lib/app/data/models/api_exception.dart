/// API异常类，用于处理网络请求中的各种错误
class ApiException implements Exception {
  final String message;
  final int? code;
  final dynamic data;

  ApiException({
    required this.message,
    this.code,
    this.data,
  });

  /// 网络错误
  factory ApiException.network(String message) {
    return ApiException(
      message: message,
      code: -1001,
    );
  }

  /// 服务器错误
  factory ApiException.server(String message, {int? code}) {
    return ApiException(
      message: message,
      code: code ?? -1002,
    );
  }

  /// 请求超时
  factory ApiException.timeout() {
    return ApiException(
      message: '请求超时，请检查网络连接',
      code: -1003,
    );
  }

  /// 未授权错误（如token失效）
  factory ApiException.unauthorized() {
    return ApiException(
      message: '登录已过期，请重新登录',
      code: 401,
    );
  }

  /// 权限不足
  factory ApiException.forbidden() {
    return ApiException(
      message: '没有权限访问该资源',
      code: 403,
    );
  }

  /// 资源不存在
  factory ApiException.notFound() {
    return ApiException(
      message: '请求的资源不存在',
      code: 404,
    );
  }

  /// 业务逻辑错误
  factory ApiException.business(String message, {int? code, dynamic data}) {
    return ApiException(
      message: message,
      code: code ?? -1,
      data: data,
    );
  }

  /// 解析错误
  factory ApiException.parse() {
    return ApiException(
      message: '数据解析错误',
      code: -1004,
    );
  }

  /// 取消请求
  factory ApiException.cancel() {
    return ApiException(
      message: '请求已取消',
      code: -1005,
    );
  }

  @override
  String toString() {
    return 'ApiException{message: $message, code: $code, data: $data}';
  }
}
