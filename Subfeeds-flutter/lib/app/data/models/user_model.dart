/// 用户模型类
class UserModel {
  final String? id;
  final int? userId;
  final String? account;
  final String? email;
  final String? name;
  final String? avatar;
  final String? uuid;
  final int? channel;
  final int? type;
  final bool? operation;
  final int? relevance;
  final String? password;
  final String? phone;
  final int? lastOnlineTime;
  final String? createIp;
  final String? ipAddress;
  final int? enable;
  final String? googleId;
  final String? wechatId;
  final String? appleId;
  final String? motto;
  final String? roleId;
  final int? createTime;
  final int? updateTime;

  UserModel({
    this.id,
    this.userId,
    this.account,
    this.email,
    this.name,
    this.avatar,
    this.uuid,
    this.channel,
    this.type,
    this.operation,
    this.relevance,
    this.password,
    this.phone,
    this.lastOnlineTime,
    this.createIp,
    this.ipAddress,
    this.enable,
    this.googleId,
    this.wechatId,
    this.appleId,
    this.motto,
    this.roleId,
    this.createTime,
    this.updateTime,
  });

  /// 从JSON映射创建UserModel实例
  factory UserModel.fromJson(Map<String, dynamic> json) {
    print('正在解析用户数据: $json'); // 添加日志
    return UserModel(
      id: json['id']?.toString(),
      userId: json['userId'],
      account: json['email'], // 使用 email 作为 account
      email: json['email'],
      name: json['name'],
      avatar: json['avatar'],
      uuid: json['uuid'],
      channel: json['channel'],
      type: json['type'],
      operation: json['operation'],
      relevance: json['relevance'],
      password: json['password'],
      phone: json['phone'],
      lastOnlineTime: json['lastOnlineTime'],
      createIp: json['createIp'],
      ipAddress: json['ipAddress'],
      enable: json['enable'],
      googleId: json['googleId'],
      wechatId: json['wechatId'],
      appleId: json['appleId'],
      motto: json['motto'],
      roleId: json['roleId'],
      createTime: json['createTime'],
      updateTime: json['updateTime'],
    );
  }

  /// 转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'account': account ?? email,
      'email': email ?? account,
      'name': name,
      'avatar': avatar,
      'uuid': uuid,
      'channel': channel,
      'type': type,
      'operation': operation,
      'relevance': relevance,
      'phone': phone,
      'lastOnlineTime': lastOnlineTime,
      'createIp': createIp,
      'ipAddress': ipAddress,
      'enable': enable,
      'googleId': googleId,
      'wechatId': wechatId,
      'appleId': appleId,
      'motto': motto,
      'roleId': roleId,
      'createTime': createTime,
      'updateTime': updateTime,
    };
  }

  /// 创建登录请求数据
  Map<String, dynamic> toLoginRequest(
      {required String password, required int type}) {
    return {
      'account': account ?? email,
      'pwd': password,
      'channel': 0,
      'type': type,
      'operation': true,
    };
  }

  /// 创建第三方登录请求数据
  Map<String, dynamic> toAuthLoginRequest() {
    return {
      'account': account,
      'name': name,
      'avatar': avatar,
      'uuid': uuid,
      'channel': channel,
      'type': 0,
      'pwd': '',
      'relevance': 0,
    };
  }

  /// 创建注册请求数据
  Map<String, dynamic> toRegisterRequest({
    required String password,
    required String verifyCode,
  }) {
    return {
      'account': account,
      'name': name,
      'pwd': password,
      'verifyCode': verifyCode,
      'channel': 0,
      'type': 0,
    };
  }

  /// 创建重置密码请求数据
  Map<String, dynamic> toResetPasswordRequest({
    required String password,
    required String verifyCode,
  }) {
    return {
      'account': account,
      'pwd': password,
      'verifyCode': verifyCode,
      'channel': 0,
      'type': 1,
    };
  }

  /// 创建发送验证码请求数据
  Map<String, dynamic> toSendVerifyCodeRequest({required int type}) {
    return {
      'account': account,
      'channel': 0,
      'name': name ?? '',
      'type': type, // 注册填0，重置密码填1
    };
  }

  /// 复制并更新用户模型
  UserModel copyWith({
    String? id,
    int? userId,
    String? account,
    String? email,
    String? name,
    String? avatar,
    String? uuid,
    int? channel,
    int? type,
    bool? operation,
    int? relevance,
    String? password,
    String? phone,
    int? lastOnlineTime,
    String? createIp,
    String? ipAddress,
    int? enable,
    String? googleId,
    String? wechatId,
    String? appleId,
    String? motto,
    String? roleId,
    int? createTime,
    int? updateTime,
  }) {
    return UserModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      account: account ?? this.account,
      email: email ?? this.email,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      uuid: uuid ?? this.uuid,
      channel: channel ?? this.channel,
      type: type ?? this.type,
      operation: operation ?? this.operation,
      relevance: relevance ?? this.relevance,
      password: password ?? this.password,
      phone: phone ?? this.phone,
      lastOnlineTime: lastOnlineTime ?? this.lastOnlineTime,
      createIp: createIp ?? this.createIp,
      ipAddress: ipAddress ?? this.ipAddress,
      enable: enable ?? this.enable,
      googleId: googleId ?? this.googleId,
      wechatId: wechatId ?? this.wechatId,
      appleId: appleId ?? this.appleId,
      motto: motto ?? this.motto,
      roleId: roleId ?? this.roleId,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }
}
