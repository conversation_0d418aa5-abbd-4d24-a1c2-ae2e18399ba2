import '../../presentation/screens/article/widgets/chat_dialog.dart';

/// 聊天对话模型
class ChatConversation {
  final String id;
  final String articleTitle;
  final String articleContent;
  final String articleUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<ChatMessage> messages;

  ChatConversation({
    required this.id,
    required this.articleTitle,
    required this.articleContent,
    required this.articleUrl,
    required this.createdAt,
    required this.updatedAt,
    required this.messages,
  });

  /// 获取对话标题（使用第一条用户消息）
  String get title {
    final firstUserMessage = messages.firstWhere(
      (msg) => msg.isUser,
      orElse: () =>
          ChatMessage(content: '未知对话', isUser: true, timestamp: DateTime.now()),
    );
    return firstUserMessage.content.length > 50
        ? '${firstUserMessage.content.substring(0, 50)}...'
        : firstUserMessage.content;
  }

  /// 获取对话描述（使用第一条AI回复）
  String get description {
    final firstAiMessage = messages.firstWhere(
      (msg) => !msg.isUser,
      orElse: () => ChatMessage(
          content: '暂无回复', isUser: false, timestamp: DateTime.now()),
    );
    return firstAiMessage.content.length > 100
        ? '${firstAiMessage.content.substring(0, 100)}...'
        : firstAiMessage.content;
  }

  /// 从JSON创建对象
  factory ChatConversation.fromJson(Map<String, dynamic> json) {
    return ChatConversation(
      id: json['id'] as String,
      articleTitle: json['article_title'] as String,
      articleContent: json['article_content'] as String,
      articleUrl: json['article_url'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      messages: (json['messages'] as List<dynamic>)
          .map((msg) =>
              ChatMessageExtension.fromJson(msg as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'article_title': articleTitle,
      'article_content': articleContent,
      'article_url': articleUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'messages': messages.map((msg) => msg.toJson()).toList(),
    };
  }

  /// 创建副本
  ChatConversation copyWith({
    String? id,
    String? articleTitle,
    String? articleContent,
    String? articleUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<ChatMessage>? messages,
  }) {
    return ChatConversation(
      id: id ?? this.id,
      articleTitle: articleTitle ?? this.articleTitle,
      articleContent: articleContent ?? this.articleContent,
      articleUrl: articleUrl ?? this.articleUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      messages: messages ?? this.messages,
    );
  }
}

/// 聊天消息模型扩展
extension ChatMessageExtension on ChatMessage {
  /// 从JSON创建ChatMessage
  static ChatMessage fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      content: json['content'] as String,
      isUser: json['is_user'] as bool,
      isLoading: json['is_loading'] as bool? ?? false,
      isError: json['is_error'] as bool? ?? false,
      isComplete: json['is_complete'] as bool? ?? true, // 默认为true以兼容旧数据
      timestamp: DateTime.parse(json['timestamp'] as String),
      id: json['id'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'is_user': isUser,
      'is_loading': isLoading,
      'is_error': isError,
      'is_complete': isComplete,
      'timestamp': timestamp.toIso8601String(),
      'id': id,
    };
  }
}
