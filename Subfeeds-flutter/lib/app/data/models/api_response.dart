import 'dart:convert';

/// API响应模型，用于处理统一的数据结构
class ApiResponse<T> {
  final bool isSuccess;
  final int code;
  final String msg;
  final T? data;
  final String? token;

  ApiResponse({
    required this.code,
    required this.msg,
    this.data,
    this.token,
  }) : isSuccess = _isSuccessCode(code);

  /// 判断状态码是否表示成功
  static bool _isSuccessCode(int code) {
    // 业务成功码（自定义的正数码）
    if (code >= 0 && code <= 100) {
      return true;
    }
    // HTTP 2xx 成功状态码
    if (code >= 200 && code < 300) {
      return true;
    }
    // 其他情况都视为失败
    return false;
  }

  /// 从JSON映射创建ApiResponse实例
  factory ApiResponse.fromJson(Map<String, dynamic> json) {
    return ApiResponse<T>(
      code: json['code'] as int? ?? 500,
      msg: json['msg'] as String? ?? '',
      data: json['data'] as T?,
      token: json['token'] as String?,
    );
  }

  /// 从JSON字符串创建ApiResponse实例
  factory ApiResponse.fromJsonString(
      String jsonString, T? Function(dynamic)? fromJsonT) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return ApiResponse.fromJson(json);
  }

  /// 创建成功的响应
  factory ApiResponse.success(
      {T? data, String? token, required String msg, required int code}) {
    return ApiResponse<T>(
      code: code,
      msg: msg,
      data: data,
      token: token,
    );
  }

  /// 创建失败的响应
  factory ApiResponse.failure({
    required int code,
    required String msg,
  }) {
    return ApiResponse<T>(
      code: code,
      msg: msg,
    );
  }

  /// 转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'msg': msg,
      'data': data,
      'token': token,
    };
  }

  @override
  String toString() {
    return 'ApiResponse{code: $code, data: $data, msg: $msg, token: $token}';
  }
}
