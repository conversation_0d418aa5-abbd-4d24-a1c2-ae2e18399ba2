import 'package:http/http.dart' as http;
import 'package:dart_rss/dart_rss.dart';
import 'dart:async';

/// RSS提供者，负责获取和解析RSS源
class RssProvider {
  /// 获取RSS源内容并解析
  Future<List<Map<String, dynamic>>> fetchRssFeed(String feedUrl) async {
    try {
      // 设置超时时间为10秒，避免长时间等待
      final client = http.Client();
      final response = await client
          .get(Uri.parse(feedUrl))
          .timeout(const Duration(seconds: 10), onTimeout: () {
        // 超时时关闭客户端并抛出异常
        client.close();
        throw TimeoutException('请求超时，无法连接到 $feedUrl');
      });

      // 请求完成后关闭客户端
      client.close();

      if (response.statusCode != 200) {
        throw Exception('Failed to load feed: ${response.statusCode}');
      }

      // 根据内容类型尝试不同的解析方法
      try {
        // 首先尝试解析为RSS
        print('Trying to parse as RSS feed...');
        final rssFeed = RssFeed.parse(response.body);
        return _convertRssItemsToArticles(rssFeed, feedUrl);
      } catch (rssError) {
        print('Error parsing as RSS: $rssError');
        // 如果RSS解析失败，尝试解析为Atom
        try {
          print('Trying to parse as Atom feed...');
          final atomFeed = AtomFeed.parse(response.body);
          return _convertAtomItemsToArticles(atomFeed, feedUrl);
        } catch (atomError) {
          print('Error parsing as Atom: $atomError');
          throw Exception('无法解析Feed内容');
        }
      }
    } catch (e) {
      print('Error fetching RSS feed: $e');
      throw e; // 向上传递异常，让调用者处理
    }
  }

  /// 将Atom项目转换为文章格式
  List<Map<String, dynamic>> _convertAtomItemsToArticles(
      AtomFeed feed, String sourceUrl) {
    final String sourceName = feed.title ?? 'Unknown Source';
    final String sourceId = sourceUrl.hashCode.toString();

    return feed.items.map((item) {
      // 提取图片URL
      String? imageUrl;

      // 从内容中提取图片
      if (item.content != null) {
        final String contentStr = item.content!;
        // 首先尝试查找featured-image类的div
        final featuredImageRegex = RegExp(
            r'<div[^>]*class="featured-image"[^>]*>.*?<img[^>]+src="([^">]+)"');
        final featuredMatch = featuredImageRegex.firstMatch(contentStr);
        if (featuredMatch != null && featuredMatch.group(1) != null) {
          imageUrl = featuredMatch.group(1);
        } else {
          // 如果没有featured-image，尝试查找任何图片
          final imgRegExp = RegExp(r'<img[^>]+src="([^">]+)"');
          final match = imgRegExp.firstMatch(contentStr);
          if (match != null && match.group(1) != null) {
            imageUrl = match.group(1);
          }
        }
      }

      // 使用默认图片，如果无法提取
      if (imageUrl == null || imageUrl.isEmpty) {
        // The Verge默认图片
        if (feedContainsVergeContent(sourceUrl)) {
          imageUrl =
              'https://cdn.vox-cdn.com/uploads/chorus_asset/file/24486268/220506_The_Verge_001.jpg';
        } else {
          // 其他源的通用默认图片
          imageUrl = 'https://via.placeholder.com/600x400?text=No+Image';
        }
      }

      // 清理内容（移除HTML标签）
      String cleanContent = '';
      if (item.content != null) {
        cleanContent = item.content!;
        cleanContent = cleanContent.replaceAll(RegExp(r'<[^>]*>'), '');
      } else if (item.summary != null) {
        cleanContent = item.summary!;
        cleanContent = cleanContent.replaceAll(RegExp(r'<[^>]*>'), '');
      }

      // 计算发布时间距离现在的时间
      String publishTime = '未知时间';
      if (item.published != null) {
        try {
          final DateTime publishDate = DateTime.parse(item.published!);
          final Duration difference = DateTime.now().difference(publishDate);

          if (difference.inDays > 30) {
            final int months = (difference.inDays / 30).floor();
            publishTime = '$months个月前';
          } else if (difference.inDays > 0) {
            publishTime = '${difference.inDays}天前';
          } else if (difference.inHours > 0) {
            publishTime = '${difference.inHours}小时前';
          } else if (difference.inMinutes > 0) {
            publishTime = '${difference.inMinutes}分钟前';
          } else {
            publishTime = '刚刚';
          }
        } catch (e) {
          print('Error parsing date: ${item.published}');
          publishTime = '未知时间';
        }
      }

      // 生成安全的sourceId（确保是整数）
      int safeSourceId;
      try {
        safeSourceId = int.parse(sourceId.substring(0, 1));
      } catch (e) {
        safeSourceId = sourceUrl.hashCode % 5;
      }

      // 获取文章链接
      String articleUrl = '';
      if (item.links.isNotEmpty) {
        articleUrl = item.links.first.href ?? '';
      }

      return {
        'title': item.title ?? 'No Title',
        'publishDate': publishTime,
        'source': sourceName,
        'sourceId': safeSourceId,
        'content': cleanContent,
        'url': articleUrl,
        'imageUrl': imageUrl,
        'isBookmarked': false,
        'guid': item.id ?? articleUrl,
        'author': item.authors.isNotEmpty ? item.authors.first.name : null,
        'categories': item.categories != null
            ? item.categories.map((c) => c.term).toList()
            : []
      };
    }).toList();
  }

  /// 将RSS项目转换为文章格式
  List<Map<String, dynamic>> _convertRssItemsToArticles(
      RssFeed feed, String sourceUrl) {
    final String sourceName = feed.title ?? 'Unknown Source';
    final String sourceId = sourceUrl.hashCode.toString();

    return feed.items.map((item) {
      // 提取图片URL
      String? imageUrl;

      // The Verge特殊处理，尝试从item.content中提取图片
      if (feedContainsVergeContent(sourceUrl) && item.content != null) {
        // 首先尝试查找featured-image类的div
        final featuredImageRegex = RegExp(
            r'<div[^>]*class="featured-image"[^>]*>.*?<img[^>]+src="([^">]+)"');
        final featuredMatch =
            featuredImageRegex.firstMatch(item.content!.value ?? '');
        if (featuredMatch != null && featuredMatch.group(1) != null) {
          imageUrl = featuredMatch.group(1);
        } else {
          // 如果没有featured-image，尝试查找任何图片
          final imgRegExp = RegExp(r'<img[^>]+src="([^">]+)"');
          final match = imgRegExp.firstMatch(item.content!.value ?? '');
          if (match != null && match.group(1) != null) {
            imageUrl = match.group(1);
          }
        }
      }

      // 尝试从媒体内容中获取图片
      if (imageUrl == null &&
          item.media != null &&
          item.media!.contents.isNotEmpty) {
        final mediaContent = item.media!.contents.first;
        imageUrl = mediaContent.url;
      }

      // 如果没有找到媒体内容，尝试从enclosure中获取
      if (imageUrl == null && item.enclosure != null) {
        final enclosure = item.enclosure!;
        if (enclosure.type != null && enclosure.type!.startsWith('image/')) {
          imageUrl = enclosure.url;
        }
      }

      // 使用默认图片，如果无法提取
      if (imageUrl == null || imageUrl.isEmpty) {
        // The Verge默认图片
        if (feedContainsVergeContent(sourceUrl)) {
          imageUrl =
              'https://cdn.vox-cdn.com/uploads/chorus_asset/file/24486268/220506_The_Verge_001.jpg';
        } else {
          // 其他源的通用默认图片
          imageUrl = 'https://via.placeholder.com/600x400?text=No+Image';
        }
      }

      // 清理内容（移除HTML标签）
      String cleanContent = '';
      if (item.content != null) {
        cleanContent = item.content!.value ?? '';
        cleanContent = cleanContent.replaceAll(RegExp(r'<[^>]*>'), '');
      } else if (item.description != null) {
        cleanContent = item.description ?? '';
        cleanContent = cleanContent.replaceAll(RegExp(r'<[^>]*>'), '');
      }

      // 计算发布时间距离现在的时间
      String publishTime = '未知时间';
      if (item.pubDate != null) {
        try {
          final DateTime publishDate = DateTime.parse(item.pubDate!);
          final Duration difference = DateTime.now().difference(publishDate);

          if (difference.inDays > 30) {
            final int months = (difference.inDays / 30).floor();
            publishTime = '$months个月前';
          } else if (difference.inDays > 0) {
            publishTime = '${difference.inDays}天前';
          } else if (difference.inHours > 0) {
            publishTime = '${difference.inHours}小时前';
          } else if (difference.inMinutes > 0) {
            publishTime = '${difference.inMinutes}分钟前';
          } else {
            publishTime = '刚刚';
          }
        } catch (e) {
          print('Error parsing date: ${item.pubDate}');
          publishTime = '未知时间';
        }
      }

      // 生成安全的sourceId（确保是整数）
      int safeSourceId;
      try {
        safeSourceId = int.parse(sourceId.substring(0, 1));
      } catch (e) {
        safeSourceId = sourceUrl.hashCode % 5;
      }

      return {
        'title': item.title ?? 'No Title',
        'publishDate': publishTime,
        'source': sourceName,
        'sourceId': safeSourceId,
        'content': cleanContent,
        'url': item.link ?? '',
        'imageUrl': imageUrl,
        'isBookmarked': false,
        'guid': item.guid ?? item.link ?? '${item.title}-${item.pubDate}',
        'author': item.author,
        'categories': item.categories != null
            ? item.categories!.map((c) => c.value).toList()
            : []
      };
    }).toList();
  }

  /// 检查是否为The Verge的RSS源
  bool feedContainsVergeContent(String url) {
    return url.contains('theverge.com');
  }
}
