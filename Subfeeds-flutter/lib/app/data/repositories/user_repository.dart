import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/data/models/api_response.dart';
import 'package:subfeeds/app/data/models/user_model.dart';
import 'package:subfeeds/app/data/services/http_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

/// 用户仓库类，负责处理用户相关的API请求
class UserRepository {
  final HttpService _httpService = HttpService();

  /// 登录
  Future<ApiResponse<UserModel>> login(
      String account, String password, int type) async {
    final user = UserModel(account: account);
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/login',
      body: user.toLoginRequest(password: password, type: type),
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: UserModel.fromJson(response.data!),
        token: response.token,
        msg: '登录成功',
        code: 200,
      );
    }

    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 第三方登录
  Future<ApiResponse<UserModel>> authLogin(UserModel user) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/authLoginRegister',
      body: user.toAuthLoginRequest(),
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: UserModel.fromJson(response.data!),
        token: response.token,
        msg: '登录成功',
        code: 200,
      );
    }

    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 注册
  Future<ApiResponse<UserModel>> register(
    String account,
    String password,
    String verifyCode,
  ) async {
    final user = UserModel(account: account);
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/register',
      body: user.toRegisterRequest(
        password: password,
        verifyCode: verifyCode,
      ),
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        msg: '注册成功',
        code: 200,
      );
    }

    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 重置密码
  Future<ApiResponse<bool>> resetPassword(
    String account,
    String password,
    String verifyCode,
  ) async {
    final user = UserModel(account: account);
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/forgetPwd',
      body: user.toResetPasswordRequest(
        password: password,
        verifyCode: verifyCode,
      ),
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess) {
      return ApiResponse.success(
        data: true,
        msg: response.msg ?? '重置密码成功',
        code: response.code,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 发送验证码
  Future<ApiResponse<bool>> sendVerifyCode(String account, int type) async {
    final user = UserModel(account: account);
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/sendVerifyCode',
      body: user.toSendVerifyCodeRequest(type: type),
      fromJson: (json) => json as Map<String, dynamic>,
    );

    // 直接根据 HttpService 返回的 response.isSuccess 来判断
    if (response.code == 200) {
      return ApiResponse.success(
        data: true,
        msg: response.msg ?? '发送验证码成功',
        code: response.code,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 获取用户信息
  Future<ApiResponse<UserModel>> getUserInfo() async {
    try {
      final response = await _httpService.get('/api/v1/getUserInfo');

      if (response.isSuccess && response.data != null) {
        final userData = response.data as Map<String, dynamic>;

        final userModel = UserModel(
          id: userData['id']?.toString(),
          name: userData['name']?.toString(),
          email: userData['email']?.toString(),
          phone: userData['phone']?.toString(),
          avatar: userData['avatar']?.toString(),
          createTime:
              userData['createTime'] is int ? userData['createTime'] : null,
          updateTime:
              userData['updateTime'] is int ? userData['updateTime'] : null,
          lastOnlineTime: userData['lastOnlineTime'] is int
              ? userData['lastOnlineTime']
              : null,
          createIp: userData['createIp']?.toString(),
          ipAddress: userData['ipAddress']?.toString(),
          enable: userData['enable'] is int ? userData['enable'] : null,
          googleId: userData['googleId']?.toString(),
          wechatId: userData['wechatId']?.toString(),
          appleId: userData['appleId']?.toString(),
          motto: userData['motto']?.toString(),
          roleId: userData['roleId']?.toString(),
          password: userData['password']?.toString(),
        );
        await saveUserInfo(userModel);
        return ApiResponse<UserModel>.success(
            data: userModel, code: 200, msg: '获取成功');
      }

      return ApiResponse<UserModel>.failure(
        code: response.code,
        msg: response.msg ?? '获取用户信息失败',
      );
    } catch (e, stackTrace) {
      print('getUserInfo error: $e');
      print('Stack trace: $stackTrace');
      return ApiResponse<UserModel>.failure(
        code: 500,
        msg: e.toString(),
      );
    }
  }

  /// 退出登录
  Future<ApiResponse<bool>> logout() async {
    final response = await _httpService.post<bool>(
      '/api/v1/user/logout',
      fromJson: (json) => json as bool,
    );

    if (response.isSuccess) {
      await _clearUserInfo();
      await _httpService.clearToken();
    }

    return response;
  }

  /// 保存用户信息到本地存储
  Future<void> saveUserInfo(UserModel user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_info', json.encode(user.toJson()));
      print('用户信息: ${user.toJson()}');
    } catch (e) {
      print('保存用户信息到本地错误: $e');
    }
  }

  /// 从本地存储获取用户信息r
  Future<UserModel?> getUserInfoFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('user_info');
      if (userJson != null) {
        return UserModel.fromJson(json.decode(userJson));
      }
    } catch (e) {
      print('从本地获取用户信息错误: $e');
    }
    return null;
  }

  /// 清除本地存储的用户信息
  Future<void> _clearUserInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_info');
    } catch (e) {
      print('清除用户信息失败: $e');
    }
  }

  /// 更新用户资料
  Future<ApiResponse<UserModel>> updateUserProfile(UserModel user) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/user/updateUserInfo',
      body: {
        'name': user.name,
        'avatar': user.avatar,
      },
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(code: 200, msg: '更新成功');
    }

    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 修改密码
  Future<ApiResponse<bool>> changePassword(
      String oldPassword, String newPassword) async {
    final response = await _httpService.post<bool>(
      '/api/v1/user/updatePwd',
      body: {
        'oldPwd': oldPassword,
        'newPwd': newPassword,
      },
      fromJson: (json) => json as bool,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(code: 200, msg: '修改成功');
    }

    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 上传头像
  Future<ApiResponse<void>> uploadAvatar(String filePath) async {
    try {
      print('文件路径${filePath}');

      // 检查文件格式
      final ext = filePath.split('.').last.toLowerCase();
      if (!['jpg', 'jpeg', 'png'].contains(ext)) {
        return ApiResponse.failure(
          code: 400,
          msg: '只支持 JPG 和 PNG 格式的图片',
        );
      }

      // 检查文件大小
      final file = File(filePath);
      final fileSize = await file.length();
      final maxSize = 4 * 1024 * 1024; // 4MB
      print('文件大小${fileSize / 1024 / 1024}MB');
      if (fileSize > maxSize) {
        return ApiResponse.failure(
          code: 400,
          msg: '图片大小不能超过 4MB',
        );
      }

      print('请求基本路径${_httpService.baseUrl}');
      final uri = Uri.parse('${_httpService.baseUrl}/api/v1/user/uploadAvatar');

      // 创建multipart请求
      final request = http.MultipartRequest('POST', uri);

      // 设置请求头
      final token = await _httpService.getToken();
      request.headers['Authorization'] = 'Bearer $token';

      // 添加文件
      final stream = http.ByteStream(file.openRead());
      final length = await file.length();

      // 创建multipart文件
      final multipartFile = await http.MultipartFile.fromPath('file', filePath,
          contentType: MediaType.parse(_getContentType(filePath)));

      // 添加文件到请求中
      request.files.add(multipartFile);

      // 打印请求信息
      print('上传头像请求头 ${request.headers}');
      print(
          '上传头像文件信息: ${multipartFile.filename}, ${multipartFile.contentType}, ${multipartFile.length}');

      // 发送请求
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('上传头像响应状态码 ${response.statusCode}');
      print('上传头像响应内容 ${response.body}');

      if (response.statusCode == 200) {
        return ApiResponse.success(
          code: 200,
          msg: '上传成功',
        );
      }

      final responseData = json.decode(response.body);
      return ApiResponse.failure(
        code: responseData['code'] ?? response.statusCode,
        msg: responseData['msg'] ?? '上传失败',
      );
    } catch (e, stackTrace) {
      print('上传头像失败: $e');
      print('堆栈信息: $stackTrace');
      return ApiResponse.failure(
        code: 500,
        msg: e.toString(),
      );
    }
  }

  /// 根据文件扩展名获取Content-Type字符串
  String _getContentType(String filePath) {
    final ext = filePath.split('.').last.toLowerCase();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      default:
        return 'application/octet-stream';
    }
  }

  // 注销账号
  Future<ApiResponse<void>> deleteAccount() async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/user/unsubscribeAccount',
      fromJson: (json) => json as Map<String, dynamic>,
    );
    if (response.isSuccess) {
      return ApiResponse.success(
          code: response.code, msg: response.msg ?? '注销成功');
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }
}
