import 'package:flutter/material.dart';
import 'package:subfeeds/app/data/models/api_response.dart';
import 'package:subfeeds/app/data/services/http_service.dart';

/// 文章仓库类，负责处理文章相关的API请求
class ArticleRepository {
  final HttpService _httpService = HttpService();

  /// 获取用户的未分组的订阅列表
  Future<ApiResponse<Map<String, dynamic>>> getMyRssSubList(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/feeds/myRssSubList?category=&isAsset=0&orderByColumn=createTime&isAsc=descending&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}&search=${params['search']}',
      fromJson: (json) {
        if (json == null) return {'pageList': [], 'total': 0};
        final data = json['data'];
        if (data == null) return {'pageList': [], 'total': 0};
        return data as Map<String, dynamic>;
      },
    );

    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {'pageList': [], 'total': 0},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取feeds全部feeds分类
  Future<ApiResponse<List<Map<String, dynamic>>>> getFeedsCategory() async {
    final response = await _httpService.get<List<Map<String, dynamic>>>(
      '/api/v1/feedsCategory',
      fromJson: (json) {
        if (json == null) return [];
        final data = json['data'];
        if (data == null) return [];
        return (data as List).map((e) => e as Map<String, dynamic>).toList();
      },
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? [],
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取推荐源列表
  Future<ApiResponse<Map<String, dynamic>>> getRecommendFeeds() async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/recommendFeeds?pageNum=1&pageSize=20',
      fromJson: (json) {
        if (json == null) return {'pageList': [], 'total': 0};
        final data = json['data'];
        if (data == null) return {'pageList': [], 'total': 0};
        return data as Map<String, dynamic>;
      },
    );

    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {'pageList': [], 'total': 0},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 订阅指定RSS /api/v1/subOtherFeeds
  /*
   * @param {Map<String, dynamic>} params
   * @returns {ApiResponse<Map<String, dynamic>>}
   * params:
   * {
   *  "searchValue": "2424", // 需要订阅的文章id
   *  "type": 1, // 订阅类型类型 根据搜索到的feeds列表放回的type类型传递
   * }
   */
  Future<ApiResponse<Map<String, dynamic>>> subscribeRss(
      Map<String, dynamic> params) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/subOtherFeeds',
      body: params,
    );

    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '订阅成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

// 删除指定rss /api/v1/feeds/deleteRssSub?arrayId=5284404411582809088
  Future<ApiResponse<Map<String, dynamic>>> deleteRss(
      List<int> articleIds) async {
    final response = await _httpService.delete<Map<String, dynamic>>(
      '/api/v1/feeds/deleteRssSub?arrayId=${articleIds.join(',')}',
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '取消订阅成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 从分组将feeds移除deleteGroupFeeds?arrayId=88
  Future<ApiResponse<Map<String, dynamic>>> deleteGroupFeeds(
      Map<String, dynamic> params) async {
    final response = await _httpService.delete<Map<String, dynamic>>(
      '/api/v1/feeds/deleteUserFeedsGroup?arrayId=${params['arrayId']}',
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '移除成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 搜索rss源searchFeeds?search=deep&pageNum=2&pageSize=15
  Future<ApiResponse<Map<String, dynamic>>> searchFeeds(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/searchFeeds?search=${params['search']}&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}',
      fromJson: (json) {
        if (json == null) return {'pageList': [], 'total': 0};
        final data = json['data'];
        if (data == null) return {'pageList': [], 'total': 0};
        return data as Map<String, dynamic>;
      },
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {'pageList': [], 'total': 0},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 搜索rss对应分类下的rss源
  Future<ApiResponse<Map<String, dynamic>>> searchCategoryFeeds(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/searchFeeds?search=${params['search']}&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}&categoryList=${params['categoryList']}',
      fromJson: (json) {
        if (json == null) return {'pageList': [], 'total': 0};
        final data = json['data'];
        if (data == null) return {'pageList': [], 'total': 0};
        return data as Map<String, dynamic>;
      },
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {'pageList': [], 'total': 0},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // Google News 搜索
  Future<ApiResponse<Map<String, dynamic>>> searchGoogleNews(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/searchGoogle?search=${params['search']}&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}',
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // Telegram 搜索
  Future<ApiResponse<Map<String, dynamic>>> searchTelegram(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/searchTelegram?search=${params['search']}&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}',
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 创建文件夹
  // 接受参数
  /*
   * {
   *  "groupName": "string", // 文件夹名称
   *  "feedsList": [1,2,3] // 需要移动到此文件夹的订阅的rss源id列表
   * }
   */
  Future<ApiResponse<Map<String, dynamic>>> addFeedsGroup(
      Map<String, dynamic> params) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/feeds/addFeedsGroup',
      body: params,
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '创建成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取文件夹列表
  // /api/v1/feeds/getFeedsGroupList
  Future<ApiResponse<Map<String, dynamic>>> getFeedsGroupList() async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/feeds/myRssGroupList',
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取指定文件夹下的feeds列表
  // /api/v1/feeds/getGroupFeeds/{folderId}
  Future<ApiResponse<Map<String, dynamic>>> getGroupFeeds(
      String folderId) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/feeds/getGroupFeeds/$folderId',
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 移动feeds到指定文件夹/api/v1/feeds/addUserFeedsGroup
  Future<ApiResponse<Map<String, dynamic>>> addUserFeedsGroup(
      Map<String, dynamic> params) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/feeds/addUserFeedsGroup',
      body: params,
    );

    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '移动成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 修改feeds信息
  // /api/v1/feeds/updateRssSub
  Future<ApiResponse<Map<String, dynamic>>> updateRssSub(
      Map<String, dynamic> params) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/feeds/updateRssSub',
      body: params,
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '修改成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 修改文件夹信息/api/v1/feeds/updateFeedsGroup
  Future<ApiResponse<Map<String, dynamic>>> updateFeedsGroup(
      Map<String, dynamic> params) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/feeds/updateFeedsGroup',
      body: params,
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '修改成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 将对应feeds下的所有文章标记为已读
  // /api/v1/feeds/updateUserFeedsIsReadStatus
  // 接受参数
  /*
   * {
   *   "arrayIds": [1,2,3], // 需要标记为已读的feeds源id
   * }
   */
  Future<ApiResponse<Map<String, dynamic>>> updateUserFeedsIsReadStatus(
      Map<String, dynamic> params) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/feeds/updateUserFeedsIsReadStatus',
      body: params,
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '标记成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取用户计数数据
  Future<ApiResponse<Map<String, dynamic>>> getUserCount() async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/article/getUserCount',
      fromJson: (json) {
        if (json == null) return {};
        final data = json['data'];
        if (data == null) return {};
        return data as Map<String, dynamic>;
      },
    );

    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // YouTube 搜索
  Future<ApiResponse<Map<String, dynamic>>> searchYoutube(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/searchYoutube?search=${params['search']}&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}',
      fromJson: (json) {
        if (json == null) return {'pageList': [], 'total': 0};
        final data = json['data'];
        if (data == null) return {'pageList': [], 'total': 0};
        return data as Map<String, dynamic>;
      },
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {'pageList': [], 'total': 0},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // Reddit 搜索
  Future<ApiResponse<Map<String, dynamic>>> searchReddit(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/searchReddit?search=${params['search']}&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}',
      fromJson: (json) {
        if (json == null) return {'pageList': [], 'total': 0};
        final data = json['data'];
        if (data == null) return {'pageList': [], 'total': 0};
        return data as Map<String, dynamic>;
      },
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {'pageList': [], 'total': 0},
        msg: '获取成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }
}
