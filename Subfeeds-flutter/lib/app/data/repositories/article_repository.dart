import 'package:flutter/material.dart';
import 'package:subfeeds/app/data/models/api_response.dart';
import 'package:subfeeds/app/data/services/http_service.dart';
import 'package:get/get.dart';

/// 文章仓库类，负责处理文章相关的API请求
class ArticleRepository extends GetxService {
  final HttpService _httpService = HttpService();
  Future<ApiResponse<Map<String, dynamic>>> subscribeRss(
      Map<String, dynamic> params) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/subOtherFeeds',
      body: params,
    );
    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '订阅成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

// 删除指定rss /api/v1/feeds/deleteRssSub?arrayId=5284404411582809088
  Future<ApiResponse<Map<String, dynamic>>> deleteRss(
      List<int> articleIds) async {
    final response = await _httpService.delete<Map<String, dynamic>>(
      '/api/v1/feeds/deleteRssSub?arrayId=${articleIds.join(',')}',
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '取消订阅成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 将文章插入为历史记录（同时也会设置文章为已读） 需要传递suffixTable: 文章分表id
  Future<ApiResponse<Map<String, dynamic>>> insertHistory(
      int articleId, int feedsId, String suffixTable) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/article/insertReadRecord',
      body: {
        'articleId': articleId,
        'feedsId': feedsId,
        'suffixTable': suffixTable
      },
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '插入成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 将文章设置为稍后阅读 需要传递suffixTable: 文章分表id feedsId
  Future<ApiResponse<Map<String, dynamic>>> setReadLater(
      int articleId, int feedsId, String suffixTable) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/article/insertLaterRead',
      body: {
        'articleId': articleId,
        'feedsId': feedsId,
        'suffixTable': suffixTable,
      },
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '设置稍后阅读成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 删除稍后阅读
  Future<ApiResponse<Map<String, dynamic>>> deleteLaterRead(
      List<int> articleIds) async {
    final response = await _httpService.delete<Map<String, dynamic>>(
      '/api/v1/article/deleteLaterRead?arrayId=${articleIds.join(',')}',
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '取消稍后阅读成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 收藏文章 需要传递suffixTable: 文章分表id //feedsId 必填 为feeds列表的feedsid字段 而不是id字段
  Future<ApiResponse<Map<String, dynamic>>> insertCollect(
      int articleId, int feedsId, String suffixTable) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/article/insertCollect',
      body: {
        'articleId': articleId,
        'feedsId': feedsId,
        'suffixTable': suffixTable,
      },
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '收藏成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 删除收藏
  Future<ApiResponse<Map<String, dynamic>>> deleteCollect(
      List<int> articleIds) async {
    final response = await _httpService.delete<Map<String, dynamic>>(
      '/api/v1/article/deleteCollect?arrayId=${articleIds.join(',')}',
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '删除收藏成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取稍后阅读文章列表
  Future<ApiResponse<Map<String, dynamic>>> getLaterReadList(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/article/getLaterReadList?orderByColumn=createTime&isAsc=descending&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}&${params['feedsName'] != null ? 'feedsName=${params['feedsName']}' : ''}',
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '获取稍后阅读文章列表成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取历史记录列表
  Future<ApiResponse<Map<String, dynamic>>> getReadRecordList(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/article/readRecordList?orderByColumn=createTime&isAsc=descending&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}&${params['feedsName'] != null ? 'feedsName=${params['feedsName']}' : ''}',
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '获取历史记录列表成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取收藏文章列表
  Future<ApiResponse<Map<String, dynamic>>> getCollectList(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/article/collectList?orderByColumn=createTime&isAsc=descending&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}&${params['feedsName'] != null ? 'feedsName=${params['feedsName']}' : ''}',
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '获取收藏文章列表成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取笔记列表
  Future<ApiResponse<Map<String, dynamic>>> getUserNoteList(
      Map<String, dynamic> params) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/article/userNoteList?orderByColumn=createTime&isAsc=descending&pageNum=${params['pageNum']}&pageSize=${params['pageSize']}',
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '获取笔记列表成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取某个feeds下的文章分页列表 支持已读未读
  // statusFilter 0: 展示所有 1: 未读
  // feedsId 必填 为feeds列表的feedsid字段 而不是id字段
  // lastWeek 可选 不传递表示 只展示最近一周的文章 1表示上周的文章 2表示前二周的文章 3表示前三周的文章
  Future<ApiResponse<Map<String, dynamic>>> getRssArticle(int feedsId,
      int statusFilter, int lastWeek, int pageSize, int pageNum) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/feeds/getRssArticle/$feedsId?statusFilter=$statusFilter&lastWeek=$lastWeek&pageSize=$pageSize&pageNum=$pageNum',
      fromJson: (json) => json as Map<String, dynamic>,
    );
    debugPrint('123123asd');
    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '获取文章列表成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> getRssArticleForRec(
      int feedsId, int pageSize, int pageNum) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/feeds/getRssArticle/$feedsId?pageSize=$pageSize&pageNum=$pageNum',
      fromJson: (json) => json as Map<String, dynamic>,
    );
    debugPrint('123123asd');
    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '获取文章列表成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> getRssNewArticle(
      int pageSize, int pageNum, int statusFilter) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/feeds/getNewList?pageSize=$pageSize&pageNum=$pageNum&statusFilter=$statusFilter',
      fromJson: (json) => json as Map<String, dynamic>,
    );
    debugPrint('123123asd');
    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '获取文章列表成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 将订阅源的所有文章标记为已读
  Future<ApiResponse<Map<String, dynamic>>> updateUserFeedsIsReadStatus(
      List<int> feedIds) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/feeds/updateUserFeedsIsReadStatus',
      body: {'arrayIds': feedIds},
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess) {
      return ApiResponse.success(
        data: response.data ?? {},
        msg: '标记成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  /// 管理用户笔记（新增、修改、删除）
  Future<ApiResponse<Map<String, dynamic>>> manageUserNote({
    required String articleId,
    required String content,
    required String feedsId,
    required List<Map<String, dynamic>> noteList,
    String? suffixTable,
  }) async {
    final response = await _httpService.post<Map<String, dynamic>>(
      '/api/v1/article/manageUserNote',
      body: {
        'articleId': articleId,
        'content': content,
        'feedsId': feedsId,
        'noteList': noteList,
        'suffixTable': suffixTable ?? null,
      },
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '笔记操作成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }

  // 获取某个feeds下的全部文章，不区分时间段
  Future<ApiResponse<Map<String, dynamic>>> getAllArticle(
      int feedsId, int pageSize, int pageNum) async {
    final response = await _httpService.get<Map<String, dynamic>>(
      '/api/v1/feeds/getAllArticle/$feedsId?pageSize=$pageSize&pageNum=$pageNum',
      fromJson: (json) => json as Map<String, dynamic>,
    );

    if (response.isSuccess && response.data != null) {
      return ApiResponse.success(
        data: response.data,
        msg: '获取全部文章列表成功',
        code: 200,
      );
    }
    return ApiResponse.failure(
      code: response.code,
      msg: response.msg,
    );
  }
}
