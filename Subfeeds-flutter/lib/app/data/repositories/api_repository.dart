import 'dart:async';
import 'package:subfeeds/app/data/models/api_response.dart';
import 'package:subfeeds/app/data/services/http_service.dart';
import 'package:flutter/foundation.dart';

/// API仓库，负责处理与后端API的交互
class ApiRepository {
  final HttpService _httpService = HttpService();

  // 是否已初始化
  bool _isInitialized = false;

  // 是否正在初始化
  bool _isInitializing = false;

  // 初始化超时时间
  static const Duration _initTimeout = Duration(seconds: 3);

  /// 初始化API仓库
  Future<void> init(String baseUrl) async {
    // 如果已初始化或正在初始化，则直接返回
    if (_isInitialized || _isInitializing) return;

    _isInitializing = true;

    try {
      // 使用带超时的Future.any，确保即使网络请求卡住也能继续
      await Future.any([
        _httpService.init(
          baseUrl: baseUrl,
          autoRefreshToken: true,
        ),
        Future.delayed(_initTimeout, () {
          debugPrint('HTTP服务初始化超时，将继续运行应用');
          throw TimeoutException('HTTP服务初始化超时');
        }),
      ]);

      _isInitialized = true;
      debugPrint('API仓库初始化完成');
    } catch (e) {
      debugPrint('API仓库初始化失败: $e');
      // 不重新抛出异常，允许应用继续运行
      _isInitialized = true; // 标记为已初始化，避免重复尝试
    } finally {
      _isInitializing = false;
    }
  }

  /// 设置token
  void setToken(String token) {
    try {
      _httpService.setToken(token);
    } catch (e) {
      debugPrint('设置token失败: $e');
    }
  }

  /// 清除token
  Future<void> clearToken() async {
    try {
      await _httpService.clearToken();
    } catch (e) {
      debugPrint('清除token失败: $e');
    }
  }

  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;
}
