import 'package:subfeeds/app/data/providers/rss_provider.dart';
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

/// RSS仓库，负责管理RSS订阅源
class RssRepository {
  final RssProvider _rssProvider = RssProvider();

  /// 获取默认RSS源
  Future<List<Map<String, dynamic>>> fetchDefaultSource() async {
    try {
      // 使用多个备选RSS源，避免单一源的连接问题
      List<String> rssSources = [
        'https://www.propublica.org/feeds/propublica/main' // The Verge (作为备选)
      ];
      // 如果是Web平台，使用CORS代理
      if (kIsWeb) {
        print('检测到Web平台，将使用CORS代理');

        // 在Web平台上，直接使用模拟数据，避免CORS问题
        print('在Web平台上使用模拟数据，避免CORS问题');
        return _getMockData();
      }
      // 尝试依次获取各个源的数据
      for (final source in rssSources) {
        try {
          final result = await _rssProvider.fetchRssFeed(source);
          // 如果获取到数据，则返回
          if (result.isNotEmpty) {
            return result;
          }
        } catch (sourceError) {
          print('获取RSS源失败: $source, 错误: $sourceError');
          // 继续尝试下一个源
          continue;
        }
      }

      // 如果所有源都失败，返回模拟数据
      print('所有RSS源获取失败，使用模拟数据');
      return _getMockData();
    } catch (e) {
      print('Error fetching RSS feed: $e');
      // 出错时返回模拟数据
      return _getMockData();
    }
  }

  /// 获取指定URL的RSS源
  Future<List<Map<String, dynamic>>> fetchRssFeed(String url) async {
    try {
      // 如果是Web平台，使用模拟数据
      if (kIsWeb) {
        print('在Web平台上使用模拟数据，避免CORS问题');
        return _getMockData();
      }

      final result = await _rssProvider.fetchRssFeed(url);

      // 如果获取到数据，则返回
      if (result.isNotEmpty) {
        return result;
      }

      // 如果没有获取到数据，返回模拟数据
      return _getMockData();
    } catch (e) {
      print('Error fetching RSS feed: $e');
      // 出错时返回模拟数据
      return _getMockData();
    }
  }

  /// 获取模拟数据
  List<Map<String, dynamic>> _getMockData() {
    return [
      {
        'title': '模拟文章1：Flutter新版本发布，带来性能提升和新特性',
        'publishDate': '2小时前',
        'source': 'Flutter News',
        'sourceId': 0,
        'content':
            'Flutter团队发布了最新版本，带来了显著的性能提升和多项新特性。此次更新优化了渲染引擎，减少了内存占用，并添加了更多的Material You设计组件。',
        'url': 'https://example.com/flutter-news',
        'imageUrl': 'https://via.placeholder.com/600x400?text=Flutter+News',
        'isBookmarked': false,
        'guid': 'mock-article-1',
        'author': 'Flutter Team',
        'categories': ['Flutter', 'Mobile Development']
      },
      {
        'title': '模拟文章2：Dart 3.0推出空安全特性，开发者反响热烈',
        'publishDate': '1天前',
        'source': 'Dart Weekly',
        'sourceId': 1,
        'content':
            'Dart 3.0正式发布，带来了期待已久的空安全特性。这一更新将帮助开发者在编译时捕获空引用错误，大幅提高代码质量和应用稳定性。',
        'url': 'https://example.com/dart-weekly',
        'imageUrl': 'https://via.placeholder.com/600x400?text=Dart+Weekly',
        'isBookmarked': false,
        'guid': 'mock-article-2',
        'author': 'Dart Team',
        'categories': ['Dart', 'Programming']
      },
      {
        'title': '模拟文章3：GetX状态管理库获得重大更新',
        'publishDate': '3天前',
        'source': 'Flutter Community',
        'sourceId': 2,
        'content':
            'GetX状态管理库发布了新版本，带来了更简洁的API和更高的性能。新版本简化了路由管理，并提供了更好的依赖注入解决方案。',
        'url': 'https://example.com/flutter-community',
        'imageUrl':
            'https://via.placeholder.com/600x400?text=Flutter+Community',
        'isBookmarked': false,
        'guid': 'mock-article-3',
        'author': 'GetX Team',
        'categories': ['Flutter', 'State Management']
      },
      {
        'title': '模拟文章4：Flutter Web获得重大性能提升',
        'publishDate': '1周前',
        'source': 'Web Dev News',
        'sourceId': 3,
        'content':
            'Flutter Web平台获得了重大性能提升，新的渲染引擎大幅减少了首次加载时间和交互延迟。这使得Flutter Web成为构建高性能Web应用的更具吸引力的选择。',
        'url': 'https://example.com/web-dev-news',
        'imageUrl': 'https://via.placeholder.com/600x400?text=Web+Dev+News',
        'isBookmarked': false,
        'guid': 'mock-article-4',
        'author': 'Web Dev Team',
        'categories': ['Flutter', 'Web Development']
      },
      {
        'title': '模拟文章5：Flutter桌面应用开发指南',
        'publishDate': '2周前',
        'source': 'Desktop Dev',
        'sourceId': 4,
        'content':
            '本指南详细介绍了如何使用Flutter构建高质量的桌面应用程序。从环境设置到发布打包，全面覆盖了Flutter桌面应用开发的各个方面。',
        'url': 'https://example.com/desktop-dev',
        'imageUrl': 'https://via.placeholder.com/600x400?text=Desktop+Dev',
        'isBookmarked': false,
        'guid': 'mock-article-5',
        'author': 'Desktop Dev Team',
        'categories': ['Flutter', 'Desktop Development']
      },
      {
        'title': '模拟文章6：Flutter与React Native性能对比',
        'publishDate': '3周前',
        'source': 'Mobile Comparison',
        'sourceId': 0,
        'content':
            '最新的性能测试显示，Flutter在渲染复杂UI和处理大量数据方面优于React Native。测试涵盖了启动时间、内存使用和动画流畅度等多个方面。',
        'url': 'https://example.com/mobile-comparison',
        'imageUrl':
            'https://via.placeholder.com/600x400?text=Mobile+Comparison',
        'isBookmarked': false,
        'guid': 'mock-article-6',
        'author': 'Mobile Dev Team',
        'categories': ['Flutter', 'React Native', 'Performance']
      },
      {
        'title': '模拟文章7：使用GetX实现高效状态管理',
        'publishDate': '1个月前',
        'source': 'Flutter Tips',
        'sourceId': 1,
        'content': '本文详细介绍了如何使用GetX库实现高效的状态管理。从基本概念到高级用法，帮助开发者充分利用GetX提高开发效率。',
        'url': 'https://example.com/flutter-tips',
        'imageUrl': 'https://via.placeholder.com/600x400?text=Flutter+Tips',
        'isBookmarked': false,
        'guid': 'mock-article-7',
        'author': 'Flutter Tips Team',
        'categories': ['Flutter', 'GetX', 'State Management']
      },
      {
        'title': '模拟文章8：Flutter 3.0新特性详解',
        'publishDate': '1个月前',
        'source': 'Flutter Insights',
        'sourceId': 2,
        'content':
            'Flutter 3.0带来了众多新特性，包括改进的Material You支持、更好的国际化工具和全新的渲染引擎。本文详细解析了这些新特性及其应用场景。',
        'url': 'https://example.com/flutter-insights',
        'imageUrl': 'https://via.placeholder.com/600x400?text=Flutter+Insights',
        'isBookmarked': false,
        'guid': 'mock-article-8',
        'author': 'Flutter Insights Team',
        'categories': ['Flutter', 'New Features']
      },
      {
        'title': '模拟文章9：Flutter应用性能优化指南',
        'publishDate': '2个月前',
        'source': 'Performance Tips',
        'sourceId': 3,
        'content':
            '本指南提供了一系列实用技巧，帮助开发者优化Flutter应用性能。从减少重建到优化图片加载，全面提升应用响应速度和用户体验。',
        'url': 'https://example.com/performance-tips',
        'imageUrl': 'https://via.placeholder.com/600x400?text=Performance+Tips',
        'isBookmarked': false,
        'guid': 'mock-article-9',
        'author': 'Performance Team',
        'categories': ['Flutter', 'Performance', 'Optimization']
      },
      {
        'title': '模拟文章10：Flutter与原生开发的集成方案',
        'publishDate': '3个月前',
        'source': 'Integration Guide',
        'sourceId': 4,
        'content':
            '本文详细介绍了Flutter与iOS/Android原生代码的集成方案。从平台通道到插件开发，帮助开发者充分利用原生功能增强Flutter应用。',
        'url': 'https://example.com/integration-guide',
        'imageUrl':
            'https://via.placeholder.com/600x400?text=Integration+Guide',
        'isBookmarked': false,
        'guid': 'mock-article-10',
        'author': 'Integration Team',
        'categories': ['Flutter', 'Native Integration']
      }
    ];
  }
}
