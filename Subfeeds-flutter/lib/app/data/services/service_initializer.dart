import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:subfeeds/app/data/repositories/api_repository.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 服务初始化类，用于在应用启动时初始化各种服务
class ServiceInitializer {
  // 单例模式
  static final ServiceInitializer _instance = ServiceInitializer._internal();
  factory ServiceInitializer() => _instance;
  ServiceInitializer._internal();

  // API仓库
  final ApiRepository apiRepository = ApiRepository();

  // 是否已初始化
  bool _isInitialized = false;

  // 是否正在初始化
  bool _isInitializing = false;

  // 初始化超时时间
  static const Duration _initTimeout = Duration(seconds: 3);

  // 设备信息默认值
  static const String _defaultAppVersion = '1.0.0';
  static const String _defaultOsType = 'iOS';
  static const String _defaultOsVersion = 'unknown';
  static const String _defaultDeviceModel = 'unknown';
  static const String _defaultDeviceId = 'unknown';

  /// 获取设备信息并存储到本地
  Future<void> _loadDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();

      String deviceId = _defaultDeviceId;
      String osType = _defaultOsType;
      String osVersion = _defaultOsVersion;
      String deviceModel = _defaultDeviceModel;
      String appVersion = _defaultAppVersion;

      if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        debugPrint('获取到iOS设备信息: $iosInfo');
        deviceId = iosInfo.identifierForVendor ?? _defaultDeviceId;
        osType = 'iOS';
        osVersion = iosInfo.systemVersion ?? _defaultOsVersion;
        deviceModel = iosInfo.model ?? _defaultDeviceModel;
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        debugPrint('获取到Android设备信息: $androidInfo');
        deviceId = androidInfo.id ?? _defaultDeviceId;
        osType = 'Android';
        osVersion = androidInfo.version.release ?? _defaultOsVersion;
        deviceModel = androidInfo.model ?? _defaultDeviceModel;
      } else {
        deviceId = _defaultDeviceId;
        osType = _defaultOsType;
        osVersion = _defaultOsVersion;
        deviceModel = _defaultDeviceModel;
      }

      appVersion = packageInfo.version;

      // 存储设备信息到本地
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('device_id', deviceId);
      await prefs.setString('os_type', osType);
      await prefs.setString('os_version', osVersion);
      await prefs.setString('device_model', deviceModel);
      await prefs.setString('app_version', appVersion);

      debugPrint(
          '设备信息加载完成并保存到本地: deviceId=$deviceId, osType=$osType, osVersion=$osVersion, appVersion=$appVersion, deviceModel=$deviceModel');
    } catch (e) {
      debugPrint('获取设备信息失败: $e');
      // 使用默认值
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('device_id', _defaultDeviceId);
      await prefs.setString('os_type', _defaultOsType);
      await prefs.setString('os_version', _defaultOsVersion);
      await prefs.setString('device_model', _defaultDeviceModel);
      await prefs.setString('app_version', _defaultAppVersion);
    }
  }

  /// 初始化所有服务
  Future<void> initializeServices() async {
    // 如果已初始化或正在初始化，则直接返回
    if (_isInitialized || _isInitializing) return;

    _isInitializing = true;

    try {
      // 根据环境选择不同的API基础URL
      final String baseUrl = _getBaseUrl();
      debugPrint('开始初始化服务，使用基础URL: $baseUrl');

      // 先加载设备信息并存储到本地
      await Future.any([
        _loadDeviceInfo(),
        Future.delayed(const Duration(seconds: 2), () {
          debugPrint('设备信息加载超时，使用默认值');
          throw TimeoutException('设备信息加载超时');
        }),
      ]).catchError((e) {
        debugPrint('设备信息加载失败，将使用默认值: $e');
      });

      // 使用带超时的Future.any，确保即使网络请求卡住也能继续
      await Future.any([
        _initWithTimeout(baseUrl),
        Future.delayed(_initTimeout, () {
          debugPrint('服务初始化超时，将继续运行应用');
          throw TimeoutException('服务初始化超时');
        }),
      ]);

      _isInitialized = true;
      debugPrint('服务初始化完成');
    } catch (e) {
      // 捕获并记录错误，但不重新抛出，允许应用继续运行
      debugPrint('服务初始化失败，但应用将继续运行: $e');
      _isInitialized = true; // 标记为已初始化，避免重复尝试
    } finally {
      _isInitializing = false;
    }
  }

  /// 带超时的初始化
  Future<void> _initWithTimeout(String baseUrl) async {
    try {
      // 创建一个Completer来控制完成状态
      final completer = Completer<void>();

      // 启动一个微任务来执行初始化
      Future.microtask(() async {
        try {
          await apiRepository.init(baseUrl);
          if (!completer.isCompleted) completer.complete();
        } catch (e) {
          if (!completer.isCompleted) completer.completeError(e);
        }
      });

      // 设置超时
      Timer(_initTimeout, () {
        if (!completer.isCompleted) {
          completer.completeError(TimeoutException('API初始化超时'));
        }
      });

      // 等待完成或超时
      await completer.future;
    } catch (e) {
      debugPrint('API初始化失败: $e');
      // 不重新抛出异常，允许应用继续运行
    }
  }

  /// 根据环境获取API基础URL
  String _getBaseUrl() {
    if (kReleaseMode) {
      // 生产环境
      return 'https://www.subfeeds.com/prod-api';
    } else {
      // 开发环境R
      return 'https://www.subfeeds.com/prod-api';
      // return 'http://192.168.31.1:8999';
    }
  }

  /// 重置所有服务
  Future<void> resetServices() async {
    try {
      // 清除token
      await apiRepository.clearToken();

      _isInitialized = false;
      debugPrint('服务重置完成');
    } catch (e) {
      debugPrint('服务重置失败: $e');
      // 不重新抛出异常
    }
  }
}
