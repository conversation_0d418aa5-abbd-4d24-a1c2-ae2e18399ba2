import 'package:shared_preferences/shared_preferences.dart';

/// 新手引导服务
class TutorialService {
  static const String _footpointTutorialKey = 'footpoint_tutorial_shown';
  
  static TutorialService? _instance;
  static TutorialService get instance => _instance ??= TutorialService._();
  
  TutorialService._();

  /// 检查足迹页面引导是否已显示
  Future<bool> isFootpointTutorialShown() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_footpointTutorialKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// 标记足迹页面引导已显示
  Future<void> markFootpointTutorialShown() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_footpointTutorialKey, true);
    } catch (e) {
      // 静默处理错误
    }
  }

  /// 重置足迹页面引导状态（用于测试）
  Future<void> resetFootpointTutorial() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_footpointTutorialKey);
    } catch (e) {
      // 静默处理错误
    }
  }

  /// 重置所有引导状态（用于测试）
  Future<void> resetAllTutorials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_footpointTutorialKey);
      // 可以在这里添加其他引导的重置
    } catch (e) {
      // 静默处理错误
    }
  }
}
