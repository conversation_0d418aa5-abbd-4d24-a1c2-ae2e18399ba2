import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/chat_conversation.dart';
import '../../presentation/screens/article/widgets/chat_dialog.dart';
import 'dart:io';

/// 聊天历史记录数据库服务
class ChatHistoryService {
  static final ChatHistoryService _instance = ChatHistoryService._internal();
  factory ChatHistoryService() => _instance;
  ChatHistoryService._internal();

  Database? _database;

  /// 获取数据库实例
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// 初始化数据库
  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'chat_history.db');

    return await openDatabase(
      path,
      version: 2,
      onCreate: _createTables,
      onUpgrade: _upgradeDatabase,
    );
  }

  /// 创建数据库表
  Future<void> _createTables(Database db, int version) async {
    await db.execute('''
      CREATE TABLE conversations (
        id TEXT PRIMARY KEY,
        article_title TEXT NOT NULL,
        article_content TEXT NOT NULL,
        article_url TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE messages (
        id TEXT PRIMARY KEY,
        conversation_id TEXT NOT NULL,
        content TEXT NOT NULL,
        is_user INTEGER NOT NULL,
        is_loading INTEGER NOT NULL DEFAULT 0,
        is_error INTEGER NOT NULL DEFAULT 0,
        is_complete INTEGER NOT NULL DEFAULT 1,
        timestamp TEXT NOT NULL,
        message_order INTEGER NOT NULL,
        FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
      )
    ''');

    // 创建索引以提高查询性能
    await db.execute('''
      CREATE INDEX idx_conversations_created_at ON conversations (created_at DESC)
    ''');

    await db.execute('''
      CREATE INDEX idx_messages_conversation_id ON messages (conversation_id, message_order)
    ''');
  }

  /// 数据库升级
  Future<void> _upgradeDatabase(
      Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // 添加is_complete字段
      await db.execute(
          'ALTER TABLE messages ADD COLUMN is_complete INTEGER NOT NULL DEFAULT 1');
    }
  }

  /// 保存对话（基于文章分组）
  Future<void> saveConversation(ChatConversation conversation) async {
    debugPrint('ChatHistoryService.saveConversation 被调用');
    debugPrint('对话ID: ${conversation.id}');
    debugPrint('文章标题: ${conversation.articleTitle}');
    debugPrint('文章URL: ${conversation.articleUrl}');
    debugPrint('消息数量: ${conversation.messages.length}');

    final db = await database;

    await db.transaction((txn) async {
      // 保存对话基本信息
      await txn.insert(
        'conversations',
        {
          'id': conversation.id,
          'article_title': conversation.articleTitle,
          'article_content': conversation.articleContent,
          'article_url': conversation.articleUrl,
          'created_at': conversation.createdAt.toIso8601String(),
          'updated_at': conversation.updatedAt.toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // 删除旧消息
      await txn.delete(
        'messages',
        where: 'conversation_id = ?',
        whereArgs: [conversation.id],
      );

      // 保存消息
      for (int i = 0; i < conversation.messages.length; i++) {
        final message = conversation.messages[i];
        await txn.insert(
          'messages',
          {
            'id': message.id ?? '${conversation.id}_$i',
            'conversation_id': conversation.id,
            'content': message.content,
            'is_user': message.isUser ? 1 : 0,
            'is_loading': message.isLoading ? 1 : 0,
            'is_error': message.isError ? 1 : 0,
            'is_complete': message.isComplete ? 1 : 0,
            'timestamp': message.timestamp.toIso8601String(),
            'message_order': i,
          },
        );
      }
    });

    debugPrint('✅ 对话保存成功: ${conversation.id}');
  }

  /// 清理重复的对话记录（用于数据迁移）
  Future<void> cleanupDuplicateConversations() async {
    // 获取所有对话
    final conversations = await getAllConversations();

    // 按文章标题分组，查找可能的重复记录
    final Map<String, List<ChatConversation>> groupedByTitle = {};

    for (final conversation in conversations) {
      final title = conversation.articleTitle;
      if (!groupedByTitle.containsKey(title)) {
        groupedByTitle[title] = [];
      }
      groupedByTitle[title]!.add(conversation);
    }

    // 处理每个标题组中的重复记录
    for (final entry in groupedByTitle.entries) {
      final title = entry.key;
      final conversationsForTitle = entry.value;

      if (conversationsForTitle.length > 1) {
        debugPrint('发现重复对话记录，标题: $title，数量: ${conversationsForTitle.length}');

        // 保留最新的记录，删除旧的记录
        conversationsForTitle
            .sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        final latestConversation = conversationsForTitle.first;
        final duplicates = conversationsForTitle.skip(1).toList();

        for (final duplicate in duplicates) {
          debugPrint('删除重复对话: ${duplicate.id}');
          await deleteConversation(duplicate.id);
        }

        debugPrint('保留最新对话: ${latestConversation.id}');
      }
    }
  }

  /// 根据文章URL查找现有对话
  Future<ChatConversation?> getConversationByArticleUrl(
      String articleUrl) async {
    final db = await database;

    final conversationMaps = await db.query(
      'conversations',
      where: 'article_url = ?',
      whereArgs: [articleUrl],
      orderBy: 'updated_at DESC',
      limit: 1,
    );

    if (conversationMaps.isEmpty) return null;

    final conversationMap = conversationMaps.first;
    final messages =
        await _getMessagesForConversation(conversationMap['id'] as String);

    return ChatConversation(
      id: conversationMap['id'] as String,
      articleTitle: conversationMap['article_title'] as String,
      articleContent: conversationMap['article_content'] as String,
      articleUrl: conversationMap['article_url'] as String,
      createdAt: DateTime.parse(conversationMap['created_at'] as String),
      updatedAt: DateTime.parse(conversationMap['updated_at'] as String),
      messages: messages,
    );
  }

  /// 为文章添加新消息到现有对话
  Future<void> addMessagesToConversation(
      String conversationId, List<ChatMessage> newMessages) async {
    final db = await database;

    await db.transaction((txn) async {
      // 获取当前消息数量以确定新消息的顺序
      final existingMessages = await txn.query(
        'messages',
        where: 'conversation_id = ?',
        whereArgs: [conversationId],
        orderBy: 'message_order DESC',
        limit: 1,
      );

      int nextOrder = 0;
      if (existingMessages.isNotEmpty) {
        nextOrder = (existingMessages.first['message_order'] as int) + 1;
      }

      // 添加新消息
      for (int i = 0; i < newMessages.length; i++) {
        final message = newMessages[i];
        await txn.insert(
          'messages',
          {
            'id': message.id ?? '${conversationId}_${nextOrder + i}',
            'conversation_id': conversationId,
            'content': message.content,
            'is_user': message.isUser ? 1 : 0,
            'is_loading': message.isLoading ? 1 : 0,
            'is_error': message.isError ? 1 : 0,
            'is_complete': message.isComplete ? 1 : 0,
            'timestamp': message.timestamp.toIso8601String(),
            'message_order': nextOrder + i,
          },
        );
      }

      // 更新对话的最后更新时间
      await txn.update(
        'conversations',
        {'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [conversationId],
      );
    });
  }

  /// 获取所有对话（按创建时间倒序）
  Future<List<ChatConversation>> getAllConversations() async {
    final db = await database;

    final conversationMaps = await db.query(
      'conversations',
      orderBy: 'created_at DESC',
    );

    final conversations = <ChatConversation>[];

    for (final conversationMap in conversationMaps) {
      final messages =
          await _getMessagesForConversation(conversationMap['id'] as String);

      conversations.add(ChatConversation(
        id: conversationMap['id'] as String,
        articleTitle: conversationMap['article_title'] as String,
        articleContent: conversationMap['article_content'] as String,
        articleUrl: conversationMap['article_url'] as String,
        createdAt: DateTime.parse(conversationMap['created_at'] as String),
        updatedAt: DateTime.parse(conversationMap['updated_at'] as String),
        messages: messages,
      ));
    }

    return conversations;
  }

  /// 获取指定对话的消息
  Future<List<ChatMessage>> _getMessagesForConversation(
      String conversationId) async {
    final db = await database;

    final messageMaps = await db.query(
      'messages',
      where: 'conversation_id = ?',
      whereArgs: [conversationId],
      orderBy: 'message_order ASC',
    );

    return messageMaps.map((messageMap) {
      return ChatMessage(
        content: messageMap['content'] as String,
        isUser: (messageMap['is_user'] as int) == 1,
        isLoading: (messageMap['is_loading'] as int) == 1,
        isError: (messageMap['is_error'] as int) == 1,
        isComplete: (messageMap['is_complete'] as int?) == 1,
        timestamp: DateTime.parse(messageMap['timestamp'] as String),
        id: messageMap['id'] as String,
      );
    }).toList();
  }

  /// 删除对话
  Future<void> deleteConversation(String conversationId) async {
    final db = await database;

    await db.transaction((txn) async {
      await txn.delete(
        'messages',
        where: 'conversation_id = ?',
        whereArgs: [conversationId],
      );

      await txn.delete(
        'conversations',
        where: 'id = ?',
        whereArgs: [conversationId],
      );
    });
  }

  /// 获取指定对话
  Future<ChatConversation?> getConversation(String conversationId) async {
    final db = await database;

    final conversationMaps = await db.query(
      'conversations',
      where: 'id = ?',
      whereArgs: [conversationId],
      limit: 1,
    );

    if (conversationMaps.isEmpty) return null;

    final conversationMap = conversationMaps.first;
    final messages = await _getMessagesForConversation(conversationId);

    return ChatConversation(
      id: conversationMap['id'] as String,
      articleTitle: conversationMap['article_title'] as String,
      articleContent: conversationMap['article_content'] as String,
      articleUrl: conversationMap['article_url'] as String,
      createdAt: DateTime.parse(conversationMap['created_at'] as String),
      updatedAt: DateTime.parse(conversationMap['updated_at'] as String),
      messages: messages,
    );
  }

  /// 更新对话的最后更新时间
  Future<void> updateConversationTimestamp(String conversationId) async {
    final db = await database;

    await db.update(
      'conversations',
      {'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [conversationId],
    );
  }

  /// 清空所有历史记录
  Future<void> clearAllHistory() async {
    try {
      // 方案1：完全删除数据库文件（最彻底的清除）
      final db = await database;
      final dbPath = db.path;

      // 关闭数据库连接
      await db.close();

      // 删除数据库文件
      final file = File(dbPath);
      if (await file.exists()) {
        await file.delete();
        debugPrint('数据库文件已完全删除: $dbPath');
      }

      // 重置数据库实例，下次访问时会重新创建
      _database = null;

      debugPrint('数据库已完全清空，大小变为0KB');
    } catch (e) {
      debugPrint('删除数据库文件失败，使用备用清除方案: $e');

      // 备用方案：传统的清除方式
      final db = await database;
      await db.transaction((txn) async {
        await txn.delete('messages');
        await txn.delete('conversations');
      });

      // 执行 VACUUM 命令来压缩数据库文件，回收删除数据后的空间
      await db.execute('VACUUM');
      debugPrint('数据库已清空并压缩（备用方案）');
    }
  }

  /// 获取数据库文件大小
  Future<int> getDatabaseSize() async {
    try {
      final db = await database;
      final dbPath = db.path;
      final file = File(dbPath);

      if (await file.exists()) {
        final stat = await file.stat();
        return stat.size;
      }
      return 0;
    } catch (e) {
      debugPrint('获取数据库大小失败: $e');
      return 0;
    }
  }

  /// 获取对话数量
  Future<int> getConversationCount() async {
    try {
      final db = await database;
      final result =
          await db.rawQuery('SELECT COUNT(*) as count FROM conversations');
      return result.first['count'] as int;
    } catch (e) {
      debugPrint('获取对话数量失败: $e');
      return 0;
    }
  }
}
