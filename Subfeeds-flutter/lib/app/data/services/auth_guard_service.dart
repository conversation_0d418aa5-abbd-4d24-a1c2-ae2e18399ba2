import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:subfeeds/app/presentation/widgets/auth_prompt_bottom_sheet.dart';
import 'package:subfeeds/app/data/services/pending_action_manager.dart';

/// 认证保护功能的类型枚举
enum ProtectedFeatureType {
  personalInfo,
  premiumFeatures,
  syncData,
  manageSubscriptions,
  changePassword,
  accountSettings,
  readLater,
  favorites,
  customFeature,
}

/// 认证保护服务
///
/// 这个服务提供了一个集中的方式来处理需要登录的功能访问。
/// 当用户尝试访问受保护的功能时，会自动检查登录状态，
/// 如果未登录则显示友好的提示并引导用户登录。
class AuthGuardService extends GetxService {
  static AuthGuardService get to => Get.find();

  final UserController _userController = Get.find<UserController>();
  final PendingActionManager _pendingActionManager =
      Get.find<PendingActionManager>();

  // 当前是否正在显示认证提示
  final RxBool _isShowingAuthPrompt = false.obs;

  /// 获取待执行操作列表
  List<PendingActionData> get pendingActions =>
      _pendingActionManager.pendingActions;

  /// 是否正在显示认证提示
  bool get isShowingAuthPrompt => _isShowingAuthPrompt.value;

  /// 是否有待执行操作
  bool get hasPendingActions => _pendingActionManager.hasPendingActions;

  @override
  void onInit() {
    super.onInit();
    // 监听用户登录状态变化
    ever(_userController.user, _onUserStateChanged);
  }

  /// 用户状态变化处理
  void _onUserStateChanged(dynamic user) {
    if (user != null) {
      // 用户已登录，执行所有待执行的操作
      _pendingActionManager.executeAllPendingActions();
    }
  }

  /// 检查功能访问权限并处理认证
  ///
  /// [featureType] 功能类型
  /// [action] 需要执行的操作
  /// [customTitle] 自定义标题（可选）
  /// [customDescription] 自定义描述（可选）
  /// [forcePrompt] 是否强制显示提示（即使已登录）
  ///
  /// 返回 true 表示可以立即执行操作，false 表示需要等待登录
  Future<bool> requireAuth({
    required ProtectedFeatureType featureType,
    required VoidCallback action,
    String? customTitle,
    String? customDescription,
    bool forcePrompt = false,
  }) async {
    try {
      // 验证服务状态
      if (!_validateServiceState()) {
        return false;
      }

      // 验证操作参数
      if (!_validateActionParameters(
          featureType: featureType, action: action)) {
        return false;
      }

      // 如果用户已登录且不强制显示提示，直接执行操作
      if (_userController.user.value != null && !forcePrompt) {
        action();
        return true;
      }

      // 如果正在显示认证提示，避免重复显示
      if (_isShowingAuthPrompt.value) {
        debugPrint('认证提示已在显示中，跳过重复请求');
        return false;
      }
    } catch (e) {
      handleAuthError('认证检查失败: $e');
      return false;
    }

    // 创建待执行操作ID
    final actionId = DateTime.now().millisecondsSinceEpoch.toString();
    final description =
        customDescription ?? _getFeatureDescription(featureType);

    // 添加到待执行队列
    _pendingActionManager.addPendingAction(
      id: actionId,
      description: description,
      featureType: featureType,
      action: action,
    );

    // 显示认证提示
    await _showAuthPrompt(
      featureType: featureType,
      title: customTitle ?? _getFeatureTitle(featureType),
      description: description,
      pendingActionId: actionId,
    );

    return false;
  }

  /// 显示认证提示
  Future<void> _showAuthPrompt({
    required ProtectedFeatureType featureType,
    required String title,
    required String description,
    required String pendingActionId,
  }) async {
    if (_isShowingAuthPrompt.value) return;

    _isShowingAuthPrompt.value = true;

    try {
      await Get.bottomSheet(
        AuthPromptBottomSheet(
          title: title,
          description: description,
          featureType: featureType,
          onLoginSuccess: () => _onLoginSuccess(pendingActionId),
          onCancel: () => _onAuthCancel(pendingActionId),
        ),
        isScrollControlled: true,
        isDismissible: true,
        enableDrag: true,
      );
    } finally {
      _isShowingAuthPrompt.value = false;
    }
  }

  /// 登录成功回调
  void _onLoginSuccess(String pendingActionId) {
    // 执行特定的待执行操作
    _pendingActionManager.executePendingAction(pendingActionId);
  }

  /// 取消认证回调
  void _onAuthCancel(String pendingActionId) {
    // 移除对应的待执行操作
    _pendingActionManager.removePendingAction(pendingActionId);
  }

  /// 清除所有待执行操作
  void clearPendingActions() {
    _pendingActionManager.clearAllPendingActions();
  }

  /// 清除过期的待执行操作
  void clearExpiredActions() {
    _pendingActionManager.clearExpiredActions();
  }

  /// 获取功能标题
  String _getFeatureTitle(ProtectedFeatureType featureType) {
    switch (featureType) {
      case ProtectedFeatureType.personalInfo:
        return 'auth_required_personal_info_title'.tr;
      case ProtectedFeatureType.premiumFeatures:
        return 'auth_required_premium_title'.tr;
      case ProtectedFeatureType.syncData:
        return 'auth_required_sync_title'.tr;
      case ProtectedFeatureType.manageSubscriptions:
        return 'auth_required_subscriptions_title'.tr;
      case ProtectedFeatureType.changePassword:
        return 'auth_required_password_title'.tr;
      case ProtectedFeatureType.accountSettings:
        return 'auth_required_account_title'.tr;
      case ProtectedFeatureType.readLater:
        return 'auth_required_read_later_title'.tr;
      case ProtectedFeatureType.favorites:
        return 'auth_required_favorites_title'.tr;
      case ProtectedFeatureType.customFeature:
        return 'auth_required_custom_title'.tr;
    }
  }

  /// 获取功能描述
  String _getFeatureDescription(ProtectedFeatureType featureType) {
    switch (featureType) {
      case ProtectedFeatureType.personalInfo:
        return 'auth_required_personal_info_desc'.tr;
      case ProtectedFeatureType.premiumFeatures:
        return 'auth_required_premium_desc'.tr;
      case ProtectedFeatureType.syncData:
        return 'auth_required_sync_desc'.tr;
      case ProtectedFeatureType.manageSubscriptions:
        return 'auth_required_subscriptions_desc'.tr;
      case ProtectedFeatureType.changePassword:
        return 'auth_required_password_desc'.tr;
      case ProtectedFeatureType.accountSettings:
        return 'auth_required_account_desc'.tr;
      case ProtectedFeatureType.readLater:
        return 'auth_required_read_later_desc'.tr;
      case ProtectedFeatureType.favorites:
        return 'auth_required_favorites_desc'.tr;
      case ProtectedFeatureType.customFeature:
        return 'auth_required_custom_desc'.tr;
    }
  }

  /// 便捷方法：检查是否需要认证（不执行操作）
  bool isAuthRequired() {
    return _userController.user.value == null;
  }

  /// 便捷方法：直接导航到登录页面
  void navigateToLogin({String? returnRoute}) {
    if (returnRoute != null) {
      Get.toNamed(Routes.LOGIN, arguments: {'returnRoute': returnRoute});
    } else {
      Get.toNamed(Routes.LOGIN_SELECTION);
    }
  }

  /// 便捷方法：显示简单的登录提示对话框
  Future<bool?> showSimpleAuthDialog({
    String? title,
    String? message,
  }) async {
    return await Get.dialog<bool>(
      AlertDialog(
        title: Text(title ?? 'auth_required_title'.tr),
        content: Text(message ?? 'auth_required_message'.tr),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text('cancel'.tr),
          ),
          TextButton(
            onPressed: () {
              Get.back(result: true);
              navigateToLogin();
            },
            child: Text('login'.tr),
          ),
        ],
      ),
    );
  }

  /// 便捷方法：为个人信息功能提供认证保护
  Future<bool> requireAuthForPersonalInfo(VoidCallback action) {
    return requireAuth(
      featureType: ProtectedFeatureType.personalInfo,
      action: action,
    );
  }

  /// 便捷方法：为高级功能提供认证保护
  Future<bool> requireAuthForPremiumFeatures(VoidCallback action) {
    return requireAuth(
      featureType: ProtectedFeatureType.premiumFeatures,
      action: action,
    );
  }

  /// 便捷方法：为同步功能提供认证保护
  Future<bool> requireAuthForSync(VoidCallback action) {
    return requireAuth(
      featureType: ProtectedFeatureType.syncData,
      action: action,
    );
  }

  /// 便捷方法：为稍后阅读功能提供认证保护
  Future<bool> requireAuthForReadLater(VoidCallback action) {
    return requireAuth(
      featureType: ProtectedFeatureType.readLater,
      action: action,
    );
  }

  /// 便捷方法：为收藏功能提供认证保护
  Future<bool> requireAuthForFavorites(VoidCallback action) {
    return requireAuth(
      featureType: ProtectedFeatureType.favorites,
      action: action,
    );
  }

  /// 错误处理：处理认证失败
  void handleAuthError(String error, {String? context}) {
    debugPrint('认证错误: $error${context != null ? ' (上下文: $context)' : ''}');

    // 显示错误提示
    Get.snackbar(
      'auth_error_title'.tr,
      error,
      snackPosition: SnackPosition.TOP,
      backgroundColor: const Color(0xFFFF5252),
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }

  /// 错误处理：处理网络错误
  void handleNetworkError({String? context}) {
    debugPrint('网络错误${context != null ? ' (上下文: $context)' : ''}');

    Get.snackbar(
      'network_error_title'.tr,
      'network_error_message'.tr,
      snackPosition: SnackPosition.TOP,
      backgroundColor: const Color(0xFFFF9800),
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }

  /// 错误处理：处理服务不可用错误
  void handleServiceUnavailableError({String? context}) {
    debugPrint('服务不可用${context != null ? ' (上下文: $context)' : ''}');

    Get.snackbar(
      'service_unavailable_title'.tr,
      'service_unavailable_message'.tr,
      snackPosition: SnackPosition.TOP,
      backgroundColor: const Color(0xFF9E9E9E),
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }

  /// 安全检查：验证服务状态
  bool _validateServiceState() {
    try {
      // 检查用户控制器是否可用
      if (!Get.isRegistered<UserController>()) {
        handleServiceUnavailableError(context: 'UserController未注册');
        return false;
      }

      // 检查待执行操作管理器是否可用
      if (!Get.isRegistered<PendingActionManager>()) {
        handleServiceUnavailableError(context: 'PendingActionManager未注册');
        return false;
      }

      return true;
    } catch (e) {
      handleServiceUnavailableError(context: '服务状态验证失败: $e');
      return false;
    }
  }

  /// 安全检查：验证操作参数
  bool _validateActionParameters({
    required ProtectedFeatureType featureType,
    required VoidCallback action,
  }) {
    try {
      // 检查功能类型是否有效
      if (!ProtectedFeatureType.values.contains(featureType)) {
        handleAuthError('无效的功能类型', context: featureType.toString());
        return false;
      }

      // 回调函数在Dart中不能为null（非空类型），所以这个检查是多余的
      // 但我们可以检查其他条件

      return true;
    } catch (e) {
      handleAuthError('参数验证失败: $e');
      return false;
    }
  }

  /// 重试机制：重试认证操作
  Future<bool> retryAuth({
    required ProtectedFeatureType featureType,
    required VoidCallback action,
    String? customTitle,
    String? customDescription,
    int maxRetries = 3,
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('认证重试第 $attempt 次');

        final result = await requireAuth(
          featureType: featureType,
          action: action,
          customTitle: customTitle,
          customDescription: customDescription,
        );

        if (result) {
          debugPrint('认证重试成功');
          return true;
        }

        // 如果不是最后一次尝试，等待一段时间再重试
        if (attempt < maxRetries) {
          await Future.delayed(Duration(seconds: attempt));
        }
      } catch (e) {
        debugPrint('认证重试第 $attempt 次失败: $e');

        if (attempt == maxRetries) {
          handleAuthError('认证重试失败: $e');
          return false;
        }
      }
    }

    handleAuthError('认证重试次数已用完');
    return false;
  }

  /// 恢复机制：从错误状态恢复
  Future<void> recoverFromError() async {
    try {
      debugPrint('开始从错误状态恢复');

      // 清除可能损坏的状态
      _isShowingAuthPrompt.value = false;

      // 清除过期的待执行操作
      clearExpiredActions();

      // 重新检查用户登录状态
      await _userController.checkLoginStatus();

      debugPrint('错误状态恢复完成');
    } catch (e) {
      debugPrint('错误状态恢复失败: $e');
      handleServiceUnavailableError(context: '恢复失败');
    }
  }
}
