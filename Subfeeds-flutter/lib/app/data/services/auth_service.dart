import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:subfeeds/app/data/models/user_model.dart';

/// 第三方登录服务类
class AuthService {
  // 单例模式
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // Google登录客户端ID
  final String _googleClientIdiOS =
      '355252710931-v8rhqp3hf7v05v9l9j0maqqdnbqbop1r.apps.googleusercontent.com';

  final String _googleClientIdAndroid =
      '355252710931-cftsi2vj65qe83egu1nc3nuqeta6fru3.apps.googleusercontent.com';

  // Apple登录客户端ID
  final String _appleClientId = 'com.subfeeds.web';

  // Google登录实例
  late GoogleSignIn _googleSignIn;

  // 是否已初始化
  bool _isInitialized = false;

  /// 初始化服务
  Future<void> init() async {
    if (_isInitialized) return;

    try {
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        debugPrint('初始化Google登录(iOS), 使用iOS客户端ID');
        _googleSignIn = GoogleSignIn(
          clientId: _googleClientIdiOS,
          scopes: ['email', 'profile'],
        );
      } else if (defaultTargetPlatform == TargetPlatform.android) {
        // 在Android上，不要使用clientId参数，只使用serverClientId
        // 这是关键修复
        debugPrint('初始化Google登录(Android), 改为不设置clientId，只传serverClientId');
        _googleSignIn = GoogleSignIn(
          // 移除clientId参数，改用serverClientId
          serverClientId:
              '355252710931-cftsi2vj65qe83egu1nc3nuqeta6fru3.apps.googleusercontent.com',
          scopes: [
            'email',
            'profile',
          ],
        );
      } else {
        // Web或其他平台
        debugPrint('初始化Google登录(Web/其他), 使用Web客户端ID');
        _googleSignIn = GoogleSignIn(
          clientId:
              '355252710931-v7h1i6fq1t7v73kndlo4qu1g00nmi61h.apps.googleusercontent.com',
          scopes: [
            'email',
            'profile',
          ],
        );
      }

      debugPrint(
          'Google Sign-In 初始化成功。Platform: ${defaultTargetPlatform.toString()}');
      _isInitialized = true;
    } catch (e) {
      debugPrint('Google Sign-In 初始化失败: $e');
      rethrow;
    }
  }

  /// Google登录
  Future<UserModel?> signInWithGoogle() async {
    try {
      debugPrint('开始Google登录流程');
      // 确保已初始化
      await init();
      debugPrint('初始化Google登录完成,平台:${defaultTargetPlatform}');

      debugPrint('清除之前的登录状态...');
      // 先尝试注销之前的登录状态
      try {
        final currentUser = await _googleSignIn.signInSilently();
        if (currentUser != null) {
          await _googleSignIn.disconnect();
          debugPrint('已断开之前的登录状态');
        }
      } catch (e) {
        debugPrint('清除之前登录状态时出错(可忽略): $e');
      }

      debugPrint('开始调用Google登录...');
      GoogleSignInAccount? account;

      try {
        account = await _googleSignIn.signIn();
        debugPrint('Google登录调用返回结果: ${account != null ? "成功" : "用户取消或失败"}');
      } catch (e) {
        debugPrint('Google登录过程出错: $e');
        if (e.toString().contains('PlatformException')) {
          debugPrint('检测到平台错误，错误详情: $e');
          _isInitialized = false;
          await init();
          debugPrint('重新初始化后再次尝试登录...');
          account = await _googleSignIn.signIn();
        } else {
          rethrow;
        }
      }

      debugPrint('Google登录结果: ${account?.email ?? "无账户信息"}');

      // 如果用户取消登录，则返回null
      if (account == null) {
        debugPrint('用户取消登录或登录失败');
        return null;
      }

      // 获取用户信息
      debugPrint('获取Google认证信息');
      final GoogleSignInAuthentication auth = await account.authentication;

      // 检查认证信息
      if (auth.idToken == null) {
        debugPrint('未能获取到Google ID Token');
        return null;
      }

      debugPrint('成功获取ID Token，解析Google ID Token');
      final Map<String, dynamic> idToken = _parseJwt(auth.idToken!);

      // 创建用户模型
      debugPrint('创建用户模型');
      return UserModel(
        account: account.email,
        name: account.displayName ?? '',
        avatar: account.photoUrl,
        uuid: idToken['sub'] as String?, // Google的唯一标识符
        channel: 2, // Google登录渠道
      );
    } catch (e, stackTrace) {
      debugPrint('Google登录失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      return null;
    }
  }

  /// Apple登录
  Future<UserModel?> signInWithApple() async {
    try {
      // 生成随机状态字符串
      final rawNonce = _generateNonce();
      final nonce = _sha256ofString(rawNonce);

      // 请求Apple登录
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: _appleClientId,
          redirectUri: Uri.parse('https://www.subfeeds.com/'),
        ),
      );

      // 获取用户信息
      final String? email = credential.email;
      final String? givenName = credential.givenName;
      final String? familyName = credential.familyName;
      final String? userIdentifier = credential.userIdentifier;

      // 组合用户名
      String? fullName;
      if (givenName != null || familyName != null) {
        fullName = [givenName, familyName]
            .where((name) => name != null && name.isNotEmpty)
            .join(' ');
      }

      // 创建用户模型
      return UserModel(
        account: email,
        name: fullName,
        uuid: userIdentifier, // Apple的唯一标识符
        channel: 4, // Apple登录渠道
      );
    } catch (e) {
      debugPrint('Apple登录失败: $e');
      return null;
    }
  }

  /// 退出登录
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
    } catch (e) {
      debugPrint('退出登录失败: $e');
    }
  }

  /// 解析JWT令牌
  Map<String, dynamic> _parseJwt(String token) {
    if (token.isEmpty) return {};

    final parts = token.split('.');
    if (parts.length != 3) return {};

    final payload = parts[1];
    var normalized = base64Url.normalize(payload);
    var resp = utf8.decode(base64Url.decode(normalized));
    final payloadMap = json.decode(resp);

    if (payloadMap is Map<String, dynamic>) {
      return payloadMap;
    }

    return {};
  }

  /// 生成随机字符串
  String _generateNonce([int length = 32]) {
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)])
        .join();
  }

  /// 计算字符串的SHA256哈希值
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}
