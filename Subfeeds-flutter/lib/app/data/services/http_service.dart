import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:subfeeds/app/data/models/api_exception.dart';
import 'package:subfeeds/app/data/models/api_response.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

/// HTTP服务类，用于处理所有的网络请求
class HttpService {
  // 单例模式
  static final HttpService _instance = HttpService._internal();
  factory HttpService() => _instance;
  HttpService._internal();

  static final List<String> _pathList = [
    '/prod-api/api/v1/article/deleteCollect',
    '/prod-api/api/v1/article/insertCollect',
    '/prod-api/api/v1/article/insertLaterRead',
    '/prod-api/api/v1/article/deleteLaterRead',
    '/prod-api/api/v1/subOtherFeeds',
    '/prod-api/api/v1/feeds/deleteRssSub',
    '/prod-api/api/v1/article/manageUserNote',
    '/prod-api/api/v1/getUserInfo'
  ];

  // 默认超时时间
  static const Duration _defaultTimeout = Duration(seconds: 10);

  // 默认文件上传超时时间
  static const Duration _defaultUploadTimeout = Duration(minutes: 2);

  // 初始化超时时间
  static const Duration _initTimeout = Duration(seconds: 2);

  // 存储超时时间
  static const Duration _storageTimeout = Duration(milliseconds: 500);

  // 增加超时时间到30秒
  static const Duration _timeout = Duration(seconds: 30);

  // 默认请求头
  Map<String, String> _defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'accept-language': 'en-US',
    'S-Client': 'APP',
  };

  // 设备信息默认值
  static const String _defaultAppVersion = '1.0.0';
  static const String _defaultOsType = 'iOS';
  static const String _defaultOsVersion = 'unknown';
  static const String _defaultDeviceModel = 'unknown';
  static const String _defaultDeviceId = 'unknown';

  // 基础URL
  String _baseUrl = '';

  /// 获取基础URL
  String get baseUrl => _baseUrl;

  // 当前token
  String? _token;

  // 是否自动刷新token
  bool _autoRefreshToken = true;

  // 是否已初始化
  bool _isInitialized = false;

  // 是否正在初始化
  bool _isInitializing = false;

  /// 初始化HTTP服务
  Future<void> init({
    required String baseUrl,
    Map<String, String>? defaultHeaders,
    bool autoRefreshToken = true,
  }) async {
    // 如果已初始化或正在初始化，则直接返回
    if (_isInitialized || _isInitializing) return;

    _isInitializing = true;
    debugPrint('开始初始化HTTP服务...');

    try {
      _baseUrl = baseUrl;
      if (defaultHeaders != null) {
        _defaultHeaders = defaultHeaders;
      }
      _autoRefreshToken = autoRefreshToken;

      // 加载token
      await _loadToken();

      _isInitialized = true;
      debugPrint('HTTP服务初始化完成');
    } catch (e) {
      debugPrint('HTTP服务初始化失败: $e');
      // 不重新抛出异常，允许应用继续运行
      _isInitialized = true; // 标记为已初始化，避免重复尝试
    } finally {
      _isInitializing = false;
    }
  }

  /// 从本地存储加载token
  Future<void> _loadToken() async {
    try {
      // 使用带超时的Future.any，确保即使SharedPreferences卡住也能继续
      await Future.any([
        () async {
          final prefs = await SharedPreferences.getInstance();
          print('prefs: ${prefs.getString('auth_token')}');
          _token = prefs.getString('auth_token');
          debugPrint('Token加载${_token != null ? "成功" : "为空"}');
        }(),
        Future.delayed(_storageTimeout, () {
          throw TimeoutException('SharedPreferences获取超时');
        }),
      ]);
    } catch (e) {
      debugPrint('加载token失败: $e');
    }
  }

  //  从本地获取语言设置
  Future<String> _getLanguage() async {
    try {
      // 使用带超时的Future.any，确保即使SharedPreferences卡住也能继续
      return await Future.any([
        () async {
          final prefs = await SharedPreferences.getInstance();
          var language =
              prefs.getString('locale') == 'en_US' ? 'en-US' : 'zh-CN';
          return language;
        }(),
        Future.delayed(const Duration(seconds: 1), () {
          debugPrint('获取语言设置超时，使用默认值');
          return 'en-US'; // 默认返回中文
        }),
      ]);
    } catch (e) {
      debugPrint('获取语言设置失败: $e');
      return 'zh-CN'; // 默认返回中文
    }
  }

  /// 保存token到本地存储
  Future<void> _saveToken(String token) async {
    try {
      // 使用带超时的Future.any，确保即使SharedPreferences卡住也能继续
      await Future.any([
        () async {
          final prefs = await SharedPreferences.getInstance();

          await prefs.setString('auth_token', token);
          _token = token;
          debugPrint('Token保存成功');
        }(),
        Future.delayed(_storageTimeout, () {
          debugPrint('保存token超时，将继续运行应用');
          _token = token; // 至少在内存中保存token
          throw TimeoutException('保存token超时');
        }),
      ]);
    } catch (e) {
      debugPrint('保存token失败: $e');
      _token = token; // 至少在内存中保存token
    }
  }

  /// 清除token
  Future<void> clearToken() async {
    try {
      // 使用带超时的Future.any，确保即使SharedPreferences卡住也能继续
      await Future.any([
        () async {
          final prefs = await SharedPreferences.getInstance();
          await prefs.remove('auth_token');
          _token = null;
          debugPrint('Token清除成功');
        }(),
        Future.delayed(_storageTimeout, () {
          debugPrint('清除token超时，将继续运行应用');
          _token = null; // 至少在内存中清除token
          throw TimeoutException('清除token超时');
        }),
      ]);
    } catch (e) {
      debugPrint('清除token失败: $e');
      _token = null; // 至少在内存中清除token
    }
  }

  /// 设置token
  void setToken(String token) {
    _token = token;
    unawaited(_saveToken(token));
  }

  /// 获取token
  String? getToken() {
    return _token;
  }

  /// 获取完整URL
  String _getFullUrl(String path) {
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }
    return '$_baseUrl${path.startsWith('/') ? path : '/$path'}';
  }

  /// 处理查询参数
  String _buildUrl(String path, Map<String, dynamic>? queryParameters) {
    if (queryParameters == null || queryParameters.isEmpty) {
      return _getFullUrl(path);
    }

    final uri = Uri.parse(_getFullUrl(path));
    final newQueryParameters = Map<String, String>.from(uri.queryParameters);

    // 将所有参数转换为字符串
    queryParameters.forEach((key, value) {
      if (value != null) {
        newQueryParameters[key] = value.toString();
      }
    });

    return uri.replace(queryParameters: newQueryParameters).toString();
  }

  /// 获取请求头
  Future<Map<String, String>> _getHeaders(
      [Map<String, String>? headers]) async {
    try {
      final Map<String, String> requestHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ..._defaultHeaders ?? {},
        ...headers ?? {},
      };

      // 添加token
      if (_token != null) {
        requestHeaders['Authorization'] = 'Bearer $_token';
      }

      // 从本地存储获取设备信息，替代从内存获取的方式
      final prefs = await SharedPreferences.getInstance();
      final deviceId = prefs.getString('device_id') ?? _defaultDeviceId;
      final osType = prefs.getString('os_type') ?? _defaultOsType;
      final osVersion = prefs.getString('os_version') ?? _defaultOsVersion;
      final appVersion = prefs.getString('app_version') ?? _defaultAppVersion;
      final deviceModel =
          prefs.getString('device_model') ?? _defaultDeviceModel;

      // 添加设备信息
      requestHeaders['S-Device-Id'] = deviceId;
      requestHeaders['S-Os-Type'] = osType;
      requestHeaders['S-Os-Version'] = osVersion;
      requestHeaders['S-App-Version'] = appVersion;
      requestHeaders['S-Device-Model'] = deviceModel;
      debugPrint('请求头: $requestHeaders');

      // 添加语言设置
      final language = await Future.any([
        _getLanguage(),
        Future.delayed(_storageTimeout, () {
          debugPrint('获取语言设置超时，使用默认值');
          return 'zh-CN';
        }),
      ]);
      requestHeaders['Accept-Language'] = language;

      return requestHeaders;
    } catch (e) {
      debugPrint('获取请求头失败: $e');
      // 返回基本请求头
      return {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Accept-Language': 'zh-CN',
      };
    }
  }

  /// 处理响应
  Future<ApiResponse<T>> _handleResponse<T>(
    http.Response response,
    T? Function(dynamic)? fromJson,
  ) async {
    try {
      final int statusCode = response.statusCode;
      // 添加UTF-8解码
      final String body = utf8.decode(response.bodyBytes);
      final responseCode = jsonDecode(body)['code'] as int? ?? statusCode;
      debugPrint('响应状态码: $statusCode');
      debugPrint('响应body: $body: ${jsonDecode(body)['data']}');
      debugPrint('响应code: $responseCode');
      if (statusCode >= 200 &&
          statusCode < 300 &&
          responseCode >= 0 &&
          responseCode != 401) {
        final dynamic jsonData = jsonDecode(body);
        final int code = jsonData['code'] as int? ?? statusCode;
        final String msg = jsonData['msg'] as String? ?? 'Success';
        final dynamic data = jsonData['data'];
        final String? token = jsonData['token'] as String?;

        if (token != null) {
          setToken(token);
        }

        if (code >= 0 || (code >= 200 && code < 300)) {
          return ApiResponse.success(
            data: fromJson != null ? fromJson(jsonData) : data as T?,
            token: token,
            msg: msg,
            code: code,
          );
        } else {
          return ApiResponse.failure(
            code: code,
            msg: msg,
          );
        }
      } else if (responseCode == 401 ||
          responseCode == -103 ||
          responseCode == -100) {
        // 先清除所有Get.snackbar
        Get.closeAllSnackbars();
        if (responseCode == -103) {
          // 显示提示
          Get.snackbar(
            'no_password'.tr,
            'no_password_msg'.tr,
            icon: const Icon(Icons.error, color: Colors.orange),
            backgroundColor:
                Theme.of(Get.context!).brightness == Brightness.dark
                    ? const Color(0xFF161617)
                    : const Color(0xFF161617),
            colorText: Theme.of(Get.context!).brightness == Brightness.dark
                ? Colors.white
                : Colors.white,
          );
        } else if (responseCode == -100 || responseCode == 401) {
          debugPrint('responseCode: $responseCode');
        }
        final prefs = await SharedPreferences.getInstance();
        await clearToken(); // 清除token//
        // 清除本地用户信息
        await prefs.remove('user_info');
        return ApiResponse.failure(
          code: responseCode,
          msg: 'login_expired_msg'.tr,
        );
      } else {
        final msg = jsonDecode(body)['msg'] as String? ?? '请求失败';
        return ApiResponse.failure(
          code: responseCode ?? statusCode,
          msg: msg,
        );
      }
    } catch (e) {
      debugPrint('处理响应失败: $e');
      return ApiResponse.failure(
        code: 500,
        msg: '响应处理失败: $e',
      );
    }
  }

  /// 处理错误
  ApiResponse<T> _handleError<T>(dynamic error) {
    if (error is SocketException) {
      return ApiResponse.failure(
        code: -1001,
        msg: '网络连接失败，请检查网络设置',
      );
    } else if (error is TimeoutException) {
      return ApiResponse.failure(
        code: -1003,
        msg: '请求超时，请检查网络连接',
      );
    } else if (error is ApiException) {
      return ApiResponse.failure(
        code: error.code ?? -1,
        msg: error.message,
      );
    } else {
      return ApiResponse.failure(
        code: error.code ?? -1,
        msg: '请求失败: $error',
      );
    }
  }

  /// 发送GET请求
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T? Function(dynamic)? fromJson,
    Duration timeout = _defaultTimeout,
  }) async {
    try {
      return await _sendRequest<T>(
        'GET',
        path,
        queryParameters: queryParameters,
        headers: headers,
        fromJson: fromJson,
        timeout: timeout,
      );
    } catch (e) {
      return ApiResponse.failure(
        code: 500,
        msg: '网络请求失败: $e',
      );
    }
  }

  /// 发送POST请求
  Future<ApiResponse<T>> post<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    dynamic body,
    Map<String, String>? headers,
    T? Function(dynamic)? fromJson,
    Duration timeout = _defaultTimeout,
  }) async {
    try {
      return await _sendRequest<T>(
        'POST',
        path,
        queryParameters: queryParameters,
        body: body,
        headers: headers,
        fromJson: fromJson,
        timeout: timeout,
      );
    } catch (e) {
      debugPrint('POST请求失败: $e');
      return ApiResponse.failure(
        code: 500,
        msg: '网络请求失败: $e',
      );
    }
  }

  /// 发送PUT请求
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic body,
    Map<String, String>? headers,
    T? Function(dynamic)? fromJson,
    Duration timeout = _defaultTimeout,
  }) async {
    try {
      return await _sendRequest<T>(
        'PUT',
        path,
        body: body,
        headers: headers,
        fromJson: fromJson,
        timeout: timeout,
      );
    } catch (e) {
      debugPrint('PUT请求失败: $e');
      return ApiResponse.failure(
        code: 500,
        msg: '网络请求失败: $e',
      );
    }
  }

  /// 发送DELETE请求
  Future<ApiResponse<T>> delete<T>(
    String path, {
    Map<String, String>? headers,
    T? Function(dynamic)? fromJson,
    Duration timeout = _defaultTimeout,
  }) async {
    try {
      return await _sendRequest<T>(
        'DELETE',
        path,
        headers: headers,
        fromJson: fromJson,
        timeout: timeout,
      );
    } catch (e) {
      debugPrint('DELETE请求失败: $e');
      return ApiResponse.failure(
        code: 500,
        msg: '网络请求失败: $e',
      );
    }
  }

  /// 上传文件
  Future<ApiResponse<T>> uploadFile<T>(
    String path, {
    required File file,
    String fileField = 'file',
    Map<String, String>? fields,
    Map<String, String>? headers,
    T? Function(dynamic)? fromJson,
    Duration timeout = _defaultUploadTimeout,
    Function(int sent, int total)? onProgress,
  }) async {
    try {
      debugPrint('开始上传文件: ${file.path}');
      final Uri uri = Uri.parse(_getFullUrl(path));
      final requestHeaders = await _getHeaders(headers);

      // 创建multipart请求
      final request = http.MultipartRequest('POST', uri);
      request.headers.addAll(requestHeaders);

      // 添加文件
      final fileStream = http.ByteStream(file.openRead());
      final fileLength = await file.length();
      final multipartFile = http.MultipartFile(
        fileField,
        fileStream,
        fileLength,
        filename: file.path.split('/').last,
      );
      request.files.add(multipartFile);

      // 添加其他字段
      if (fields != null) {
        request.fields.addAll(fields);
      }

      // 发送请求并监听进度
      final streamedResponse = await request.send().timeout(timeout);

      // 转换为普通响应
      final response = await http.Response.fromStream(streamedResponse);

      debugPrint('文件上传完成: ${response.statusCode}');
      return await _handleResponse<T>(response, fromJson);
    } on TimeoutException catch (e) {
      debugPrint('文件上传超时: $e');
      return ApiResponse.failure(
        code: 408,
        msg: '文件上传超时，请检查网络连接',
      );
    } on SocketException catch (e) {
      debugPrint('网络连接错误: $e');
      return ApiResponse.failure(
        code: 503,
        msg: '网络连接错误，请检查网络设置',
      );
    } on FormatException catch (e) {
      debugPrint('数据格式错误: $e');
      return ApiResponse.failure(
        code: 400,
        msg: '数据格式错误',
      );
    } catch (e) {
      debugPrint('文件上传失败: $e');
      return _handleError<T>(e);
    }
  }

  /// 下载文件
  Future<File?> downloadFile(
    String url,
    String savePath, {
    Map<String, String>? headers,
    Duration timeout = _defaultUploadTimeout,
    Function(int received, int total)? onProgress,
  }) async {
    try {
      debugPrint('开始下载文件: $url');
      final Uri uri = Uri.parse(_getFullUrl(url));
      final requestHeaders = await _getHeaders(headers);

      // 发送请求
      final response = await http
          .get(
            uri,
            headers: requestHeaders,
          )
          .timeout(timeout);

      // 检查响应状态
      if (response.statusCode >= 200 && response.statusCode < 300) {
        // 创建文件
        final file = File(savePath);
        await file.writeAsBytes(response.bodyBytes);

        debugPrint('文件下载完成: $savePath');
        return file;
      } else {
        debugPrint('文件下载失败: ${response.statusCode}');
        return null;
      }
    } on TimeoutException catch (e) {
      debugPrint('文件下载超时: $e');
      return null;
    } on SocketException catch (e) {
      debugPrint('网络连接错误: $e');
      return null;
    } on IOException catch (e) {
      debugPrint('文件IO错误: $e');
      return null;
    } catch (e) {
      debugPrint('文件下载失败: $e');
      return null;
    }
  }

  /// 发送请求
  Future<ApiResponse<T>> _sendRequest<T>(
    String method,
    String path, {
    Map<String, dynamic>? queryParameters,
    dynamic body,
    Map<String, String>? headers,
    T? Function(dynamic)? fromJson,
    Duration timeout = _defaultTimeout,
  }) async {
    try {
      // 确保 HTTP 服务已初始化
      if (!_isInitialized) {
        debugPrint('HTTP服务尚未初始化，正在等待初始化完成...');
        // 如果正在初始化，等待初始化完成
        if (_isInitializing) {
          int retryCount = 0;
          while (_isInitializing && retryCount < 5) {
            await Future.delayed(const Duration(milliseconds: 100));
            retryCount++;
          }
        } else {
          // 如果没有初始化且没有在初始化过程中，返回错误
          return ApiResponse.failure(
            code: -1,
            msg: 'HTTP服务未初始化',
          );
        }

        // 如果仍未初始化，返回错误
        if (!_isInitialized) {
          return ApiResponse.failure(
            code: -1,
            msg: 'HTTP服务初始化失败',
          );
        }
      }

      final Uri uri = Uri.parse(_buildUrl(path, queryParameters));
      final requestHeaders = await _getHeaders(headers);
      final String? jsonBody = body != null ? jsonEncode(body) : null;

      debugPrint('发送$method请求: ${uri.toString()}');
      if (jsonBody != null) {
        debugPrint('请求体: $jsonBody');
      }
      if (queryParameters != null) {
        debugPrint('请求参数: $queryParameters');
      }

      final http.Response response;
      switch (method) {
        case 'GET':
          response = await http
              .get(
                uri,
                headers: requestHeaders,
              )
              .timeout(timeout);
          break;
        case 'POST':
          response = await http
              .post(
                uri,
                headers: requestHeaders,
                body: jsonBody,
              )
              .timeout(timeout);
          break;
        case 'PUT':
          response = await http
              .put(
                uri,
                headers: requestHeaders,
                body: jsonBody,
              )
              .timeout(timeout);
          break;
        case 'DELETE':
          response = await http
              .delete(
                uri,
                headers: requestHeaders,
                body: jsonBody,
              )
              .timeout(timeout);
          break;
        default:
          throw UnsupportedError('不支持的HTTP方法: $method');
      }

      return await _handleResponse<T>(response, fromJson);
    } on TimeoutException catch (e) {
      debugPrint('请求超时: $e');
      return ApiResponse.failure(
        code: 408,
        msg: '请求超时，请检查网络连接',
      );
    } on SocketException catch (e) {
      debugPrint('网络连接错误: $e');
      return ApiResponse.failure(
        code: 503,
        msg: '网络连接错误，请检查网络设置',
      );
    } on FormatException catch (e) {
      debugPrint('数据格式错误: $e');
      return ApiResponse.failure(
        code: 400,
        msg: '数据格式错误',
      );
    } catch (e) {
      debugPrint('请求失败123: $e');
      return ApiResponse.failure(
        code: 500,
        msg: '网络请求失败: $e',
      );
    }
  }
}
