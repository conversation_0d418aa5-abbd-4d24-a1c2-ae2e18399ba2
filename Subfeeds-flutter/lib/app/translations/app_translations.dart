/// 应用翻译配置类
class AppTranslation {
  // 避免实例化
  AppTranslation._();

  /// 翻译键值对
  static Map<String, Map<String, String>> translations = {
    'en_US': enUS,
    'zh_CN': zhCN,
  };
}

/// 英文翻译
final Map<String, String> enUS = {
  // 通用
  'app_name': 'SubFeeds',
  'please_login': 'Please login',
  'confirm': 'Confirm',
  'upgrade': 'Upgrade',
  'cancel': 'Cancel',
  'clear_cache': 'Clear Cache',
  'clear_cache_description': 'Are you sure to clear all cache data?',
  'clearing_cache': 'Clearing cache...',
  'cache_cleared_successfully': 'Cache cleared successfully',
  'cache_clear_failed': 'Failed to clear cache',
  'calculating': 'Calculating...',
  'unknown': 'Unknown',
  'search_feeds_hint': 'Search for feeds',
  'save': 'Save',
  'delete': 'Delete',
  'edit': 'Edit',
  'loading': 'Loading...',
  'or': 'OR',
  'error': 'Error',
  'retry': 'Retry',
  'success': 'Success',
  'warning': 'Warning',
  'notice': 'Notice',
  'login_expired': 'Login Expired',
  'login_expired_msg': 'Please login again',
  'search_history': 'Search History',
  'clear': 'Clear',
  'no_search_history': 'No search history',
  'no_more': 'No more data',
  'refresh': 'Refresh',
  'refresh_success': 'Refresh successful',
  'refresh_success_msg': 'Refresh successful',
  'splash_text': 'Gathering thousands of subscriptions, one stop access',
  'days_ago': 'd',
  'hours_ago': 'h',
  'minutes_ago': 'm',
  'login_view_more': "Login to view more",
  // 付费计划页
  'unlock_all_premium_features': 'Unlock All Premium Features',
  'pick_a_plan_that_fits': 'Pick a plan that fits your reading needs.',
  'yearly': 'Yearly',
  'monthly': 'Monthly',
  'popular': 'Popular',
  'per_month': 'per month',
  'per_year': 'per year',
  'continue': 'Continue',
  'select_plan': 'Select Plan',
  'plan_selected': 'Plan Selected',
  'selected_plan_processing': 'Processing subscription for: ',
  'subscription_auto_renew_notice':
      'Your subscription will automatically renew, cancel at any time. Your account will be charged 24 hours prior to renewal. By clicking "Continue" you agree to the Privacy Policy · Terms & Conditions · Restore Purchases',
  // 导航
  'nav_home': 'Home',
  'nav_discover': 'Discover',
  'nav_feeds': 'Feeds',
  'nav_settings': 'Setting',
  'nav_more': 'See More',
  'nav_footpoint': 'Footpoint',
  // 启动页
  'splash_loading': 'Loading...',

  // 引导页
  'onboarding_title_1': 'Welcome to SubFeeds',
  'onboarding_desc_1':
      'One-stop information aggregation, easily manage your RSS feeds',
  'onboarding_title_2': 'Personalized Reading',
  'onboarding_desc_2':
      'Customize your reading interface according to your preferences',
  'onboarding_title_3': 'Smart Recommendations',
  'onboarding_desc_3': 'AI recommends content you might be interested in',
  'onboarding_title_4': 'Read Anytime, Anywhere',
  'onboarding_desc_4':
      'Support offline reading, get information anytime, anywhere',
  'onboarding_skip': 'Skip',
  'onboarding_next': 'Next',
  'onboarding_start': 'Get Started',

  // 首页
  'home_recommended': 'News',
  'home_popular': 'Popular Articles',
  'home_latest': 'Latest Articles',
  'home_load_more': 'Load More',
  'home_loading': 'Loading...',
  'home_load_failed': 'Load failed, please try again',
  'home_no_more': 'No more data',
  'home_refresh_success': 'Refresh successful',
  'home_refresh_failed': 'Refresh failed',
  'home_release_to_load': 'Release to load more',
  'home_refreshing': 'Refreshing...',
  'home_refreshing_success': 'Refreshing successful',
  'home_refreshing_failed': 'Refreshing failed',
  'article_collect_success': 'Article collected successfully',
  'article_collect_failed': 'Article collection failed',
  'article_uncollect_success': 'Article uncollected successfully',
  'article_uncollect_failed': 'Article uncollection failed',
  'reddit_communities': 'Reddit Communities',
  'youtube_channels': 'Youtube Channels',
  'telegram_channels': 'Telegram Channels',
  // 发现页
  'discover_search_hint': 'Input RSS source link or title',
  'discover_categories': 'Categories',
  'discover_popular_feeds': 'Popular Feeds',
  'discover_trending': 'Trending',
  'discover_subscribe': 'Subscription',
  'discover_unsubscribe': 'Unsubscribe',
  'follow': 'Follow',
  'unfollow': 'Unfollow',
  'discover_subscribed': 'Subscribed',
  'discover_unsubscribed': 'Unsubscribed',
  'subscribe_success': 'Subscribe Success',
  'subscribe_failed': 'Subscribe Failed',
  'unsubscribe_success': 'Unsubscribe Success',
  'unsubscribe_failed': 'Unsubscribe Failed',
  'discover_title': 'Get real-time updates',
  'discover_subtitle':
      'Explore the latest news and stay on top of market trends.',
  'discover_category': 'Explore By Category',
  'discover_others': 'Follow WebSites',
  'discover_recommend_source': 'Recommend Source',
  'discover_no_recommend': 'No recommended content',
  'discover_unknown_date': 'Unknown date',
  'mark_all_as_read_success': 'Mark all as read success',
  'add_news': 'To Special News',
  // 订阅页
  'feeds_unread': 'Unread',
  'feeds_starred': 'Starred',
  'feeds_read_later': 'Read Later',
  'folder': 'Folder',
  'feeds_annotated': 'Notes',
  'feeds_add_new': 'Add New Feed',
  'to_new': 'Add New',
  'to_feeds': 'Add Feeds',
  'feeds_my_feeds': 'My Feeds',
  'feeds_uncategorized': 'Uncategorized',
  'feeds_manage': 'Manage Feeds',
  'feeds_no_feeds':
      'You haven\'t added any feeds yet. Start by adding your favorite topics!',
  'click here to subscribe': 'Click here to subscribe',
  'feeds_history': 'History',
  'add_feeds': 'To Add Feeds',
  'feeds_title': 'Feeds',
  'feeds_add_new_folder': 'Add New Folder',
  'click here to create a new folder': 'Click here to create a new folder',
  'create_folder': 'Create Folder',
  'folder_name': 'Folder Name',
  'please_input_folder_name': 'Please input folder name',
  'select_feeds': 'Select Feeds',
  'all_feeds': 'All Feeds',
  'search_feeds': 'Search Feeds',
  'folder_create_success': 'Folder created successfully',
  'folder_create_failed': 'Creation failed',
  'please_select_feeds': 'Please select at least one feed',
  'loading_more': 'Loading more...',
  'no_more_feeds': 'No more feeds',
  'move_success': 'Move successful',
  'move_failed': 'Move failed',
  'rename': 'Rename',
  'properties': 'Properties',
  'mark_all_as_read': 'Mark All as Read',
  'move_to_folder': 'Move to Folder',
  'rename_success': 'Rename successful',
  'rename_failed': 'Rename failed',
  'mark_read_success': 'Mark read successful',
  'operation_failed': 'Operation failed',
  'unfollow_success': 'Unfollow successful',
  'select_folder': 'Select Folder',
  'no_folders_available': 'No folders available',
  'enter_new_name': 'Enter new name',
  'enter_new_folder_name': 'Enter new folder name',
  'no_unread_articles': 'No unread articles',
  'are_you_sure_to_mark_all_as_read':
      'Are you sure to mark all articles as read?',
  'are_you_sure_to_unfollow': 'Are you sure to unfollow?',
  'are_you_sure_to_unfollow_folder': 'Are you sure to unfollow this folder?',
  'feed_url': 'Feed URL',
  'website': 'Website',
  'articles': 'Articles',
  'unread': 'Unread',
  'close': 'Close',
  'rename_feed': 'Rename Feed',
  'follow_date': 'Follow Date',
  'are_you_sure_to_delete_folder':
      'Are you sure to delete this folder? This action will cancel all feeds in this folder.',
  'delete_folder_failed': 'Delete folder failed',
  'delete_folder_success': 'Delete folder success',
  'operation_success': 'Operation success',

  // 编辑功能
  'edit_feeds': 'Edit Feeds',
  'edit_folders': 'Edit Folders',
  'select_all': 'Select All',
  'deselect_all': 'Deselect All',
  'are_you_sure_to_delete_feeds': 'Are you sure to delete these feeds?',
  'are_you_sure_to_delete_folders':
      'Are you sure to delete these folders? This action will cancel all feeds in these folders.',
  'folders': 'Folders',
  'unsubscribed_feeds': 'Unsubscribed Feeds',
  'delete_folders': 'Delete Folders',
  'are_you_sure_to_unsubscribe_feeds':
      'Are you sure to unsubscribe these feeds?',
  'delete_items': 'Delete Items',
  'are_you_sure_to_delete_mixed_items':
      'Are you sure to delete {folders} folders and unsubscribe {feeds} feeds?',
  'no_items_found': 'No items found',
  'ungrouped': 'Ungrouped',
  'export': 'Export',

  // 文章详情页
  'article_bookmark': 'Bookmark',
  'article_mark_read': 'Mark as Read',
  'article_mark_unread': 'Mark as Unread',
  'article_read_later': 'Read Later',
  'article_copy_link': 'Copy Link',
  'article_share': 'Share',
  'article_font_size': 'Font Size',
  'article_font_family': 'Font Family',
  'article_line_height': 'Line Height',
  'article_compact': 'Compact',
  'article_standard': 'Standard',
  'filter_by_date': 'Filter by Date',
  'filter_current_week': 'This Week',
  'filter_last_week': 'Last Week',
  'filter_two_weeks': 'Past Two Weeks',
  'filter_one_month': 'Past Month',
  'text_copied_to_clipboard': 'Text copied to clipboard',
  'article_comfortable': 'Comfortable',
  'article_reading_time': 'Reading Time',
  'article_reading_settings': 'Reading Settings',
  'link_copied_to_clipboard': 'Link copied to clipboard',
  'article_collect': 'Collect',
  'share_to': 'Share to',
  'article_theme': 'Theme',
  'share': 'Share',
  'share_selected_text': 'Share Selected Text',
  'no_text_selected': 'No text selected',
  'copy': 'Copy',
  'text_copied_use_system_share':
      'Text copied. Use system share for more options.',
  'article_ask_ai': 'Ask AI',
  'article_ask_ai_hint': 'Ask AI about the article',
  'article_ask_ai_success': 'Ask AI success',
  'article_ask_ai_failed': 'Ask AI failed',
  'article_ask_ai_no_text_selected': 'No text selected',
  'article_ask_ai_no_text_selected_msg': 'Please select text to ask AI',
  'request_timeout': 'Request timeout, please try again later',
  'notes': 'Notes',
  'notes_add': 'Add Note',
  'notes_edit': 'Edit Note',
  'notes_delete': 'Delete Note',
  'notes_delete_confirm': 'Are you sure to delete this note?',
  'notes_delete_success': 'Note deleted successfully',
  'notes_delete_failed': 'Note deletion failed',
  'notes_edit_success': 'Note edited successfully',
  'notes_edit_failed': 'Note editing failed',
  'notes_add_success': 'Note added successfully',
  'notes_add_failed': 'Note adding failed',
  'notes_no_notes': 'No notes yet',
  'add_your_note': 'Add your note...',
  'notes_delete_all': 'Delete All Notes',
  'notes_delete_all_confirm': 'Are you sure to delete all notes?',
  'notes_delete_all_success': 'All notes deleted successfully',
  'notes_delete_all_failed': 'All notes deletion failed',
  'no_notes_found': 'No notes found',
  'confirm_delete': 'Confirm Delete',
  'confirm_delete_note': 'Are you sure to delete this note?',
  'confirm_delete_note_success': 'Note deleted successfully',
  'confirm_delete_note_failed': 'Note deletion failed',
  'notes_list': 'Notes List',
  'read_all_articles_weeks': 'Great! You have read all this week\'s content',
  'check_all_articles': 'Check All Articles',
  // 设置页
  'settings_subscription': 'Subscription Plan',
  'settings_current_plan': 'Current Plan: Basic',
  'settings_upgrade': 'Upgrade',
  'settings_upgrade_tip': 'More plans are currently not open',
  'settings_theme_follow_system': 'Follow system',
  'settings_theme_light': 'Light',
  'settings_theme_dark': 'Dark',
  'settings_account': 'Account',
  'change_password': 'Change Password',
  'marking_all_as_read': 'Marking all as read',
  'img_loading_err': 'The picture is lost ~',
  'settings_personal_info': 'Account and Security',
  'settings_data_sync': 'Data Sync',
  'settings_backup': 'Backup & Restore',
  'settings_preferences': 'Preferences',
  'settings_dark_mode': 'Dark Mode',
  'settings_language': 'Language',
  'settings_font_size': 'Font Size',
  'settings_font_family': 'Font Family',
  'settings_notifications': 'Notifications',
  'settings_help': 'Help & Support',
  'settings_help_center': 'Help Center',
  'settings_feedback': 'Feedback',
  'settings_rate': 'Rate App',
  'settings_logout': 'Logout',
  "invalid_email": 'Invalid email',
  'settings_small': 'Small',
  'settings_medium': 'Medium',
  'settings_large': 'Large',
  'settings_theme': 'Theme',
  'pricing_unopen': 'Paid plans are not yet available, so stay tuned',
  'settings_light_mode': 'Light Mode',
  'settings_logout_description': 'Reset your login information',
  'settings_login': 'Login',
  'settings_login_description': 'Login to experience more features',

  'settings_rate_description': 'Rate the app',
  'settings_personal_info_description': 'Username, email',
  'login_with_password': "Login with password",
  'login_with_verification_code': 'Login with verification code',
  // 个人信息页面
  'personal_info_title': 'Personal Information',
  'personal_info_avatar': 'Avatar',
  'personal_info_name': 'Name',
  'personal_info_email': 'Email',
  'personal_info_change_avatar': 'Change Avatar',
  'personal_info_edit_name': 'Edit Name',
  'personal_info_password': 'Password',
  'personal_info_set_password': 'Set',
  'settings_logout_confirm': 'Are you sure you want to log out ?',
  'personal_info_change_password': 'Change',
  'personal_info_old_password': 'Old Password',
  'regenerate': 'Regenerate',
  'personal_info_new_password': 'New Password',
  'personal_info_confirm_password': 'Confirm Password',
  'personal_info_password_success': 'Password changed successfully',
  'personal_info_password_error': 'Password change failed',
  'personal_info_password_not_match':
      'New password and confirm password do not match',
  'personal_info_password_empty': 'Password cannot be empty',
  'personal_info_password_rule':
      '8+ chars, upper & lower case, numbers required',
  'personal_info_save': 'Save',
  'personal_info_success': 'Updated successfully',
  'personal_info_error': 'Update failed',
  'personal_info_phone': 'Phone',
  'personal_info_last_online': 'Last Online',

  // 登录页面
  'login_title': 'Login',
  'login_welcome_back': 'Log in SubFeeds',
  'login_subtitle': 'Please login to continue',
  'login_email': 'Email',
  'login_email_hint': 'Enter your email',
  'login_password': 'Password',
  'login_password_hint': 'Enter your password',
  'login_remember_me': 'Remember Me',
  'login_forgot_password': 'Forgot Password?',
  'login_button': 'Login',
  'login_no_account': '',
  'login_sign_up': 'Sign Up Now',
  'login_or_continue_with': 'Or continue with',
  'update_time': 'Updated Time',
  'login_google': 'Continue with Google',
  'login_apple': 'Continue with Apple',
  'login_apple_not_available': 'Apple Sign In Not Available',
  'login_failed': 'Login Failed',

  // 登录选择页面
  'login_selection_sign_up': 'Sign Up',
  'login_selection_sign_up_error': 'Unable to open registration page',
  'login_selection_login': 'Log In',
  'no_password': 'no password',
  'no_password_msg': 'Please enter password',
  'account_expired': 'account expired',
  'account_expired_msg':
      'Account does not exist or is frozen, please contact customer service',
  'login_selection_login_error': 'Unable to open login page',
  'login_selection_or': 'OR',
  'login_selection_modern_rss_reader':
      'Customize your feed and stay updated on what matters',
  'login_selection_error': 'Error',
  'search_results': 'Search Results:',
  'new_feeds': 'Newsfeeds',
  'login_selection_retry_later': 'Login failed, please try again later',

  // 注册页面
  'register_title': 'Register',
  'register_create_account': 'Create Account',
  'just_now': '刚刚',
  'register_subtitle': 'Please fill in the following information',
  'register_name': 'Name',
  'register_name_hint': 'Enter your name',
  'register_email': 'Email',
  'register_email_hint': 'Enter your email',
  'register_password': 'Password',
  'register_password_hint': 'Enter your password',
  'register_confirm_password': 'Confirm Password',
  'register_confirm_password_hint': 'Enter your password again',
  'register_verify_code': 'Verification Code',
  'register_verify_code_hint': 'Enter verification code',
  'register_get_code': 'Get Code',
  'register_retry_seconds': 'Retry in %s seconds',
  'add': 'Add',
  'register_terms_agree': 'I have read and agree to the',
  'register_terms': 'Terms of Service',
  'register_and': 'and',
  'please_subscribe_to_view_more': 'Please subscribe to view more',
  'register_privacy': 'Privacy Policy',
  'register_button': 'Register',
  'register_have_account': 'Already have an account?',
  'register_login': 'Login Now',
  'register_error': 'Error',
  'register_failed': 'Registration failed',
  'avatar_size_error': 'Avatar size cannot exceed 5MB',
  'register_success': 'Registration successful',
  'send_verify_code_success': 'Verification code sent to your email',
  'send_verify_code_failed': 'Verification code sending failed',
  'launch_email_failed': 'Failed to launch email client',
  'launch_url_failed': 'Failed to launch URL',
  'cancel_account': 'Cancel account',
  'cancel_account_description':
      'After canceling the account, all data in the account will be deleted and cannot be restored. Are you sure you want to cancel the account?',
  'Operation_successful': 'Operation successful',
  'account_delete_success': "Successfully cancelled",
  // 忘记密码页面
  'forgot_password_title': 'Forgot Password',
  'forgot_password_reset': 'Reset Password',
  'iframe_content': 'Content cannot be displayed, please click to view',
  'check_content': 'Check Content',
  'forgot_password_subtitle': 'Follow the steps below to reset your password',
  'forgot_password_step_1': 'Verify Email',
  'forgot_password_step_2': 'Set New Password',
  'forgot_password_email': 'Email',
  'forgot_password_email_hint': 'Enter your registered email',
  'forgot_password_verify_code': 'Verification Code',
  'forgot_password_verify_code_hint': 'Enter verification code',
  'forgot_password_get_code': 'Get Code',
  'forgot_password_retry_seconds': 'Retry in %s seconds',
  'forgot_password_next': 'Next',
  'forgot_password_new_password': 'New Password',
  'forgot_password_new_password_hint': 'Enter new password',
  'forgot_password_confirm_password': 'Confirm Password',
  'forgot_password_confirm_password_hint': 'Enter new password again',
  'forgot_password_submit': 'Reset Password',
  'forgot_password_back_login': 'Back to Login',
  'forgot_password_error': 'Error',
  'forgot_password_failed': 'Password reset failed',
  'forgot_password_success': 'Password reset successful',
  'please_input_email': 'Please input email',
  'please_input_name': 'Please input name',
  'please_input_password': 'Please input password',
  'please_input_verify_code': 'Please input verification code',
  'content_tip':
      'Due to relevant regulations, relevant content cannot be displayed in the app, click the button below to see more',
  'please_input_old_password': 'Please input old password',
  'please_input_new_password': 'Please input new password',
  'no_articles_found': 'No articles found, subscribe for more content',
  'please_input_confirm_password': 'Please input confirm password',
  'other_subscribers_also_liked': 'Other Subscribers Also Liked',
  // 搜索页面
  'results': 'Results',
  'found': 'found',
  'no_results': 'Hm... we couldn\'t find any results',
  'search_hint': 'Enter keywords or a site for updates',
  'search': 'Search',
  'search_feeds_placeholder': 'Search feeds by name...',
  // 社交媒体平台
  'platform_facebook': 'Facebook',
  'platform_twitter': 'X (Twitter)',
  'platform_instagram': 'Instagram',
  'platform_linkedin': 'LinkedIn',
  'platform_pinterest': 'Pinterest',
  'platform_telegram': 'Telegram',
  'platform_reddit': 'Reddit',
  'platform_whatsapp': 'WhatsApp',
  'platform_more': 'More',
  'search_title': 'Search - ',
  'search_rules': 'Search Rules',
  'search_examples': 'Search Examples',
  'channel_search': 'Channel Search',
  'url_search': 'URL Search',
  'reddit_search': 'Reddit Search',
  'keyword_search': 'Keyword Search',
  'no_history': 'No History',
  'start_conversation_history':
      'Start a conversation with AI, history will be displayed here',
  'stop_generation': 'Stop Generation',
  'image_load_failed': 'Image failed to load',

  // 图片操作
  'image_actions': 'Image Actions',
  'choose_action_for_image': 'Choose an action for this image',
  'save_to_gallery': 'Save to Gallery',
  'share_image': 'Share Image',
  'saving_image': 'Saving image...',
  'preparing_share': 'Preparing to share...',
  'image_saved_successfully': 'Image saved successfully',
  'image_shared_successfully': 'Image shared successfully',
  'failed_to_save_image': 'Failed to save image',
  'failed_to_share_image': 'Failed to share image',
  'failed_to_download_image': 'Failed to download image',
  'storage_permission_denied': 'Storage permission denied',
  'shared_from_subfeeds': 'Shared from SubFeeds',
  'added_to_read_later': 'Added to Read Later',
  'removed_from_read_later': 'Removed from Read Later',
  'added_to_bookmarks': 'Added to Bookmarks',
  'removed_from_bookmarks': 'Removed from Bookmarks',

  'delete_conversation': 'Delete Conversation',
  'confirm_delete_conversation':
      'Are you sure you want to delete the conversation {title}? This action cannot be undone.',
  'conversation_loaded': 'Loaded conversation about: {title}',
  'discussing_article': 'Discussing article:',
  'history': 'History',
  'failed_to_load_conversation': 'Failed to load conversation',
  'telegram_channel_search': 'Telegram Channel Search',
  'youtube_description':
      'Find YouTube videos and channels with advanced search options',
  'reddit_description': 'Find Reddit communities and posts',
  'google_news_description': 'Find the latest news from Google News',
  'telegram_description':
      'Find Telegram channels and content with advanced search options',
  'youtube_channel_example':
      'Use @ channel name to search for a specific channel, for example:@ livespeedy7451',
  'youtube_url_example':
      'Use url to search for a specific channel\nhttps://www.youtube.com/ + channel name. For example: https://www.youtube.com/@livespeedy7451\nhttp://youtube.com/feeds/videos.xml?channel_id= + channel id. For example: http://youtube.com/feeds/videos.xml?channel_id=UC2bW_AY9BlbYLGJSXAbjS4Q',
  'reddit_url_example':
      'Support direct use of URL search. For example: https://www.reddit.com/r/malaysia',
  'google_news_keyword_example':
      'Enter keywords to search for relevant news articles',
  'telegram_channel_example':
      'Use keywords to search for videos from a specific channel',
  'telegram_url_example':
      'Use \'https://t.me/{Telegram Channel}\' to search for a specific channel, example: https://t.me/s/Durov',
  'recommend_bottom_sheet_title': 'Follow the website you are interested in',
  'recommend_bottom_sheet_description':
      'After following, you can check Ta updates anytime and anywhere',
  'refresh_recommend_feeds': 'Try A Different One',
  'updated_at': 'Updated At',
  'no_update_time': 'No Update Time',
  'no_recommend_feeds': 'No recommended feeds',
  'personal_info_password_updated': 'Password updated successfully',
  'personal_info_password_set': 'Password set successfully',

  // 新手引导
  'tutorial_swipe_title': 'Swipe to Navigate',
  'tutorial_swipe_description':
      'Swipe left or right to switch between different content pages: Starred, Read Later, History, and Annotated.',
  'tutorial_got_it': 'Got it!',

  // 下拉刷新相关
  'pull_to_refresh': 'Pull down to refresh',
};

/// 中文翻译
final Map<String, String> zhCN = {
  // 通用
  'app_name': 'SubFeeds',
  'confirm': '确认',
  'upgrade': '升级',
  'other_subscribers_also_liked': '其他订阅者也喜欢',
  'clear_cache': '清除缓存',
  'clear_cache_description': '确定要清除所有缓存数据吗？',
  'clearing_cache': '正在清除缓存...',
  'cache_cleared_successfully': '缓存清除成功',
  'cache_clear_failed': '缓存清除失败',
  'calculating': '计算中...',
  'unknown': '未知',
  'please_login': '请登录',
  'cancel': '取消',
  'save': '保存',
  'delete': '删除',
  'rename': '重命名',
  'search_feeds_hint': '搜索订阅源',
  'edit': '编辑',
  'loading': '加载中...',
  'or': '或',
  'error': '错误',
  'retry': '重试',
  'success': '成功',
  'warning': '警告',
  'notice': '提示',
  'login_expired': '登录过期',
  'login_expired_msg': '请重新登录',
  'refreshing': '刷新中...',
  'search_history': '搜索历史',
  'login_with_verification_code': '使用验证码登录',
  'clear': '清除',
  'no_search_history': '无搜索历史',
  'no_more': '没有更多数据',
  'refresh': '刷新',
  'refresh_success': '刷新成功',
  'refresh_success_msg': '刷新成功',
  'splash_text': '聚合上千订阅源，一站式获取信息',
  'days_ago': '天前',
  'hours_ago': '小时前',
  'minutes_ago': '分钟前',
  'login_view_more': "登录查看更多",
  // 付费计划页
  'unlock_all_premium_features': '解锁所有高级功能',
  'pick_a_plan_that_fits': '选择适合您阅读需求的计划',
  'yearly': '年度计划',
  'monthly': '月度计划',
  'regenerate': '重新生成',
  'popular': '热门',
  'per_month': '每月',
  'per_year': '每年',
  'continue': '继续',
  'select_plan': '选择计划',
  'img_loading_err': '图片加载失败 ~',
  'plan_selected': '已选择计划',
  'selected_plan_processing': '正在处理订阅：',
  'subscription_auto_renew_notice':
      '您的订阅将自动续订，随时可以取消。您的账户将在续订前24小时被扣款。点击"继续"即表示您同意隐私政策·服务条款·恢复购买',
  // 导航
  'nav_home': '推荐',
  'nav_discover': '发现',
  'nav_feeds': '订阅',
  'to_new': '特殊订阅',
  'to_feeds': '添加订阅',
  'nav_settings': '设置',
  'nav_footpoint': '足迹',
  // 启动页
  'splash_loading': '加载中...',
  // 引导页
  'onboarding_title_1': '欢迎使用SubFeeds',
  'onboarding_desc_1': '一站式获取多渠道信息，轻松管理你的RSS订阅源',
  'onboarding_title_2': '个性化阅读体验',
  'onboarding_desc_2': '根据个人喜好定制阅读界面，提供舒适的阅读体验',
  'onboarding_title_3': '智能内容推荐',
  'onboarding_desc_3': 'AI智能推荐你可能感兴趣的内容，发现更多精彩',
  'onboarding_title_4': '随时随地阅读',
  'onboarding_desc_4': '支持离线阅读，让你随时随地都能获取信息',
  'onboarding_skip': '跳过',
  'onboarding_next': '下一步',
  'onboarding_start': '开始使用',
  'avatar_size_error': '头像大小不能超过5MB',
  // 首页
  'home_recommended': '新闻',
  'home_popular': '热门文章',
  'home_latest': '最新文章',
  'personal_info_set_password': '设置密码',
  'home_load_more': '加载更多',
  'home_loading': '加载中...',
  'home_load_failed': '加载失败，请重试',
  'home_no_more': '没有更多数据了',
  'home_refresh_success': '刷新成功',
  'home_refresh_failed': '刷新失败',
  'nav_more': '查看更多',
  'home_release_to_load': '下拉加载更多',
  'home_refreshing': '刷新中...',
  'home_refreshing_success': '刷新成功',
  'home_refreshing_failed': '刷新失败',
  'article_collect_success': '文章收藏成功',
  'search_results': '相关文章:',
  'articles': '文章数',
  'article_collect_failed': '文章收藏失败',
  'article_uncollect_success': '文章取消收藏成功',
  'article_uncollect_failed': '文章取消收藏失败',
  // 发现页
  'discover_search_hint': '输入RSS源链接或标题',
  'discover_categories': '分类',
  'discover_popular_feeds': '热门订阅源',
  'discover_trending': '趋势',
  'discover_subscribe': '订阅',
  'discover_unsubscribe': '取消订阅',
  'follow': '订阅',
  'unfollow': '取消订阅',
  'discover_subscribed': '已订阅',
  'no_password': '没有密码',
  'no_password_msg': '请输入密码',
  'account_expired': '帐号异常',
  'stop_generation': '停止生成',
  'image_load_failed': '图片加载失败',

  // 图片操作
  'image_actions': '图片操作',
  'choose_action_for_image': '选择对此图片的操作',
  'save_to_gallery': '保存到相册',
  'share_image': '分享图片',
  'saving_image': '正在保存图片...',
  'preparing_share': '准备分享...',
  'image_saved_successfully': '图片保存成功',
  'image_shared_successfully': '图片分享成功',
  'failed_to_save_image': '保存图片失败',
  'failed_to_share_image': '分享图片失败',
  'failed_to_download_image': '下载图片失败',
  'storage_permission_denied': '存储权限被拒绝',
  'shared_from_subfeeds': '来自 SubFeeds 的分享',
  'added_to_read_later': '已添加到稍后阅读',
  'removed_from_read_later': '已从稍后阅读中移除',
  'added_to_bookmarks': '已添加到收藏',
  'removed_from_bookmarks': '已从收藏中移除',

  'account_expired_msg': '帐号不存在或被冻结，请联系客服',
  'discover_unsubscribed': '未订阅',
  'subscribe_success': '订阅成功',
  'subscribe_failed': '订阅失败',
  'unsubscribe_success': '取消订阅成功',
  'unsubscribe_failed': '取消订阅失败',
  'check_content': '查看内容',
  'discover_title': '获取实时更新',
  'discover_subtitle': '探索最新资讯，掌握市场动态',
  'discover_category': '按类别搜索',
  'discover_others': '按照网站搜索',
  'discover_recommend_source': '推荐订阅源',
  'discover_no_recommend': '暂无推荐内容',
  'no_articles_found': '没有找到任何文章,订阅后查看更多内容',
  'discover_unknown_date': '未知日期',
  'add_news': '订阅渠道',
  // 订阅页
  'feeds_unread': '未读',
  'feeds_starred': '收藏',
  'feeds_read_later': '稍后阅读',
  'feeds_annotated': '笔记',
  'feeds_add_new': '添加订阅源',
  'no_history': '暂无历史记录',
  'start_conversation_history': '开始与AI对话后，历史记录将显示在这里',
  'delete_conversation': '删除对话',
  'confirm_delete_conversation': '确定要删除对话{title}吗？此操作无法撤销。',
  'conversation_loaded': '已加载关于文章的对话：{title}',
  'discussing_article': '正在讨论文章：',
  'history': '历史',
  'failed_to_load_conversation': '加载对话失败',
  'feeds_my_feeds': '我的订阅',
  'feeds_uncategorized': '未分类',
  'feeds_manage': '管理订阅',
  'feeds_no_feeds': '你还没有添加任何订阅源。开始添加你感兴趣的Feeds吧！',
  'click here to subscribe': '点击这里订阅',
  'feeds_history': '历史',
  'add_feeds': '添加订阅',
  'feeds_title': '订阅',
  'feeds_add_new_folder': '添加新文件夹',
  'click here to create a new folder': '点击这里创建新文件夹',
  'create_folder': '创建文件夹',
  'folder_name': '文件夹名称',
  'please_input_folder_name': '请输入文件夹名称',
  'select_feeds': '选择订阅源',
  'all_feeds': '全部订阅',
  'search_feeds': '搜索订阅源',
  'folder_create_success': '文件夹创建成功',
  'change_password': '修改密码',
  'folder_create_failed': '创建失败',
  'please_select_feeds': '请选择至少一个订阅源',
  'loading_more': '加载更多...',
  'no_more_feeds': '没有更多订阅源了',
  'move_success': '移动成功',
  'move_failed': '移动失败',
  'folder': '文件夹',
  'properties': '属性',
  'mark_all_as_read': '标记全部已读',
  'move_to_folder': '移动到文件夹',
  'rename_success': '重命名成功',
  'rename_failed': '重命名失败',
  'mark_read_success': '标记已读成功',
  'operation_failed': '操作失败',
  'unfollow_success': '取消订阅成功',
  'select_folder': '选择文件夹',
  'no_folders_available': '暂无可用文件夹',
  'enter_new_name': '输入新名称',
  'enter_new_folder_name': '输入新文件夹名称',
  'no_unread_articles': '没有未读文章',
  'are_you_sure_to_mark_all_as_read': '确定要将所有文章标记为已读吗？',
  'are_you_sure_to_unfollow': '确定要取消订阅此源吗？',
  'are_you_sure_to_unfollow_folder': '确定要取消订阅此文件夹中的所有源吗？',
  'feed_url': '订阅源地址',
  'website': '网站',
  'update_time': '更新时间',
  'unread': '未读数',
  'close': '关闭',
  'rename_feed': '重命名订阅源',
  'follow_date': '订阅时间',
  'are_you_sure_to_delete_folder': '确定要删除此文件夹吗？将会取消文件夹中的所有订阅源',
  'delete_folder_failed': '删除文件夹失败',
  'launch_email_failed': '无法打开邮件客户端',
  'launch_url_failed': '无法打开链接',
  'delete_folder_success': '删除文件夹成功',
  'operation_success': '操作成功',
  'new_feeds': '订阅新闻',
  'cannot_open_url': '无法打开链接',
  'copy_failed': '复制失败',
  'share_failed': '分享失败',
  'link_not_available': '链接不可用',
  'link_copied_to_clipboard': '链接已复制到剪贴板',
  'link_copied_to_clipboard_failed': '复制链接失败',
  'link_copied_to_clipboard_success': '链接已复制到剪贴板',
  'link_copied_to_clipboard_success_msg': '链接已复制到剪贴板',
  'link_copied_to_clipboard_failed_msg': '复制链接失败',
  'please_input_email': '请输入邮箱',
  'please_input_name': '请输入昵称',
  'please_input_password': '请输入密码',
  'please_input_verify_code': '请输入验证码',
  'please_input_old_password': '请输入原密码',
  'please_input_new_password': '请输入新密码',
  'please_input_confirm_password': '请输入确认密码',

  // 编辑功能
  'edit_feeds': '编辑订阅',
  'edit_folders': '编辑文件夹',
  'select_all': '全选',
  'deselect_all': '取消全选',
  'are_you_sure_to_delete_feeds': '确定要删除这些订阅源吗？',
  'are_you_sure_to_delete_folders': '确定要删除这些文件夹吗？此操作将取消文件夹中的所有订阅源。',
  'folders': '文件夹',
  'unsubscribed_feeds': '未订阅源',
  'delete_folders': '删除文件夹',
  'are_you_sure_to_unsubscribe_feeds': '确定要取消订阅这些订阅源吗？',
  'delete_items': '删除项目',
  'are_you_sure_to_delete_mixed_items':
      '确定要删除 {folders} 个文件夹并取消订阅 {feeds} 个订阅源吗？',
  'no_items_found': '未找到项目',
  'ungrouped': '未分组',
  'export': '导出',

  // 文章详情页
  'article_bookmark': '收藏',
  'article_mark_read': '标记为已读',
  'article_mark_unread': '标记为未读',
  'article_read_later': '稍后阅读',
  'article_copy_link': '复制链接',
  'article_share': '分享',
  'article_font_size': '字体大小',
  'article_font_family': '字体选择',
  'article_line_height': '行间距',
  'article_compact': '紧凑',
  'article_standard': '标准',
  'filter_by_date': '按日期筛选',
  'filter_current_week': '本周文章',
  'filter_last_week': '上周文章',
  'filter_two_weeks': '过去两周文章',
  'filter_one_month': '过去一个月文章',
  'text_copied_to_clipboard': '文本已复制到剪贴板',
  'article_comfortable': '宽松',
  'article_reading_time': '阅读时长',
  'article_reading_settings': '阅读设置',
  'article_collect': '收藏',
  'share_to': '分享到',
  'article_theme': '主题',
  'share': '分享',
  'share_selected_text': '分享选中文本',
  'no_text_selected': '未选中文本',
  'copy': '复制',
  'text_copied_use_system_share': '文本已复制。使用系统分享功能获取更多选项。',
  'article_ask_ai': '问AI',
  'article_ask_ai_hint': '问AI关于这篇文章',
  'article_ask_ai_success': '问AI成功',
  'article_ask_ai_failed': '问AI失败',
  'article_ask_ai_no_text_selected': '未选中文本',
  'article_ask_ai_no_text_selected_msg': '请选择文本进行问AI',
  'request_timeout': '请求超时，请稍后重试',
  'notes': '笔记',
  'notes_add': '添加笔记',
  'add': '添加',

  'notes_edit': '编辑笔记',
  'notes_delete': '删除笔记',
  'notes_delete_confirm': '确定要删除此笔记吗？',
  'just_now': '刚刚',
  'notes_delete_success': '笔记删除成功',
  'notes_delete_failed': '笔记删除失败',
  'notes_edit_success': '笔记编辑成功',
  'notes_edit_failed': '笔记编辑失败',
  'please_subscribe_to_view_more': '请订阅以查看更多',
  'notes_add_success': '笔记添加成功',
  'notes_add_failed': '笔记添加失败',
  'notes_no_notes': '暂无笔记',
  'notes_delete_all': '删除所有笔记',
  'notes_delete_all_confirm': '确定要删除所有笔记吗？',
  'notes_delete_all_success': '所有笔记删除成功',
  'notes_delete_all_failed': '所有笔记删除失败',
  'notes_delete_all_confirm_msg': '所有笔记删除后无法恢复，确定要删除所有笔记吗？',
  'add_your_note': '添加你的笔记...',
  'no_notes_found': '暂无笔记',
  'confirm_delete_note': '确定要删除此笔记吗？',
  'confirm_delete_note_success': '笔记删除成功',
  'confirm_delete_note_failed': '笔记删除失败',
  'settings_logout_confirm': '你确定要退出登陆吗？',
  'confirm_delete_note_msg': '笔记删除后无法恢复，确定要删除此笔记吗？',
  'notes_list': '笔记列表',
  'read_all_articles_weeks': '恭喜！你已经阅读了本周的所有内容',
  'check_all_articles': '查看所有文章',
  'mark_all_as_read_success': '标记全部已读成功',
  // 设置页
  'settings_subscription': '订阅计划',
  'settings_current_plan': '当前计划: 基础版',
  'settings_upgrade': '升级',
  'settings_upgrade_tip': '付费计划暂未开放',
  'settings_theme_follow_system': '跟随系统',
  'settings_theme_light': '浅色',
  'settings_theme_dark': '深色',
  'settings_account': '账户',
  'settings_personal_info': '账户与安全',
  'settings_data_sync': '数据同步',
  'settings_backup': '备份与恢复',
  'settings_preferences': '偏好设置',
  'settings_dark_mode': '深色模式',
  'settings_language': '语言',
  'settings_font_size': '字体大小',
  'settings_font_family': '字体选择',
  'settings_notifications': '通知设置',
  'settings_help': '帮助与支持',
  'settings_help_center': '帮助中心',
  'settings_feedback': '反馈',
  'settings_rate': '应用评分',
  'settings_logout': '退出登录',
  'settings_small': '小号',
  'settings_medium': '中号',
  'settings_large': '大号',
  'settings_theme': '主题',
  'settings_lang': '语言',
  'settings_fontsize': '字体大小',
  'settings_fontfamily': '字体选择',
  'settings_light_mode': '浅色模式',
  'settings_logout_description': '重置你的登陆信息',
  'settings_rate_description': '在应用商店评分',
  'settings_personal_info_description': '用户名、邮箱等',
  'reddit_communities': 'Reddit 社区',
  'youtube_channels': 'Youtube 频道',
  'telegram_channels': 'Telegram 频道',
  'settings_login': '登录',
  'settings_login_description': '登录体验更多功能',
  'cancel_account': '注销账号',
  'cancel_account_description': '注销账号后，该账号里所有数据将被删除，并且无法恢复，是否确定注销账号？',
  // 个人信息页面
  'personal_info_title': '个人信息',
  'personal_info_avatar': '头像',
  'personal_info_name': '用户名',
  'personal_info_email': '邮箱',
  'personal_info_change_avatar': '更换头像',
  'personal_info_edit_name': '编辑用户名',
  'personal_info_password': '密码',
  'content_tip': '因为相关规定，相关内容无法在app内显示，点击下方按钮查看更多',
  'personal_info_change_password': '修改',
  'personal_info_old_password': '原密码',
  'personal_info_new_password': '新密码',
  'personal_info_confirm_password': '确认密码',
  'personal_info_password_rule': '大与8个字符，包括大小写字母和数字',
  'pricing_unopen': '付费计划暂未开放，敬请期待',
  'personal_info_save': '保存',
  'personal_info_success': '更新成功',
  'personal_info_error': '更新失败',
  'marking_all_as_read': '正在标记全部已读',
  'personal_info_phone': '手机号码',
  'personal_info_last_online': '最后在线',
  'iframe_content': '内容无法显示，请点击查看',
  'personal_info_password_success': '密码修改成功',
  'personal_info_password_error': '密码修改失败',
  'personal_info_password_not_match': '新密码和确认密码不匹配',
  'personal_info_password_empty': '密码不能为空',
  'login_with_password': "使用密码登陆",
  'personal_info_password_updated': '密码修改成功',
  'personal_info_password_set': '密码设置成功',

  // 登录页面
  'login_title': '登录',
  'login_welcome_back': '登录SubFeeds',
  'login_subtitle': '请登录您的账号继续使用',
  "invalid_email": '不合法的邮箱',
  'login_email': '邮箱',
  'login_email_hint': '请输入您的邮箱',
  'login_password': '密码',
  'login_password_hint': '请输入您的密码',
  'login_remember_me': '记住密码',
  'login_forgot_password': '忘记密码?',
  'login_button': '登录',
  'login_no_account': '还没有账号?',
  'login_sign_up': '立即注册',
  'login_or_continue_with': '或者使用以下方式登录',
  'login_google': '使用Google账号登录',
  'login_apple': '使用Apple账号登录',
  'login_apple_not_available': 'Apple登录不可用',
  'login_failed': '登录失败,请检查邮箱是否正确',

  // 登录选择页面
  'login_selection_sign_up': '注 册',
  'login_selection_sign_up_error': '无法打开注册页面',
  'login_selection_login': '登 录',
  'login_selection_login_error': '无法打开登录页面',
  'login_selection_or': '或',
  'login_selection_error': '错误',
  'login_selection_modern_rss_reader': '定制你的信息流，及时更新重要的信息',
  'login_selection_retry_later': '登录失败，请稍后重试',

  // 注册页面
  'register_title': '注册',
  'register_create_account': '创建账号',
  'register_subtitle': '请填写以下信息完成注册',
  'register_name': '姓名',
  'register_name_hint': '请输入您的姓名',
  'register_email': '邮箱',
  'register_email_hint': '请输入您的邮箱',
  'register_password': '密码',
  'register_password_hint': '请输入密码',
  'register_confirm_password': '确认密码',
  'register_confirm_password_hint': '请再次输入密码',
  'register_verify_code': '验证码',
  'register_verify_code_hint': '请输入验证码',
  'register_get_code': '获取验证码',
  'register_retry_seconds': '%s秒后重试',
  'register_terms_agree': '我已阅读并同意',
  'register_terms': '用户协议',
  'register_and': '和',
  'register_privacy': '隐私政策',
  'register_button': '注册',
  'register_have_account': '已有账号?',
  'register_login': '立即登录',
  'register_error': '错误',
  'register_failed': '注册失败',
  'register_success': '注册成功',
  'send_verify_code_success': '验证码已发送到您的邮箱',
  'send_verify_code_failed': '验证码发送失败',
  // 忘记密码页面
  'forgot_password_title': '找回密码',
  'forgot_password_reset': '重置密码',
  'forgot_password_subtitle': '请按照以下步骤重置您的密码',
  'forgot_password_step_1': '验证邮箱',
  'forgot_password_step_2': '设置新密码',
  'forgot_password_email': '邮箱',
  'forgot_password_email_hint': '请输入您的注册邮箱',
  'forgot_password_verify_code': '验证码',
  'forgot_password_verify_code_hint': '请输入验证码',
  'forgot_password_get_code': '获取验证码',
  'forgot_password_retry_seconds': '%s秒后重试',
  'forgot_password_next': '下一步',
  'forgot_password_new_password': '新密码',
  'forgot_password_new_password_hint': '请输入新密码',
  'forgot_password_confirm_password': '确认密码',
  'forgot_password_confirm_password_hint': '请再次输入新密码',
  'forgot_password_submit': '重置密码',
  'forgot_password_back_login': '返回登录',
  'forgot_password_error': '错误',
  'forgot_password_failed': '密码重置失败',
  'forgot_password_success': '密码重置成功',
  'Operation_successful': '操作成功',
  'account_delete_success': "已成功注销",

  // 搜索页面
  'results': '搜索结果',
  'found': '个结果',
  'no_results': '抱歉...没有找到相关内容',
  'search_hint': '输入关键词或网站进行搜索',
  'search': '搜索',
  'search_feeds_placeholder': '按订阅源名称搜索...',
  // 社交媒体平台
  'platform_facebook': 'Facebook',
  'platform_twitter': 'X (Twitter)',
  'platform_instagram': 'Instagram',
  'platform_linkedin': 'LinkedIn',
  'platform_pinterest': 'Pinterest',

  'platform_telegram': 'Telegram',
  'platform_reddit': 'Reddit',
  'platform_whatsapp': 'WhatsApp',
  'platform_more': '更多',
  'search_title': '搜索 - ',
  'search_rules': '搜索规则',
  'search_examples': '搜索示例',
  'channel_search': '频道搜索',
  'url_search': 'URL搜索',
  'reddit_search': 'Reddit搜索',
  'keyword_search': '关键词搜索',
  'telegram_channel_search': 'Telegram频道搜索',
  'youtube_description': '使用高级搜索选项查找YouTube视频和频道',
  'reddit_description': '查找Reddit社区和帖子',
  'google_news_description': '查找Google News的最新新闻',
  'telegram_description': '使用高级搜索选项查找Telegram频道和内容',
  'youtube_channel_example': '使用@频道名称搜索特定频道，例如：@livespeedy7451',
  'youtube_url_example':
      '使用URL搜索特定频道\nhttps://www.youtube.com/ + channel name. For example: https://www.youtube.com/@livespeedy7451\nhttp://youtube.com/feeds/videos.xml?channel_id= + channel id. For example: http://youtube.com/feeds/videos.xml?channel_id=UC2bW_AY9BlbYLGJSXAbjS4Q',
  'reddit_url_example': '支持直接使用URL搜索。例如：https://www.reddit.com/r/malaysia',
  'google_news_keyword_example': '输入关键词搜索相关新闻文章',
  'telegram_channel_example': '使用关键词搜索特定频道的视频',
  'telegram_url_example':
      '使用\'https://t.me/{Telegram频道}\'搜索特定频道，例如：https://t.me/s/Durov',
  'no_recommend_feeds': '暂无推荐订阅源',
  'recommend_bottom_sheet_title': '订阅你感兴趣的网站',
  'recommend_bottom_sheet_description': '订阅后，你可以在任何地方随时查看更新',
  'refresh_recommend_feeds': '试试其他订阅源',
  'refreshing_recommend_feeds': '刷新中...',
  'updated_at': '更新于',
  'no_update_time': '暂无更新时间',

  // 新手引导
  'tutorial_swipe_title': '左右滑动切换页面',
  'tutorial_swipe_description': '左右滑动可以在不同内容页面间切换：收藏、稍后阅读、历史记录和笔记。',
  'tutorial_got_it': '我知道了',

  // 下拉刷新相关
  'pull_to_refresh': '下拉刷新',
};
