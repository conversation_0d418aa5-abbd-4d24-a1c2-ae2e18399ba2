import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 主题控制器，负责管理应用的主题模式
class ThemeController extends GetxController {
  // 存储实例
  final GetStorage _storage = GetStorage();

  // 主题模式键
  static const String _themeKey = 'theme_mode';

  // 是否为暗黑模式
  final RxBool isDarkMode = false.obs;

  // 当前主题模式
  final Rx<ThemeMode> currentThemeMode = ThemeMode.system.obs;

  // 主题切换动画的触发位置（用于涟漪效果）
  final Rx<Offset?> themeTogglePosition = Rx<Offset?>(null);

  @override
  void onInit() {
    super.onInit();
    debugPrint('ThemeController初始化');
    // 初始化时读取存储的主题模式
    _loadThemeMode();
  }

  /// 加载主题模式
  Future<void> _loadThemeMode() async {
    try {
      final String? themeMode = _storage.read(_themeKey);
      if (themeMode != null) {
        if (themeMode == 'system') {
          currentThemeMode.value = ThemeMode.system;
          // 根据系统设置暗黑模式
          final brightness =
              WidgetsBinding.instance.platformDispatcher.platformBrightness;
          isDarkMode.value = brightness == Brightness.dark;
        } else if (themeMode == 'dark') {
          currentThemeMode.value = ThemeMode.dark;
          isDarkMode.value = true;
        } else {
          currentThemeMode.value = ThemeMode.light;
          isDarkMode.value = false;
        }
      } else {
        // 默认使用系统主题
        currentThemeMode.value = ThemeMode.system;
        final brightness =
            WidgetsBinding.instance.platformDispatcher.platformBrightness;
        isDarkMode.value = brightness == Brightness.dark;
      }
      debugPrint('读取主题模式: ${currentThemeMode.value}');

      // 使用 Future.microtask 确保在下一个事件循环中应用主题
      Future.microtask(() {
        _applyTheme();
      });
    } catch (e) {
      debugPrint('加载主题模式失败: $e');
      // 使用系统主题作为后备
      currentThemeMode.value = ThemeMode.system;
      final brightness =
          WidgetsBinding.instance.platformDispatcher.platformBrightness;
      isDarkMode.value = brightness == Brightness.dark;

      // 使用 Future.microtask 确保在下一个事件循环中应用主题
      Future.microtask(() {
        _applyTheme();
      });
    }
  }

  /// 切换主题模式
  Future<void> toggleThemeMode() async {
    try {
      isDarkMode.value = !isDarkMode.value;
      currentThemeMode.value =
          isDarkMode.value ? ThemeMode.dark : ThemeMode.light;
      // 保存主题模式
      _storage.write(
          _themeKey, currentThemeMode.value.toString().split('.').last);
      // 保存到本地
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isDarkMode', isDarkMode.value);
      // QING
      // 应用主题
      _applyTheme();
    } catch (e) {
      debugPrint('切换主题模式失败: $e');
    }
  }

  /// 带位置参数的主题切换（用于涟漪动画）
  void toggleThemeModeWithPosition(Offset? position) {
    try {
      // 设置动画触发位置
      themeTogglePosition.value = position;

      // 切换主题
      toggleThemeMode();
    } catch (e) {
      debugPrint('带位置的主题切换失败: $e');
    }
  }

  /// 设置主题模式
  void setThemeMode(ThemeMode mode) {
    try {
      currentThemeMode.value = mode;

      if (mode == ThemeMode.system) {
        // 如果是系统模式，根据系统设置暗黑模式
        final brightness =
            WidgetsBinding.instance.platformDispatcher.platformBrightness;
        isDarkMode.value = brightness == Brightness.dark;
      } else {
        // 否则根据模式设置暗黑模式
        isDarkMode.value = mode == ThemeMode.dark;
      }

      // 保存主题模式
      _saveThemeMode(mode);

      // 应用主题
      _applyTheme();
    } catch (e) {
      debugPrint('设置主题模式失败: $e');
    }
  }

  /// 保存主题模式到存储
  Future<void> _saveThemeMode(ThemeMode mode) async {
    try {
      await _storage.write(_themeKey, mode.toString().split('.').last);
      debugPrint('保存主题模式成功: ${mode.toString()}');
    } catch (e) {
      debugPrint('保存主题模式失败: $e');
    }
  }

  /// 应用主题
  void _applyTheme() {
    try {
      Get.changeThemeMode(currentThemeMode.value);
      debugPrint('应用主题: ${currentThemeMode.value}');
    } catch (e) {
      debugPrint('应用主题失败: $e');
    }
  }

  /// 从全局坐标获取相对位置并切换主题
  void toggleThemeModeFromGlobalPosition(Offset globalPosition) {
    try {
      // 获取屏幕尺寸
      final screenSize = Get.size;

      // 转换为相对位置 (0.0 - 1.0)
      final relativePosition = Offset(
        globalPosition.dx / screenSize.width,
        globalPosition.dy / screenSize.height,
      );

      // 使用相对位置切换主题
      toggleThemeModeWithPosition(relativePosition);
    } catch (e) {
      debugPrint('从全局位置切换主题失败: $e');
      // 如果失败，使用普通切换
      toggleThemeMode();
    }
  }
}
