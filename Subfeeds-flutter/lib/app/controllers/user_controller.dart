import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:subfeeds/app/data/models/user_model.dart';
import 'package:subfeeds/app/data/repositories/user_repository.dart';
import 'package:subfeeds/app/data/services/auth_service.dart';
import 'package:subfeeds/app/data/services/http_service.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:get_storage/get_storage.dart';

/// 用户控制器，负责管理用户状态和登录流程
class UserController extends GetxController {
  // 用户仓库
  final UserRepository _userRepository = UserRepository();
  // HTTP服务
  final HttpService _httpService = HttpService();

  // 第三方登录服务
  late AuthService _authService;
  bool _authServiceInitialized = false;

  // 存储实例
  final GetStorage _storage = GetStorage();

  // 用户信息
  final Rx<UserModel?> user = Rx<UserModel?>(null);

  // 加载状态
  final RxBool isLoading = false.obs;

  // 错误信息
  final RxString errorMessage = ''.obs;
  static const Duration _storageTimeout = Duration(milliseconds: 500);

  // 用户登录状态标志
  final RxBool _hasLocalUserInfo = false.obs;

  // 是否已登录
  bool get isLoggedIn => _httpService.getToken() != null;

  @override
  void onInit() {
    super.onInit();
    debugPrint('UserController初始化');
    // 初始化时检查用户登录状态
    _checkLocalUserInfo();
    checkLoginStatus();
  }

  /// 检查本地是否有用户信息
  Future<void> _checkLocalUserInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasUserInfo = prefs.containsKey('user_info');
      _hasLocalUserInfo.value = hasUserInfo;
      debugPrint('本地是否有用户信息: $_hasLocalUserInfo');
    } catch (e) {
      debugPrint('检查本地用户信息失败: $e');
    }
  }

  /// 检查HTTP服务是否已初始化
  Future<bool> _waitForHttpServiceInit() async {
    int retryCount = 0;
    const maxRetries = 5;
    const retryDelay = Duration(milliseconds: 500);

    while (retryCount < maxRetries) {
      if (_httpService.getToken() != null) {
        debugPrint('HTTP服务已初始化，token可用');
        return true;
      }

      debugPrint('HTTP服务尚未初始化，正在等待初始化完成...');
      await Future.delayed(retryDelay);
      retryCount++;
    }

    debugPrint('等待HTTP服务初始化超时');
    return false;
  }

  /// 初始化第三方登录服务
  Future<void> _initAuthService() async {
    if (_authServiceInitialized) return;

    _authService = AuthService();
    await _authService.init();
    _authServiceInitialized = true;
  }

  /// 检查用户登录状态
  Future<void> checkLoginStatus() async {
    isLoading.value = true;
    errorMessage.value = '';
    debugPrint('UserController检查用户登录状态');

    // 等待HTTP服务初始化
    final isHttpServiceReady = await _waitForHttpServiceInit();
    if (!isHttpServiceReady) {
      debugPrint('HTTP服务未就绪，使用本地用户信息');
      // 尝试从本地获取用户信息
      final localUser = await _userRepository.getUserInfoFromLocal();
      if (localUser != null) {
        user.value = localUser;
        _hasLocalUserInfo.value = true;
        debugPrint('从本地加载用户信息成功: ${localUser.name}');
      } else {
        debugPrint('本地无用户信息');
      }
      isLoading.value = false;
      return;
    }

    try {
      // 从本地获取用户信息
      final response = await _userRepository.getUserInfo();
      debugPrint('UserController检查用户登录状态: ${response.data?.toJson()}');
      if (response.isSuccess) {
        user.value = response.data;
        _hasLocalUserInfo.value = true;
      } else {
        print('获取用户信息失败1:');
        // 尝试从本地获取用户信息作为备份
        final localUser = await _userRepository.getUserInfoFromLocal();
        if (localUser != null) {
          user.value = localUser;
          _hasLocalUserInfo.value = true;
          debugPrint('从本地加载用户信息成功: ${localUser.name}');
        } else {
          user.value = null;
          _hasLocalUserInfo.value = false;
        }
        // 如果本地没有用户信息，跳转到登录选择页面
        // Get.offAllNamed(Routes.LOGIN_SELECTION);
      }
    } catch (e) {
      print('获取用户信息失败2: $e');
      // 尝试从本地获取用户信息作为备份
      final localUser = await _userRepository.getUserInfoFromLocal();
      if (localUser != null) {
        user.value = localUser;
        _hasLocalUserInfo.value = true;
        debugPrint('从本地加载用户信息成功: ${localUser.name}');
      } else {
        user.value = null;
        _hasLocalUserInfo.value = false;
      }
      // 跳转到登录选择页面
      // Get.offAllNamed(Routes.LOGIN_SELECTION);
    } finally {
      isLoading.value = false;
    }
  }

  /// 登录
  Future<bool> login(
      String account, String password, bool rememberMe, int type) async {
    try {
      final response = await _userRepository.login(account, password, type);
      if (response.isSuccess && response.data != null) {
        // 保存token
        await _storage.write('token', response.token);
        _saveToken(response.token);
        // 保存用户信息
        user.value = response.data;

        // 获取并保存最新的用户信息
        await refreshUserInfo();

        return true;
      } else {
        errorMessage.value = response.msg;
        return false;
      }
    } catch (e) {
      print('登录错误: $e');
      return false;
    }
  }

  /// Google登录
  Future<bool> signInWithGoogle() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      // 初始化第三方登录服务
      await _initAuthService();

      // 获取Google用户信息
      final googleUser = await _authService.signInWithGoogle();

      if (googleUser != null) {
        // 调用第三方登录接口
        final response = await _userRepository.authLogin(googleUser);

        if (response.isSuccess && response.data != null) {
          // 更新用户信息
          user.value = response.data;
          // 保存token
          _storage.write('token', response.token);
          // 跳转到首页
          Get.offAllNamed(Routes.HOME);
          return true;
        } else {
          errorMessage.value = response.msg;
          return false;
        }
      } else {
        errorMessage.value = 'Google登录失败';
        return false;
      }
    } catch (e) {
      errorMessage.value = 'Google登录失败: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Apple登录
  Future<bool> signInWithApple() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      // 初始化第三方登录服务
      await _initAuthService();

      // 获取Apple用户信息
      final appleUser = await _authService.signInWithApple();

      if (appleUser != null) {
        // 调用第三方登录接口
        final response = await _userRepository.authLogin(appleUser);

        if (response.isSuccess && response.data != null) {
          // 更新用户信息
          user.value = response.data;
          // 保存token
          _storage.write('token', response.token);
          // 跳转到首页
          Get.offAllNamed(Routes.HOME);
          return true;
        } else {
          errorMessage.value = response.msg;
          return false;
        }
      } else {
        errorMessage.value = 'Apple登录失败';
        return false;
      }
    } catch (e) {
      errorMessage.value = 'Apple登录失败: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 发送验证码
  Future<bool> sendVerifyCodeForRegister(
      String account, String name, int type, int channel) async {
    try {
      final response = await _userRepository.sendVerifyCode(
        account,
        type,
      );

      if (response.isSuccess) {
        Get.snackbar(
          'notice'.tr,
          'send_verify_code_success'.tr,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      } else {
        errorMessage.value = response.msg;
        return false;
      }
    } catch (e) {
      errorMessage.value = '发送验证码失败: $e';
      return false;
    }
  }

  /// 发送验证码
  Future<bool> sendVerifyCode(String account, int type, int channel) async {
    try {
      final response = await _userRepository.sendVerifyCode(
        account,
        type,
      );
      print('发送验证码响应1: ${response.toJson()}');
      if (response.isSuccess && response.code == 200) {
        Get.snackbar(
          'notice'.tr,
          'send_verify_code_success'.tr,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      } else {
        Get.snackbar(
          'error'.tr,
          'send_verify_code_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return false;
      }
    } catch (e) {
      errorMessage.value = '发送验证码失败: $e';
      return false;
    }
  }

  /// 发送验证码（用于忘记密码）
  Future<bool> sendVerificationCode(String email) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      // 调用发送验证码接口，类型为1（忘记密码）
      final result = await sendVerifyCode(email, 1, 0);
      return result;
    } catch (e) {
      errorMessage.value = '发送验证码失败: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 注册
  Future<bool> register(
      String email, String password, String verifyCode) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      // 调用注册接口
      final response =
          await _userRepository.register(email, password, verifyCode);

      if (response.isSuccess) {
        // 注册成功，跳转到登录页面
        Get.offAllNamed(Routes.LOGIN);
        Get.snackbar(
          'notice'.tr,
          'register_success'.tr,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      } else {
        print('注册失败: ${response}');
        errorMessage.value = response.msg;
        return false;
      }
    } catch (e) {
      print('注册失败: $e');
      errorMessage.value = '注册失败: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 重置密码
  Future<bool> resetPassword(
      String email, String verifyCode, String newPassword) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response =
          await _userRepository.resetPassword(email, newPassword, verifyCode);

      if (response.isSuccess) {
        // 重置密码成功，跳转到登录页面
        Get.offAllNamed(Routes.LOGIN);

        Get.snackbar(
          'notice'.tr,
          'reset_password_success'.tr,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      } else {
        errorMessage.value = response.msg;
        return false;
      }
    } catch (e) {
      errorMessage.value = '重置密码失败: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 退出登录
  Future<void> logout() async {
    isLoading.value = true;

    try {
      // 调用退出登录接口
      await _userRepository.logout();

      // 如果已初始化第三方登录服务，则清除第三方登录状态
      if (_authServiceInitialized) {
        await _authService.signOut();
      }

      // 清除用户信息
      user.value = null;
      _hasLocalUserInfo.value = false; // 清除本地用户信息标志
      // 跳转到登录页面
      Get.toNamed(Routes.LOGIN);
    } catch (e) {
      errorMessage.value = '退出登录失败: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// 保存token
  Future<void> _saveToken(dynamic token) async {
    try {
      // 使用带超时的Future.any，确保即使SharedPreferences卡住也能继续
      await Future.any([
        () async {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('auth_token', token);
          token = token;
          debugPrint('Token保存成功');
        }(),
      ]);
    } catch (e) {
      debugPrint('保存token失败: $e');
      token = token; // 至少在内存中保存token
    }
  }

  /// 更新用户资料
  Future<bool> updateUserProfile(UserModel updatedUser) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // 调用更新用户资料API
      final response = await _userRepository.updateUserProfile(updatedUser);

      if (response.isSuccess) {
        // 更新本地用户信息
        user.value = response.data;
        return true;
      } else {
        errorMessage.value = response.msg;
        return false;
      }
    } catch (e) {
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 修改密码
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // 调用修改密码API
      final response =
          await _userRepository.changePassword(oldPassword, newPassword);

      if (response.isSuccess) {
        return true;
      } else {
        errorMessage.value = response.msg;
        return false;
      }
    } catch (e) {
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // 修改刷新用户信息的方法
  Future<void> refreshUserInfo() async {
    try {
      final response = await _userRepository.getUserInfo();
      print('刷新用户信息响应: ${response.data?.toJson()}');
      if (response.isSuccess && response.data != null) {
        final userData = response.data;
        user.value = userData;
      }
    } catch (e) {
      print('刷新用户信息失败: $e');
    }
  }
}
