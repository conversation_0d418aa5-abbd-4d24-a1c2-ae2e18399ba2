import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 语言控制器
class LanguageController extends GetxController {
  // 存储实例
  final GetStorage _storage = GetStorage();

  // 存储键
  static const String _languageKey = 'language_code';
  static const String _countryKey = 'country_code';

  // 默认语言
  static const String defaultLanguage = 'en';
  static const String defaultCountry = 'US';

  // 当前语言代码
  final RxString languageCode = defaultLanguage.obs;

  // 当前国家代码
  final RxString countryCode = defaultCountry.obs;

  @override
  void onInit() {
    super.onInit();
    debugPrint('LanguageController初始化');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = Get.context;
      if (context != null) {
        _loadLanguage(context);
      } else {
        // 如果上下文不可用，使用默认语言设置
        _loadLanguageFromStorage();
      }
    });
  }

  /// 加载语言设置
  Future<void> _loadLanguage(context) async {
    final Locale systemLocale = View.of(context).platformDispatcher.locale;
    final prefs = await SharedPreferences.getInstance();
    try {
      if (prefs.getString('locale') != null) {
        final String storedLanguage =
            prefs.getString('locale') == 'en_US' ? 'English' : '简体中文';
        final String storedCountry =
            prefs.getString('locale') == 'en_US' ? 'US' : 'CN';
        languageCode.value = storedLanguage;
        countryCode.value = storedCountry;
        _updateLocale(storedLanguage, storedCountry);
        debugPrint('应用语言设置成功');
      } else {
        if (systemLocale.languageCode == 'zh') {
          languageCode.value = '简体中文';
          countryCode.value = 'CN';
        } else {
          languageCode.value = 'English';
          countryCode.value = 'US';
        }
        await prefs.setString(
            'locale', languageCode.value == 'English' ? 'en_US' : 'zh_CN');
        _updateLocale(languageCode.value, countryCode.value);
      }
    } catch (e) {
      debugPrint('加载语言设置失败: $e');

      if (systemLocale.languageCode == 'zh') {
        languageCode.value = '简体中文';
        countryCode.value = 'CN';
      } else {
        languageCode.value = 'English';
        countryCode.value = 'US';
      }
      await prefs.setString(
          'locale', languageCode.value == 'English' ? 'en_US' : 'zh_CN');
      _updateLocale(languageCode.value, countryCode.value);
    }
  }

  /// 从存储加载语言设置（不依赖上下文）
  Future<void> _loadLanguageFromStorage() async {
    final prefs = await SharedPreferences.getInstance();
    try {
      if (prefs.getString('locale') != null) {
        final String storedLanguage =
            prefs.getString('locale') == 'en_US' ? 'English' : '简体中文';
        final String storedCountry =
            prefs.getString('locale') == 'en_US' ? 'US' : 'CN';
        languageCode.value = storedLanguage;
        countryCode.value = storedCountry;
        _updateLocale(storedLanguage, storedCountry);
        debugPrint('从存储应用语言设置成功');
      } else {
        // 使用默认语言（中文）
        languageCode.value = '简体中文';
        countryCode.value = 'CN';
        await prefs.setString('locale', 'zh_CN');
        _updateLocale(languageCode.value, countryCode.value);
        debugPrint('使用默认语言设置');
      }
    } catch (e) {
      debugPrint('从存储加载语言设置失败: $e');
      // 使用默认语言（中文）
      languageCode.value = '简体中文';
      countryCode.value = 'CN';
      _updateLocale(languageCode.value, countryCode.value);
    }
  }

  /// 更新语言
  void updateLanguage(String language, String country) {
    try {
      languageCode.value = language;
      countryCode.value = country;

      // 保存到存储
      _storage.write(_languageKey, language);
      _storage.write(_countryKey, country);
      debugPrint('保存语言设置成功 - 语言: $language, 国家: $country');

      // 更新应用语言
      _updateLocale(language, country);
    } catch (e) {
      debugPrint('更新语言设置失败: $e');
    }
  }

  /// 更新应用语言
  void _updateLocale(String language, String country) {
    final locale = language == 'English'
        ? const Locale('en', 'US')
        : const Locale('zh', 'CN');
    Get.updateLocale(locale);
  }

  /// 获取当前语言名称
  String getCurrentLanguageName() {
    final locale = '${languageCode.value}_${countryCode.value}';
    switch (locale) {
      case 'en_US':
        return '英语';
      case 'zh_CN':
        return '简体中文';
      default:
        return '简体中文';
    }
  }

  /// 获取所有支持的语言
  List<Map<String, String>> get supportedLanguages => [
        {'name': '简体中文', 'languageCode': 'zh', 'countryCode': 'CN'},
        {'name': '英语', 'languageCode': 'en', 'countryCode': 'US'},
      ];
}
