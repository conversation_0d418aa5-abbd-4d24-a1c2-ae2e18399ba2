import 'package:flutter/cupertino.dart';

///自动缩放解析器
class DimensionsAutoScaleResolver {

  ///静态实例
  static final DimensionsAutoScaleResolver _instance=DimensionsAutoScaleResolver._();

  ///构造
  DimensionsAutoScaleResolver._(){
    resolver=(num val,{int? design})=>val.toDouble();
  }

  ///单例
  factory DimensionsAutoScaleResolver()=>_instance;

  ///解析器
  late double Function(num val,{int? design}) resolver;

  ///解析
  double resolve(num v,{int? design})=>resolver.call(v,design:design);





  bool _hasSetup=false;

  ///是否已经初始化
  bool get hasSetup=>_hasSetup;


  TextScaler _textScaler=TextScaler.noScaling;

  ///文字缩放
  TextScaler get textScaler=>_textScaler;

  ///初始化
  bool trySetup(BuildContext context,{double designWidth=375.0}) {
    if(_hasSetup)return true;
    var screenSize=MediaQuery.of(context).size;
    if(screenSize.width==0||screenSize.height==0)return false;
    final scale=screenSize.width/designWidth;
    resolver=(num val,{int? design}){
      return val*scale;
    };
    _textScaler=TextScaler.linear(scale);
    _hasSetup=true;
    return true;
  }
}




///自动缩放
extension DimensionsAutoScaleExt on num{

  ///自动缩放像素单位
  double get spx=>DimensionsAutoScaleResolver().resolve(this);

}