# 推荐订阅源列表页面
该页面为home_tab页面的推荐订阅源点击后的展示页面
 ## 布局
  整个页面的背景颜色浅色模式下为#ffffff，暗色模式下为#292c42，页面间距为20
   ### 顶部
    - 以assets/feeds/back.svg为topbar左侧的返回按钮，大小20，颜色在dark模式下为白色，浅色模式下为#333333
   ### 头部
    - 展示feeds的基本信息，总共两行
     - 第一行的左侧展示feeds的logo+ feeds的名称（上下布局；第一行的右侧展示创建时间与feeds的文章数量
     - 第二行展示操作按钮，总共有两个：1.Follow按钮，浅色模式下背景颜色为#2d2d2d，文字颜色为白色，暗色模式背景颜色白色，文字颜色为黑色；为宽度占65%；2.Share 按钮，浅色模式下背景颜色为#f0f1f7，文字颜色为黑色，暗色模式背景颜色#1c1e31，文字颜色为白色；为宽度占35%，两个按钮都有6的圆角，按钮之间有10的间距
   ### feeds的文章列表
     - 头部：展示标题“Latest Articles”,浅色模式文字颜色为黑色，暗色模式文字颜色为白色
     - 文章列表：展示文章列表；列表条目为上下布局，上面部分由文章标题，文章图片构成，下面部分为左右两侧布局，由feeds的logo+文章的创建时间，以及稍后阅读与收藏图标构成，每个文章条目有分割线，大小为1，颜色在浅色模式为#dfe3ff，暗色模式为#3f4458
     - 文章条目已读状态：
       浅色模式：对应文章条目的背景颜色更改为#eaeaf0，文字颜色更改为#8e8e8e
       暗色模式：对应文章条目的背景颜色更改为#212435，文字颜色更改为#8e8e8e
     - 点击后跳转到文章详情页面
## 相关接口
 - 根据feedsid获取文章列表，使用article_repository.dart下的getRssArticle接口函数
 - 数据结构：{
    "code": 200,
    "msg": null,
    "data": {
        "total": 1, //文章总数
        "pageList": [
            {
                "id": "2496569",
                "createTime": 1743984582, 
                "suffixTable": "_25_15",
                "title": "WEB/UI导航设计灵感必备网站：Navbar Gallery", //文章标题
                "link": "https://www.shejidaren.com/navbar-gallery.html",
                "guid": null,
                "description": "<div>123</div>", //文章内容，富文本
                "pubDate": "2025-04-07T00:09:43.000+00:00", // 文章发布时间
                "status": 0,
                "source": null,
                "groupHash": "10857615e85b9570e6849c399b46b3ce",
                "img": "https://images.shejidaren.com/wp-content/uploads/2025/04/unnamed-file-14.jpg", //文章封面突破
                "feedsId": "4888",
                "feedsName": "设计达人", // feeds的标题
                "isCollect": 0, // 是否收藏 0 = 否 1= 是
                "isLaterRead": 0, // 是否稍后阅读 0 = 否 1= 是
                "isRead": 0, // 是否已读 0 = 否 1= 是
            }
        ],
        "pageNum": 1,
        "pageSize": 20, 
        "sumPage": 1
    },
    "timestamp": 1744083917
}
