import 'package:flutter/material.dart';

class LoadingIndicator extends StatelessWidget {
  final double size; // 加载动画尺寸
  final Color? backgroundColor; // 背景色（可选）
  final BoxFit fit; // 图片填充模式

  const LoadingIndicator({
    Key? key,
    this.size = 50.0,
    this.backgroundColor,
    this.fit = BoxFit.contain,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: size,
        height: size,
        color: backgroundColor, // 设置背景色（透明背景可设为 Colors.transparent）
        child: Image.asset(
          'assets/images/loading.gif', // 你的GIF路径
          fit: fit,
          // 错误处理（可选）
          errorBuilder: (context, error, stackTrace) => Icon(Icons.error),
        ),
      ),
    );
  }
}
