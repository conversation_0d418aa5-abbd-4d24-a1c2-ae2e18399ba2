import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 自定义AppBar组件，支持居中标题和自定义左右图标
class CustomAppBar extends StatelessWidget {
  /// 标题文本
  final String title;

  /// 左侧图标路径
  final String? leftIconPath;

  /// 右侧图标路径集合
  final List<ActionItem>? rightActions;

  /// 左侧图标点击回调
  final VoidCallback? onLeftIconPressed;

  /// 额外的内容控件
  final Widget? extraContent;

  /// 标题样式
  final TextStyle? titleStyle;

  /// AppBar背景色
  final Color? backgroundColor;

  /// AppBar高度
  final double height;

  /// 是否扩展到顶部安全区域
  final bool extendToTopSafeArea;

  /// 图标默认颜色
  final Color? defaultIconColor;

  /// 图标大小
  final double iconSize;

  const CustomAppBar({
    Key? key,
    required this.title,
    this.leftIconPath,
    this.rightActions,
    this.onLeftIconPressed,
    this.extraContent,
    this.titleStyle,
    this.backgroundColor,
    this.height = 56.0,
    this.extendToTopSafeArea = true,
    this.defaultIconColor,
    this.iconSize = 20,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取顶部安全区域高度
    final topPadding = MediaQuery.of(context).padding.top;

    // 确定图标颜色
    final iconColor = defaultIconColor ??
        (Theme.of(context).brightness == Brightness.dark
            ? Colors.white
            : const Color(0xFF333333));

    // 确定背景颜色
    final bgColor = backgroundColor ??
        (Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF292c42)
            : const Color(0xFFFFFFFF));

    return Container(
      padding: EdgeInsets.only(top: extendToTopSafeArea ? topPadding : 0),
      decoration: BoxDecoration(
        color: bgColor,
      ),
      child: SizedBox(
        height: height,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.spx),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 居中标题
              Positioned.fill(
                child: Center(
                  child: Text(
                    title,
                    style: titleStyle ??
                        Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              fontSize: 16.spx,
                            ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

              // 左右两侧的Row布局
              Positioned.fill(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 左侧图标
                    if (leftIconPath != null)
                      SizedBox(
                        width: 35.spx,
                        height: 35.spx,
                        child: IconButton(
                          style: IconButton.styleFrom(
                            padding: const EdgeInsets.only(left: 5, right: 5),
                          ),
                          icon: SvgPicture.asset(
                            leftIconPath!,
                            width: iconSize.spx,
                            height: iconSize.spx,
                            colorFilter: ColorFilter.mode(
                              iconColor,
                              BlendMode.srcIn,
                            ),
                          ),
                          onPressed: onLeftIconPressed,
                        ),
                      )
                    else
                      const SizedBox(width: 48), // 保持左侧占位

                    // 右侧操作图标
                    Row(
                      children: rightActions
                              ?.map((item) => _buildActionItem(
                                  item, iconColor, context, iconSize))
                              .toList() ??
                          [SizedBox(width: 48.spx)], // 保持右侧占位
                    ),
                  ],
                ),
              ),

              // 额外内容
              if (extraContent != null) extraContent!,
            ],
          ),
        ),
      ),
    );
  }

  /// 构建操作按钮项
  Widget _buildActionItem(
      ActionItem item, Color iconColor, BuildContext context, double iconSize) {
    // 根据当前状态显示加载指示器或图标按钮
    if (item.isLoading) {
      return Container(
        width: iconSize.spx,
        height: iconSize.spx,
        margin: const EdgeInsets.all(8),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).primaryColor,
          ),
        ),
      );
    } else {
      return SizedBox(
        width: 35.spx,
        height: 35.spx,
        child: IconButton(
          style: IconButton.styleFrom(
            padding: const EdgeInsets.only(left: 5, right: 5),
          ),
          icon: SvgPicture.asset(
            item.iconPath,
            width: iconSize.spx,
            height: iconSize.spx,
            colorFilter: ColorFilter.mode(
              item.iconColor ?? iconColor,
              BlendMode.srcIn,
            ),
          ),
          onPressed: item.onPressed,
        ),
      );
    }
  }
}

/// 操作按钮项
class ActionItem {
  /// 图标路径
  final String iconPath;

  /// 点击回调
  final VoidCallback onPressed;

  /// 图标大小
  final double iconSize;

  /// 是否显示加载状态
  final bool isLoading;

  /// 图标颜色 (可选，覆盖默认颜色)
  final Color? iconColor;

  ActionItem({
    required this.iconPath,
    required this.onPressed,
    this.isLoading = false,
    this.iconColor,
    this.iconSize = 20,
  });
}
