import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/footpoint_screen.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:flutter/services.dart';
import 'package:flutter/rendering.dart';

/// Feeds列表抽屉组件
class FeedsDrawerList extends StatefulWidget {
  final FeedsController controller;

  const FeedsDrawerList({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<FeedsDrawerList> createState() => _FeedsDrawerListState();
}

class _FeedsDrawerListState extends State<FeedsDrawerList> {
  final RxMap<String, dynamic> userCounts = <String, dynamic>{}.obs;
  final RxBool isFolderFixed = false.obs;

  /// 是否已经加载过用户计数数据
  bool _hasLoadedUserCounts = false;

  @override
  void initState() {
    super.initState();

    // 确保在组件初始化时加载数据
    if (widget.controller.feedsList.isEmpty &&
        widget.controller.foldersList.isEmpty) {
      // widget.controller.loadAllData();
    }

    // 只在初始化时获取一次用户计数数据
    _loadUserCountsOnce();
  }

  /// 只加载一次用户计数数据
  void _loadUserCountsOnce() {
    if (!_hasLoadedUserCounts) {
      _hasLoadedUserCounts = true;
      widget.controller.getUserCount().then((response) {
        if (response.isSuccess) {
          userCounts.value = response.data ?? {};
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        // 检测滚动到底部的条件
        if (notification is ScrollUpdateNotification ||
            notification is ScrollEndNotification) {
          if (notification.metrics.pixels >=
                  notification.metrics.maxScrollExtent - 200 &&
              !widget.controller.isLoading.value &&
              widget.controller.hasMoreData.value) {
            print(
                '触发加载更多: ${notification.metrics.pixels}/${notification.metrics.maxScrollExtent}');
            widget.controller.loadFeeds();
          }
        }
        return true; // 继续传递通知
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 0),
        child: Obx(() {
          if (widget.controller.isLoading.value &&
              widget.controller.feedsList.isEmpty &&
              widget.controller.foldersList.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          if (widget.controller.hasError.value &&
              widget.controller.feedsList.isEmpty &&
              widget.controller.foldersList.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 24, color: Colors.red),
                  const SizedBox(height: 8),
                  Text(widget.controller.errorMessage.value,
                      style: TextStyle(fontSize: 14)),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: widget.controller.refreshFeeds,
                    style: ElevatedButton.styleFrom(
                      minimumSize: Size(80, 36),
                    ),
                    child: Text('retry'.tr),
                  ),
                ],
              ),
            );
          }

          return widget.controller.feedsList.isEmpty &&
                  widget.controller.foldersList.isEmpty
              ? _buildEmptyState(context)
              : SingleChildScrollView(
                  // 禁用内部滚动，让外部抽屉的ListView处理滚动
                  physics: const NeverScrollableScrollPhysics(),
                  // 保留控制器绑定，以便未来可能的功能扩展
                  controller: widget.controller.feedsListScrollController,
                  child: _buildFeedsContent(context),
                );
        }),
      ),
    );
  }

  /// 构建空状态显示
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/note-empty.png',
            width: 80.spx,
            height: 80.spx,
          ),
          const SizedBox(height: 12),
          Text(
            'feeds_no_feeds'.tr,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontSize: 14,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建Feeds内容
  Widget _buildFeedsContent(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 文件夹标题
        _buildListHeader(context),

        // 文件夹列表
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF252939)
                : const Color(0xFFFFFFFF),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: widget.controller.foldersList
                .map((folder) => _buildFolderItem(context, folder))
                .toList(),
          ),
        ),

        // 未分组的Feeds列表
        if (widget.controller.feedsList.isNotEmpty)
          Container(
            margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF252939)
                  : const Color(0xFFFFFFFF),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ...widget.controller.feedsList
                    .map((feed) => KeyedSubtree(
                          key: ValueKey(widget.controller.getItemKey(feed)),
                          child: _buildFeedItem(context, feed),
                        ))
                    .toList(),

                // 加载更多提示
                _buildLoadMoreIndicator(context),
              ],
            ),
          ),
      ],
    );
  }

  /// 构建列表标题
  Widget _buildListHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'folder'.tr,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          Row(
            children: [
              SizedBox(
                width: 20.spx,
                height: 20.spx,
                child: IconButton(
                  onPressed: () {
                    _showCreateFolderDialog(context);
                  },
                  padding: EdgeInsets.zero,
                  icon: SvgPicture.asset(
                    'assets/feeds/add_folder.svg',
                    colorFilter:
                        ColorFilter.mode(Color(0xFF73839d), BlendMode.srcIn),
                  ),
                ),
              ),
              // 手动刷新
              // const SizedBox(width: 10),
              // SizedBox(
              //   width: 20,
              //   height: 20,
              //   child: IconButton(
              //       onPressed: () {
              //         // 刷新列表
              //         widget.controller.refreshFeeds();
              //       },
              //       padding: EdgeInsets.zero,
              //       icon: SvgPicture.asset(
              //         'assets/icons/refresh.svg',
              //         width: 18,
              //         height: 18,
              //         colorFilter:
              //             ColorFilter.mode(Color(0xFF73839d), BlendMode.srcIn),
              //       )),
              // ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建加载更多指示器
  Widget _buildLoadMoreIndicator(BuildContext context) {
    return Obx(() {
      if (widget.controller.isLoading.value &&
          widget.controller.hasMoreData.value) {
        // 加载中状态
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(
            child: SizedBox(
              width: 24.spx,
              height: 24.spx,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        );
      } else if (widget.controller.hasMoreData.value) {
        // 有更多数据但未加载
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(
            child: Text(
              'pull_up_to_load'.tr,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
            ),
          ),
        );
      } else if (widget.controller.feedsList.isNotEmpty ||
          widget.controller.foldersList.isNotEmpty) {
        // 没有更多数据
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(
            child: Text(
              'no_more_feeds'.tr,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
            ),
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  /// 构建订阅源项目
  Widget _buildFeedItem(BuildContext context, Map<String, dynamic> feed,
      {String? folderId}) {
    return AutomaticKeepAliveWidget(
      child: LongPressDraggable<Map<String, dynamic>>(
        data: {
          'feed': feed,
          'folderId': folderId,
        },
        maxSimultaneousDrags: 1,
        hapticFeedbackOnStart: true,
        delay: const Duration(milliseconds: 100),
        feedback: Material(
          elevation: 4.0,
          child: Container(
            width: MediaQuery.of(context).size.width - 64,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: feed['rssFeeds']['img'] != null &&
                          feed['rssFeeds']['img'].toString().isNotEmpty
                      ? Image.network(
                          feed['rssFeeds']['img'],
                          width: 20.spx,
                          height: 20.spx,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            width: 20.spx,
                            height: 20.spx,
                            color: Theme.of(context).textTheme.bodySmall?.color,
                            child: SvgPicture.asset(
                              'assets/feeds/feeds_logo.svg',
                              fit: BoxFit.cover,
                            ),
                          ),
                        )
                      : SvgPicture.asset(
                          'assets/feeds/feeds_logo.svg',
                          fit: BoxFit.cover,
                        ),
                ),
                const SizedBox(width: 5),
                Expanded(
                  child: Text(
                    feed['feedsName']?.trim() ?? '',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
        child: GestureDetector(
          onTap: () {
            // 跳转到该Feed的文章列表页
            Get.toNamed(
              Routes.FEEDS_ARTICLES,
              arguments: {'feed': feed},
            );
          },
          child: Container(
            color: Colors.transparent,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: Image.network(
                    feed['rssFeeds']['img'] ??
                        'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['rssFeeds']['originUrl']}&size=64',
                    width: 20.spx,
                    height: 20.spx,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: 20.spx,
                      height: 20.spx,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                      child: SvgPicture.asset(
                        'assets/feeds/feeds_logo.svg',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        feed['feedsName'] != null
                            ? feed['feedsName']?.trim() ?? ''
                            : '',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14,
                                ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  child: Text(
                    '${feed['unreadCount'] ?? 0}',
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall
                        ?.copyWith(fontSize: 12, color: Color(0XFF8e8e8e)),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => _showFeedOptionsBottomSheet(context, feed),
                  child: Container(
                    padding: const EdgeInsets.only(
                        top: 4, left: 4, bottom: 4, right: 0),
                    child: Icon(
                      Icons.more_vert,
                      size: 20.spx,
                      color: Color(0XFFcdcfd4),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建文件夹项目
  Widget _buildFolderItem(BuildContext context, Map<String, dynamic> folder) {
    return Obx(() {
      final isExpanded =
          widget.controller.expandedFolders.contains(folder['id'].toString());
      final feedsList = folder['userFeedsPos'] as List? ?? [];
      final bool isLoading = isExpanded && feedsList.isEmpty;

      return DragTarget<Map<String, dynamic>>(
        onWillAccept: (data) {
          if (data == null) return false;
          final dragFeed = data['feed'] as Map<String, dynamic>;
          final sourceFolderId = data['folderId'] as String?;
          return widget.controller
              .canAcceptDrag(folder['id'].toString(), sourceFolderId);
        },
        onAccept: (data) async {
          final dragFeed = data['feed'] as Map<String, dynamic>;
          final sourceFolderId = data['folderId'] as String?;
          final feedId = int.tryParse(dragFeed['id'].toString()) ?? -1;

          if (feedId != -1) {
            await widget.controller.moveFeedToFolder(
              feedId,
              sourceFolderId,
              folder['id'].toString(),
            );
          }
        },
        builder: (context, candidateData, rejectedData) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 文件夹头部
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () =>
                    widget.controller.toggleFolder(folder['id'].toString()),
                child: Container(
                  decoration: BoxDecoration(
                    color: candidateData.isNotEmpty
                        ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                        : null,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.only(
                      left: 12, right: 16, top: 10, bottom: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 右边的按钮
                      GestureDetector(
                        onTap: () => widget.controller
                            .toggleFolder(folder['id'].toString()),
                        child: Icon(
                          isExpanded
                              ? Icons.expand_more_outlined
                              : Icons.chevron_right,
                          size: 23,
                        ),
                      ),
                      const SizedBox(width: 3),
                      Expanded(
                        child: Text(
                          folder['groupName'] ?? '',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                  ),
                        ),
                      ),

                      const SizedBox(width: 2),
                      GestureDetector(
                        onTap: () =>
                            _showFolderOptionsBottomSheet(context, folder),
                        child: Container(
                          padding: const EdgeInsets.only(
                              top: 4, left: 4, bottom: 4, right: 0),
                          child: Icon(
                            Icons.more_vert,
                            size: 20,
                            color: Color(0XFFcdcfd4),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // 展开时显示文件夹内的订阅源
              if (isExpanded)
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight:
                        isLoading ? 100 : feedsList.length * 50.0, // 加载时高度较小
                  ),
                  child: Container(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFF212435)
                        : const Color(0xFFeaeaf0),
                    child: isLoading
                        ? Center(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: CircularProgressIndicator(),
                            ),
                          )
                        : ListView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemCount: feedsList.length,
                            itemBuilder: (context, index) {
                              final feed = feedsList[index];
                              return Padding(
                                padding: const EdgeInsets.only(left: 23),
                                child: _buildFeedItem(
                                  context,
                                  feed,
                                  folderId: folder['id'].toString(),
                                ),
                              );
                            },
                          ),
                  ),
                ),
            ],
          );
        },
      );
    });
  }

  /// 显示订阅源选项底部弹出菜单
  void _showFeedOptionsBottomSheet(
      BuildContext context, Map<String, dynamic> feed) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF212534)
              : const Color(0xFFFFFFFF),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: Text('rename'.tr),
              onTap: () {
                Get.back();
                _showFeedRenameDialog(context, feed);
              },
            ),
            ListTile(
              leading: const Icon(Icons.info_outline),
              title: Text('properties'.tr),
              onTap: () {
                Get.back();
                _showFeedPropertiesDialog(context, feed);
              },
            ),
            ListTile(
              leading: const Icon(Icons.mark_email_read),
              title: Text('mark_all_as_read'.tr),
              onTap: () {
                Get.back();
                _showMarkAllAsReadDialog(context, feed);
              },
            ),
            ListTile(
              leading: const Icon(Icons.unsubscribe),
              title: Text('unfollow'.tr),
              onTap: () {
                Get.back();
                _showUnfollowFeedDialog(context, feed);
              },
            ),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
    );
  }

  /// 显示文件夹选项底部弹出菜单
  void _showFolderOptionsBottomSheet(
      BuildContext context, Map<String, dynamic> folder) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF212534)
              : const Color(0xFFFFFFFF),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: Text('rename'.tr),
              onTap: () {
                Get.back();
                _showFolderRenameDialog(context, folder);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: Text('delete'.tr),
              onTap: () {
                Get.back();
                _showUnfollowFolderDialog(context, folder);
              },
            ),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
    );
  }

  /// 显示创建文件夹弹窗
  void _showCreateFolderDialog(BuildContext context) {
    final TextEditingController folderNameController = TextEditingController();
    final TextEditingController searchController = TextEditingController();
    final selectedFeeds = <int>[].obs;
    final searchResults = <Map<String, dynamic>>[].obs;
    final isSearching = false.obs;
    final searchText = ''.obs;
    Worker? searchDebounceWorker;

    // 在对话框显示时创建防抖worker
    searchDebounceWorker = debounce(
      searchText,
      (value) async {
        if (value.isEmpty) {
          searchResults.clear();
          return;
        }
        isSearching.value = true;
        final results = await widget.controller.searchFeeds(value);
        searchResults.value = results;
        isSearching.value = false;
      },
      time: const Duration(milliseconds: 500), // 500毫秒的防抖时间
    );
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF212534)
          : const Color(0xFFFFFFFF),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height - 100,
      ),
      builder: (context) {
        return Container(
          // 设置固定高度为屏幕高度，参考SearchBottomSheet的实现
          height: MediaQuery.of(context).size.height,
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF212534)
                : const Color(0xFFFFFFFF),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            // 移除mainAxisSize: MainAxisSize.min，让Column自然扩展
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 添加顶部拖动条，提升用户体验
              Center(
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40.spx,
                  height: 4.spx,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 取消按钮
                    GestureDetector(
                      onTap: () {
                        searchDebounceWorker?.dispose();
                        Get.back();
                      },
                      child: Text(
                        'cancel'.tr,
                        style: TextStyle(
                            color: const Color(0xFF8e8e8e),
                            fontSize: 14,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                    // 中间标题
                    Text(
                      'create_folder'.tr,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black,
                      ),
                    ),
                    // 按钮
                    _buildDialogActionButtons(context, folderNameController,
                        selectedFeeds, searchDebounceWorker),
                  ],
                ),
              ),
              // 使用Expanded包裹其余内容，使其在键盘弹出时能够适应布局
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      Text(
                        'folder_name'.tr,
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall
                            ?.copyWith(fontSize: 14, color: Color(0Xff8e8e8e)),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        autofocus: false,
                        controller: folderNameController,
                        decoration: InputDecoration(
                          isDense: true,
                          filled: true,
                          fillColor:
                              Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFF1c1e31)
                                  : const Color(0xFFf0f1f7),
                          hintStyle:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Color(0XFF73839d),
                                  ),
                          prefixIcon: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0),
                            child: SvgPicture.asset(
                              'assets/feeds/feeds_folder.svg',
                              width: 16.spx,
                              height: 16.spx,
                              colorFilter: ColorFilter.mode(
                                Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white
                                    : const Color(0xFF333333),
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                          prefixIconConstraints: BoxConstraints(
                            minWidth: 48.spx,
                            maxHeight: 48.spx,
                          ),
                          hintText: 'folder_name'.tr,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.primary,
                              width: 1,
                            ),
                          ),
                        ),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontSize: 14,
                            ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'select_feeds'.tr,
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall
                            ?.copyWith(fontSize: 14, color: Color(0Xff8e8e8e)),
                      ),
                      const SizedBox(height: 8),
                      _buildSearchFeedField(
                          context, searchController, searchText),
                      const SizedBox(height: 8),
                      // 移除内边距，使用Expanded包装列表
                      Expanded(
                        child: _buildSelectFeedsList(
                            context, isSearching, searchResults, selectedFeeds),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建搜索订阅源输入框
  Widget _buildSearchFeedField(BuildContext context,
      TextEditingController searchController, RxString searchText) {
    return TextField(
      autofocus: false, // 添加autofocus属性并设置为false，防止自动获取焦点导致标签切换
      controller: searchController,
      decoration: InputDecoration(
        isDense: true,
        filled: true,
        fillColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1c1e31)
            : const Color(0xFFf0f1f7),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Color(0XFF73839d),
            ),
        prefixIcon: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: SvgPicture.asset(
            'assets/feeds/feeds_search.svg',
            width: 20.spx,
            height: 20.spx,
            colorFilter: ColorFilter.mode(
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : const Color(0xFF333333),
              BlendMode.srcIn,
            ),
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 48.spx,
          maxHeight: 48.spx,
        ),
        hintText: 'search_feeds'.tr,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 1,
          ),
        ),
        suffixIcon: Obx(() => searchText.value.isNotEmpty
            ? IconButton(
                onPressed: () {
                  searchController.clear();
                  searchText.value = '';
                },
                icon: Icon(Icons.clear, size: 20.spx),
                color: Colors.grey,
              )
            : const SizedBox.shrink()),
      ),
      onChanged: (value) {
        searchText.value = value;
      },
    );
  }

  /// 构建选择订阅源列表
  Widget _buildSelectFeedsList(BuildContext context, RxBool isSearching,
      RxList<Map<String, dynamic>> searchResults, RxList<int> selectedFeeds) {
    return Obx(() {
      if (isSearching.value) {
        return const Center(child: CircularProgressIndicator());
      }

      final displayList = searchResults.isNotEmpty
          ? searchResults
          : widget.controller.feedsList;

      return ListView.builder(
        // 移除shrinkWrap: true，避免在键盘弹出时的性能问题
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom, // 保留底部安全区域
        ),
        itemCount: displayList.length,
        itemBuilder: (context, index) {
          final feed = displayList[index];
          return _buildSelectFeedItem(context, feed, selectedFeeds);
        },
      );
    });
  }

  /// 构建对话框底部按钮
  Widget _buildDialogActionButtons(
      BuildContext context,
      TextEditingController folderNameController,
      RxList<int> selectedFeeds,
      Worker? searchDebounceWorker) {
    return SizedBox(
      child: TextButton(
        onPressed: () async {
          if (folderNameController.text.isEmpty) {
            Get.snackbar(
              'error'.tr,
              'please_input_folder_name'.tr,
              snackPosition: SnackPosition.TOP,
              icon: const Icon(Icons.error, color: Colors.red),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
            return;
          }
          if (selectedFeeds.isEmpty) {
            Get.snackbar(
              'error'.tr,
              'please_seslect_feeds'.tr,
              snackPosition: SnackPosition.TOP,
              icon: const Icon(Icons.error, color: Colors.red),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
            return;
          }

          final response = await widget.controller.createFolder(
            folderNameController.text,
            selectedFeeds,
          );

          searchDebounceWorker?.dispose();

          if (response.code >= 0) {
            Get.back();
            Get.snackbar(
              'success'.tr,
              'folder_create_success'.tr,
              snackPosition: SnackPosition.TOP,
              icon: SvgPicture.asset('assets/feeds/right.svg'),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
            widget.controller.refreshFeeds();
          } else {
            Get.snackbar(
              'error'.tr,
              response.msg ?? 'folder_create_failed'.tr,
              snackPosition: SnackPosition.TOP,
              icon: const Icon(Icons.error, color: Colors.red),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
          }
        },
        style: TextButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            minimumSize: Size(58, 34), // 设置按钮最小尺寸
            padding: EdgeInsets.symmetric(horizontal: 6)),
        child: Text(
          'save'.tr,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12),
        ),
      ),
    );
  }

  /// 构建选择订阅源列表项
  Widget _buildSelectFeedItem(BuildContext context, Map<String, dynamic> feed,
      List<int> selectedFeeds) {
    final feedId = int.tryParse(feed['id'].toString()) ?? -1;

    return Obx(() => InkWell(
          onTap: () {
            if (selectedFeeds.contains(feedId)) {
              selectedFeeds.remove(feedId);
            } else {
              selectedFeeds.add(feedId);
            }
          },
          child: Padding(
            padding: const EdgeInsets.fromLTRB(0, 8, 8, 8),
            child: Row(
              children: [
                // RSS源Logo
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    feed['img'] ??
                        'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['rssFeeds']['originUrl']}&size=64',
                    width: 25.spx,
                    height: 25.spx,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: 25.spx,
                      height: 25.spx,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                      child: SvgPicture.asset(
                        'assets/feeds/feeds_logo.svg',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // RSS源标题
                Expanded(
                  child: Text(
                    feed['feedsName']?.trim() ?? '', //去除空格
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Spacer(),
                SizedBox(
                  width: 24.spx,
                  height: 24.spx,
                  child: Checkbox(
                    value: selectedFeeds.contains(feedId),
                    onChanged: (bool? value) {
                      if (value == true) {
                        selectedFeeds.add(feedId);
                      } else {
                        selectedFeeds.remove(feedId);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  /// 显示订阅源属性对话框
  void _showFeedPropertiesDialog(
      BuildContext context, Map<String, dynamic> feed) {
    final rssFeeds = feed['rssFeeds'] as Map<String, dynamic>? ?? {};

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF212534)
              : const Color(0xFFFFFFFF),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    feed['img'] ??
                        'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${rssFeeds['originUrl']}&size=64',
                    width: 25.spx,
                    height: 25.spx,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: 25.spx,
                      height: 25.spx,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                      child: SvgPicture.asset('assets/feeds/feeds_logo.svg'),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    feed['feedsName'] ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF2A2D3E)
                    : const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildPropertyRow(
                    context,
                    'feed_url'.tr,
                    rssFeeds['link'] ?? '',
                  ),
                  const SizedBox(height: 12),
                  _buildPropertyRow(
                    context,
                    'website'.tr,
                    rssFeeds['originUrl'] ?? '',
                  ),
                  const SizedBox(height: 12),
                  _buildPropertyRow(
                    context,
                    'follow_date'.tr,
                    _formatDateTime(feed['updateTime'] ?? ''),
                  ),
                  const SizedBox(height: 12),
                  _buildPropertyRow(
                    context,
                    'unread'.tr,
                    '${feed['unreadCount'] ?? 0}',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  style: TextButton.styleFrom(
                    backgroundColor: const Color(0xFFb9c0eb),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                    ),
                  ),
                  child: Text(
                    'close'.tr,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    );
  }

  /// 构建属性行
  Widget _buildPropertyRow(BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).textTheme.bodySmall?.color,
                fontSize: 13,
              ),
        ),
        const SizedBox(height: 4),
        GestureDetector(
          onTap: () {
            Clipboard.setData(ClipboardData(text: value));
            Get.snackbar(
              'success'.tr,
              'copied'.tr,
              snackPosition: SnackPosition.TOP,
              icon: SvgPicture.asset('assets/feeds/right.svg'),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 14,
                          height: 1.4,
                        ),
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.copy_outlined,
                  size: 16.spx,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 格式化时间戳为日期字符串
  String _formatDateTime(dynamic timestamp) {
    if (timestamp == null || timestamp.toString().isEmpty) return '';
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(
        int.parse(timestamp.toString()) * 1000,
      );
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  /// 显示重命名订阅源对话框
  void _showFeedRenameDialog(BuildContext context, Map<String, dynamic> feed) {
    final TextEditingController nameController =
        TextEditingController(text: feed['feedsName']);
    Get.dialog(
      Dialog(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'rename_feed'.tr,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: nameController,
                autofocus: true,
                decoration: InputDecoration(
                  hintText: 'enter_new_name'.tr,
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        backgroundColor: const Color(0xFFb9c0eb),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        if (nameController.text.trim().isEmpty) return;

                        await widget.controller.updateRssSub(
                          int.tryParse(feed['id'].toString()) ?? -1,
                          nameController.text.trim(),
                        );
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'confirm'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示标记全部已读对话框
  void _showMarkAllAsReadDialog(
      BuildContext context, Map<String, dynamic> feed) {
    final unreadCount = feed['unreadCount'] ?? 0;
    if (unreadCount <= 0) {
      Get.snackbar(
        'notice'.tr,
        'no_unread_articles'.tr,
        snackPosition: SnackPosition.TOP,
        icon: SvgPicture.asset('assets/feeds/right.svg'),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      return;
    }

    Get.dialog(
      Dialog(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'mark_all_as_read'.tr,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 16),
              Text('are_you_sure_to_mark_all_as_read'.tr),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.grey.withOpacity(0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        await widget.controller.updateUserFeedsIsReadStatus(
                          [int.tryParse(feed['feedsId'].toString()) ?? -1],
                        );
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'confirm'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示取消订阅对话框
  void _showUnfollowFeedDialog(
      BuildContext context, Map<String, dynamic> feed) {
    Get.dialog(
      Dialog(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                'assets/feeds/feeds_error.svg',
                width: 50.spx,
                height: 50.spx,
              ),
              const SizedBox(height: 16),
              Text(
                'are_you_sure_to_unfollow'.tr,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontSize: 14,
                    ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        backgroundColor: const Color(0xFFb9c0eb),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        await widget.controller.deleteRss(
                          int.tryParse(feed['feedsId'].toString()) ?? -1,
                        );
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: const Color(0xFFd0452f),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'unfollow'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示重命名文件夹对话框
  void _showFolderRenameDialog(
      BuildContext context, Map<String, dynamic> folder) {
    final TextEditingController nameController =
        TextEditingController(text: folder['groupName']);

    Get.dialog(
      Dialog(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  hintText: 'enter_new_folder_name'.tr,
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16.spx),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.grey.withOpacity(0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        if (nameController.text.trim().isEmpty) return;

                        await widget.controller.updateFeedsGroup(
                          int.tryParse(folder['id'].toString()) ?? -1,
                          nameController.text.trim(),
                        );
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'confirm'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示删除文件夹对话框
  void _showUnfollowFolderDialog(
      BuildContext context, Map<String, dynamic> folder) {
    Get.dialog(
      Dialog(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                'assets/feeds/feeds_error.svg',
                width: 50.spx,
                height: 50.spx,
              ),
              const SizedBox(height: 16),
              Text(
                'are_you_sure_to_delete_folder'.tr,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontSize: 14,
                    ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        backgroundColor: const Color(0xFFb9c0eb),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        // 获取文件夹ID
                        final folderId =
                            int.tryParse(folder['id'].toString()) ?? -1;

                        if (folderId != -1) {
                          await widget.controller.deleteGroupFeeds([folderId]);
                        }
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: const Color(0xFFd0452f),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'delete'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 自动保持活动状态的 Widget 包装器
class AutomaticKeepAliveWidget extends StatefulWidget {
  final Widget child;

  const AutomaticKeepAliveWidget({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  AutomaticKeepAliveWidgetState createState() =>
      AutomaticKeepAliveWidgetState();
}

class AutomaticKeepAliveWidgetState extends State<AutomaticKeepAliveWidget>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
