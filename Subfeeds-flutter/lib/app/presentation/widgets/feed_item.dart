import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

/// 订阅源条目组件
class FeedItem extends StatelessWidget {
  /// 订阅源数据
  final Map<String, dynamic> feed;

  /// 条目宽度
  final double? width;

  /// 图标大小
  final double iconSize;

  /// 标题字体大小
  final double fontSize;

  /// 点击回调
  final VoidCallback? onTap;

  const FeedItem({
    Key? key,
    required this.feed,
    this.width,
    this.iconSize = 36,
    this.fontSize = 10,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).primaryColor;

    return Container(
      width: width,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: InkWell(
        onTap: onTap ??
            () {
              Get.toNamed(
                Routes.UNFOLLOW_FEEDS,
                arguments: {'feed': feed},
              );
            },
        borderRadius: BorderRadius.circular(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Logo - 圆形带边框
            Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: primaryColor,
                    width: 1.5,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(2),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(iconSize / 2),
                    child: Image.network(
                      feed['img'] ??
                          'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['link']}&size=64',
                      width: iconSize,
                      height: iconSize,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        width: iconSize,
                        height: iconSize,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                        child: SvgPicture.asset(
                          'assets/feeds/feeds_logo.svg',
                          width: iconSize * 0.6,
                          height: iconSize * 0.6,
                          colorFilter: const ColorFilter.mode(
                            Colors.white,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                )),

            const SizedBox(height: 4),

            // 标题
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 2),
              child: Text(
                feed['name']?.toString().trim() ?? '',
                maxLines: 1,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: fontSize,
                      fontWeight: FontWeight.w500,
                    ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
