import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'dart:math';

import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 文章卡片组件
class ArticleCard extends StatelessWidget {
  /// 文章数据
  final Map<String, dynamic> article;

  /// 文章索引
  final int index;

  /// 是否开启右上角按钮
  final bool showRightButtons;

  /// 收藏点击回调
  final Function(int) onBookmarkToggle;

  /// 稍后阅读点击回调
  final Function(int) onReadLaterToggle;

  /// 卡片点击回调
  final Function(int, String, String) onCardTap;

  const ArticleCard({
    Key? key,
    required this.article,
    required this.index,
    required this.onBookmarkToggle,
    required this.onReadLaterToggle,
    required this.onCardTap,
    // 是否开启右上角按钮
    this.showRightButtons = false,
  }) : super(key: key);

  /// 生成随机图片URL
  /// 根据文章ID和当前时间生成一个随机种子，确保同一文章在同一时间段内获取相同图片
  /// 但不同时间段或不同文章会获取不同图片
  String _getRandomImageUrl() {
    // 获取文章ID的哈希值作为基础种子
    final int baseHashCode = article['id'].toString().hashCode;

    // 添加当前时间（精确到小时）作为变化因子，确保不同时间获取不同图片
    final String timeKey = DateTime.now().toString().substring(0, 13);
    final int timeHashCode = timeKey.hashCode;

    // 组合种子
    final int combinedSeed = baseHashCode ^ timeHashCode;
    final random = Random(combinedSeed);

    // 定义图片类别
    final List<String> categories = ['plants', 'landscape', 'geography'];

    // 随机选择一个类别
    final String category = categories[random.nextInt(categories.length)];

    // 生成一个随机数，确保URL每次都不同（即使是同一文章在同一小时内）
    final int randomParam = random.nextInt(1000);
    // 构建最终的URL，使用unsplash source获取指定类别的随机图片
    return 'https://picsum.photos/200/300?random=$randomParam';
  }

  @override
  Widget build(BuildContext context) {
    // 为每个文章生成一个随机的长宽比
    // 使用文章id的哈希值来确保同一文章每次都有相同的比例
    final seed = article['id'].toString().hashCode;
    final random = Random(seed);
    // 在0.8到0.9之间的随机比例
    final aspectRatio = 0.8 + random.nextDouble() * 0.1;

    return Card(
      margin: const EdgeInsets.only(bottom: 6),
      elevation: 0.5,
      clipBehavior: Clip.antiAlias,
      color: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF292c42)
          : const Color(0xffffffff),
      shadowColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6.0)),
      child: InkWell(
        onTap: () => onCardTap(
          index,
          article['feedsId'],
          article['suffixTable'],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 文章图片
            if (article['img'] != null)
              _buildArticleImage(context, aspectRatio),

            // 文章信息
            _buildArticleInfo(context),
          ],
        ),
      ),
    );
  }

  /// 构建文章图片部分
  Widget _buildArticleImage(BuildContext context, double aspectRatio) {
    return Stack(
      children: [
        AspectRatio(
          aspectRatio: aspectRatio,
          child: Image.network(
            article['img'],
            // 'http://192.168.31.60:5173/footprint',
            fit: BoxFit.cover,
            // 什么都不要显示https://picsum.photos/200/300
            // errorBuilder: (context, error, stackTrace) => SizedBox.shrink(),
            errorBuilder: (context, error, stackTrace) => Image.network(
              _getRandomImageUrl(),
              fit: BoxFit.cover,
              // 如果随机图片加载也失败，则显示空白
              errorBuilder: (context, error, stackTrace) =>
                  const SizedBox.shrink(),
            ),
          ),
        ),
        if (showRightButtons)
          // 操作按钮组
          Positioned(
            top: 8,
            right: 8,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildCircleButton(
                  context,
                  article['isCollect'] == 1
                      ? 'assets/icons/star-fill.svg'
                      : 'assets/icons/star.svg',
                  () => onBookmarkToggle(index),
                ),
                const SizedBox(width: 8),
                _buildCircleButton(
                  context,
                  article['isLaterRead'] == 1
                      ? 'assets/icons/readlater-fill.svg'
                      : 'assets/icons/readlater-border.svg',
                  () => onReadLaterToggle(index),
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// 构建圆形按钮
  Widget _buildCircleButton(
      BuildContext context, String iconPath, VoidCallback onPressed) {
    return Container(
      width: 32.spx,
      height: 32.spx,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        shape: BoxShape.circle,
      ),
      child: IconButton(
        padding: EdgeInsets.zero,
        icon: SvgPicture.asset(
          iconPath,
          width: 15.spx,
          height: 15.spx,
        ),
        onPressed: onPressed,
      ),
    );
  }

  /// 构建文章信息部分
  Widget _buildArticleInfo(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 文章标题
          Text(
            article['title'] ?? '',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: article['isRead'] == 1
                      ? Theme.of(context).textTheme.bodySmall?.color
                      : Theme.of(context).textTheme.titleSmall?.color,
                ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 12),

          // RSS源信息
          Row(
            children: [
              // RSS源图标
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).primaryColor,
                    width: 1.5,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: Image.network(
                    article['feedsImg'],
                    width: 20.spx,
                    height: 20.spx,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) =>
                        SizedBox.shrink(),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // RSS源名称和时间
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _formatTime(article['pubDate'] ?? article['createTime']),
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall
                          ?.copyWith(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 格式化时间 (简单实现，实际应调用Controller中的方法)
  String _formatTime(dynamic timestamp) {
    if (timestamp == null) return '';

    try {
      final now = DateTime.now();
      DateTime dateTime;

      if (timestamp is int) {
        dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      } else if (timestamp is String) {
        dateTime = DateTime.parse(timestamp);
      } else {
        return timestamp.toString();
      }

      final difference = now.difference(dateTime);

      if (difference.inDays > 0) {
        return '${difference.inDays}' + 'days_ago'.tr;
      } else if (difference.inHours > 0) {
        return '${difference.inHours}' + 'hours_ago'.tr;
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}' + 'minutes_ago'.tr;
      } else {
        return 'just_now'.tr;
      }
    } catch (e) {
      return timestamp.toString();
    }
  }
}
