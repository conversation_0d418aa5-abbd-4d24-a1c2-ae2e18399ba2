import 'package:flutter/material.dart';

/// 空状态视图组件
class EmptyView extends StatelessWidget {
  /// 显示的消息
  final String message;

  /// 自定义图标
  final Widget? icon;

  /// 操作按钮
  final Widget? actionButton;

  /// 容器高度
  final double? height;

  /// 画笔工厂，可以提供自定义绘制
  final CustomPainter? customPainter;

  const EmptyView({
    Key? key,
    required this.message,
    this.icon,
    this.actionButton,
    this.height = 200,
    this.customPainter,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (customPainter != null)
            SizedBox(
              width: 100,
              height: 80,
              child: CustomPaint(
                painter: customPainter,
              ),
            )
          else if (icon != null)
            icon!
          else
            const Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey,
            ),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          if (actionButton != null) ...[
            const SizedBox(height: 16),
            actionButton!,
          ]
        ],
      ),
    );
  }
}

/// 空状态桌子画笔
class EmptyStatePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = const Color(0xFFE6E6FA).withOpacity(0.5)
      ..style = PaintingStyle.fill;

    // 绘制圆形桌面
    final tablePath = Path();
    final tableCenter = Offset(size.width / 2, size.height * 0.4);
    final tableRadius = size.width * 0.45;
    tablePath
        .addOval(Rect.fromCircle(center: tableCenter, radius: tableRadius));
    canvas.drawPath(tablePath, paint);

    // 绘制桌腿
    final legPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // 中间桌腿
    canvas.drawLine(
      Offset(size.width / 2, size.height * 0.4),
      Offset(size.width / 2, size.height * 0.85),
      legPaint,
    );

    // 左侧桌腿
    canvas.drawLine(
      Offset(size.width * 0.25, size.height * 0.4),
      Offset(size.width * 0.25, size.height * 0.85),
      legPaint,
    );

    // 右侧桌腿
    canvas.drawLine(
      Offset(size.width * 0.75, size.height * 0.4),
      Offset(size.width * 0.75, size.height * 0.85),
      legPaint,
    );

    // 桌腿底部
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(size.width / 2, size.height * 0.85),
        width: size.width * 0.1,
        height: size.height * 0.05,
      ),
      Paint()..color = Colors.black,
    );

    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(size.width * 0.25, size.height * 0.85),
        width: size.width * 0.1,
        height: size.height * 0.05,
      ),
      Paint()..color = Colors.black,
    );

    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(size.width * 0.75, size.height * 0.85),
        width: size.width * 0.1,
        height: size.height * 0.05,
      ),
      Paint()..color = Colors.black,
    );

    // 绘制椅子
    final chairPaint = Paint()
      ..color = const Color(0xFF2B2D42)
      ..style = PaintingStyle.fill;

    // 椅子靠背（心形）
    final heartPath = Path();
    final heartCenter = Offset(size.width * 0.2, size.height * 0.3);
    final heartSize = size.width * 0.15;

    // 绘制心形
    heartPath.moveTo(heartCenter.dx, heartCenter.dy + heartSize * 0.3);

    // 左半部分
    heartPath.cubicTo(
      heartCenter.dx - heartSize * 0.8,
      heartCenter.dy - heartSize * 0.2,
      heartCenter.dx - heartSize * 0.8,
      heartCenter.dy - heartSize * 0.8,
      heartCenter.dx,
      heartCenter.dy - heartSize * 0.2,
    );

    // 右半部分
    heartPath.cubicTo(
      heartCenter.dx + heartSize * 0.8,
      heartCenter.dy - heartSize * 0.8,
      heartCenter.dx + heartSize * 0.8,
      heartCenter.dy - heartSize * 0.2,
      heartCenter.dx,
      heartCenter.dy + heartSize * 0.3,
    );

    canvas.drawPath(heartPath, chairPaint);

    // 椅子座位
    final seatPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(size.width * 0.2, size.height * 0.45),
        width: size.width * 0.15,
        height: size.height * 0.05,
      ),
      seatPaint,
    );

    // 椅子腿
    canvas.drawLine(
      Offset(size.width * 0.2, size.height * 0.45),
      Offset(size.width * 0.2, size.height * 0.85),
      legPaint,
    );

    // 椅子腿底部
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(size.width * 0.2, size.height * 0.85),
        width: size.width * 0.05,
        height: size.height * 0.02,
      ),
      Paint()..color = Colors.black,
    );

    // 绘制右侧椅子（简化版）
    final rightChairPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // 椅子座位
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(size.width * 0.85, size.height * 0.5),
        width: size.width * 0.1,
        height: size.height * 0.03,
      ),
      rightChairPaint,
    );

    // 椅子腿
    canvas.drawLine(
      Offset(size.width * 0.85, size.height * 0.5),
      Offset(size.width * 0.85, size.height * 0.85),
      legPaint,
    );

    // 椅子腿底部
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(size.width * 0.85, size.height * 0.85),
        width: size.width * 0.05,
        height: size.height * 0.02,
      ),
      Paint()..color = Colors.black,
    );

    // 椅子靠背
    canvas.drawLine(
      Offset(size.width * 0.85, size.height * 0.5),
      Offset(size.width * 0.85, size.height * 0.4),
      legPaint,
    );

    canvas.drawLine(
      Offset(size.width * 0.8, size.height * 0.4),
      Offset(size.width * 0.9, size.height * 0.4),
      legPaint,
    );
  }

  @override
  bool shouldRepaint(EmptyStatePainter oldDelegate) => false;
}
