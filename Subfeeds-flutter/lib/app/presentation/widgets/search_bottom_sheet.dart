import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

class SearchBottomSheet extends StatefulWidget {
  final String title;
  final String hintText;
  final Function(String) onSearch;
  final List<String> searchHistory;
  final Function(String) addSearchHistory;
  final Function() clearSearchHistory;
  final int searchType;

  const SearchBottomSheet({
    Key? key,
    required this.title,
    required this.hintText,
    required this.onSearch,
    required this.searchHistory,
    required this.addSearchHistory,
    required this.clearSearchHistory,
    this.searchType = 1,
  }) : super(key: key);

  @override
  State<SearchBottomSheet> createState() => _SearchBottomSheetState();
}

class _SearchBottomSheetState extends State<SearchBottomSheet> {
  late final TextEditingController searchController;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部拖动条
          Center(
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              width: 40.spx,
              height: 4.spx,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          // 添加新的一行：取消按钮、标题和搜索按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Text(
                    'cancel'.tr,
                    style: TextStyle(
                        color: const Color(0xFF8e8e8e),
                        fontSize: 14,
                        fontWeight: FontWeight.w500),
                  ),
                ),
                // 中间标题
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 16.spx,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'PingFang SC',
                  ),
                ),
                // 搜索按钮
                ElevatedButton(
                  onPressed: () {
                    // 获取输入的值并执行搜索
                    final searchText = searchController.text;
                    if (searchText.isNotEmpty) {
                      widget.addSearchHistory(searchText);
                      Navigator.pop(context);
                      widget.onSearch(searchText);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      minimumSize: const Size(58, 34),
                      padding: const EdgeInsets.symmetric(horizontal: 8)),
                  child: Text(
                    'search'.tr,
                    style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // 搜索框
          Padding(
            padding: EdgeInsets.fromLTRB(20.spx, 0, 20.spx, 0),
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextField(
                autofocus: true,
                controller: searchController,
                decoration: InputDecoration(
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(
                      vertical: 12.spx, horizontal: 12.spx),
                  filled: true,
                  fillColor: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF5B5B5B)
                      : const Color(0xFFE5E9F1),
                  hintStyle: TextStyle(
                    fontSize: 14.spx,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Color(0XFF909090)
                        : Color(0XFFBCC2CC),
                  ),
                  suffixIcon: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 0.spx),
                    child: SvgPicture.asset(
                      'assets/feeds/feeds_search_weight.svg',
                      width: 16.spx,
                      height: 16.spx,
                      colorFilter: ColorFilter.mode(
                        Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFF909090)
                            : const Color(0xFFBCC2CC),
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  suffixIconConstraints: BoxConstraints(
                    minWidth: 36.spx,
                    maxHeight: 36.spx,
                  ),
                  hintText: 'search_feeds_placeholder'.tr,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(40.spx),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(40.spx),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(40.spx),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                      width: 1,
                    ),
                  ),
                ),
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    widget.addSearchHistory(value);
                    Navigator.pop(context);
                    widget.onSearch(value);
                  }
                },
              ),
            ),
          ),
          SizedBox(height: 10.spx),
          // 历史记录标题和清除按钮
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 20.spx,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'search_history'.tr,
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge
                      ?.copyWith(fontSize: 14.spx, fontWeight: FontWeight.w500),
                ),
                Obx(() => widget.searchHistory.isNotEmpty
                    ? IconButton(
                        onPressed: widget.clearSearchHistory,
                        icon: SvgPicture.asset(
                          width: 20.spx,
                          height: 20.spx,
                          'assets/feeds/feeds_delete.svg',
                          colorFilter: const ColorFilter.mode(
                              Color(0xFF7f8ea7), BlendMode.srcIn),
                        ))
                    : const SizedBox.shrink()),
              ],
            ),
          ),
          // 历史记录标签
          Expanded(
            child: Obx(
              () => widget.searchHistory.isEmpty
                  ? _buildEmptyState(context)
                  : _buildHistoryList(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(32.spx),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/search-empty.png',
              width: 120.spx,
              height: 120.spx,
            ),
            SizedBox(height: 16.spx),
            Text(
              'no_search_history'.tr,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 14.spx,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF7F8EA7),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建历史记录列表 - 使用Wrap实现换行
  Widget _buildHistoryList(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 0.spx),
      child: Wrap(
        spacing: 8.spx, // 水平间距
        runSpacing: 8.spx, // 垂直间距
        children: widget.searchHistory.map((query) {
          return _buildHistoryItem(context, query);
        }).toList(),
      ),
    );
  }

  /// 构建单个历史记录项
  Widget _buildHistoryItem(BuildContext context, String query) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFFE6E6E6)
            : const Color(0xFF555555),
        borderRadius: BorderRadius.circular(11.6.spx),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(11.6.spx),
          onTap: () {
            widget.addSearchHistory(query);
            Navigator.pop(context);
            widget.onSearch(query);
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 11.spx, vertical: 7.spx),
            child: Text(
              _capitalizeFirstLetter(query),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 10.spx,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFFD1D1D1)
                        : const Color(0xFF999999),
                  ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }

  /// 首字母大写
  String _capitalizeFirstLetter(String input) {
    if (input.isEmpty) return input;
    return input[0].toUpperCase() + input.substring(1).toLowerCase();
  }
}
