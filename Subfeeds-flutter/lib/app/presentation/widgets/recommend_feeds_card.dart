import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 推荐订阅源卡片组件
class RecommendFeedsCard extends StatelessWidget {
  /// 推荐源列表
  final List<Map<String, dynamic>> feeds;

  /// 最多显示数量
  final int maxItems;

  /// 背景颜色
  final Color? backgroundColor;

  /// 点击单个推荐源回调
  final Function(Map<String, dynamic>)? onFeedTap;

  /// 点击查看更多回调
  final VoidCallback? onMoreTap;

  const RecommendFeedsCard({
    Key? key,
    required this.feeds,
    this.maxItems = 4,
    this.backgroundColor,
    this.onFeedTap,
    this.onMoreTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 确保最多显示指定数量的推荐源
    final displayFeeds =
        feeds.length > maxItems ? feeds.sublist(0, maxItems) : feeds;

    return Card(
      elevation: 0.5,
      clipBehavior: Clip.antiAlias,
      margin: EdgeInsets.only(bottom: 5),
      color: backgroundColor ??
          (Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF292c42)
              : const Color(0xffffffff)),
      shadowColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
      child: Padding(
        padding: const EdgeInsets.only(top: 8, bottom: 4, left: 12, right: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题行
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'discover_recommend_source'.tr,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 推荐源列表 - 紧贴标题
            Container(
              margin: const EdgeInsets.only(top: 2),
              child: ListView.separated(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: displayFeeds.length,
                separatorBuilder: (context, index) => const SizedBox(height: 6),
                itemBuilder: (context, index) {
                  final feed = displayFeeds[index];
                  return Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: onFeedTap != null ? () => onFeedTap!(feed) : null,
                      borderRadius: BorderRadius.circular(4.0),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 3),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            _buildFeedIcon(context, feed),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    feed['name']?.toString().trim() ?? '',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall
                                        ?.copyWith(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 13,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 12),
            // 底部按钮
            Row(
              children: [
                Expanded(
                  flex: 65,
                  child: ElevatedButton(
                    onPressed: () async {
                      // TODO: 点击查看更多
                      onMoreTap?.call();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : const Color(0xFF2d2d2d),
                      foregroundColor:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.black
                              : Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: Text('nav_more'.tr,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFF101121)
                                  : const Color(0xFFe1e5ea),
                            )),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建订阅源图标
  Widget _buildFeedIcon(BuildContext context, Map<String, dynamic> feed) {
    final primaryColor = Theme.of(context).primaryColor;
    const double iconSize = 36;

    return Container(
      width: 40.spx,
      height: 40.spx,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(2),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(iconSize / 2),
          child: Image.network(
            feed['img'] ??
                'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['link']}&size=64',
            width: iconSize,
            height: iconSize,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
              width: iconSize,
              height: iconSize,
              color: Theme.of(context).textTheme.bodySmall?.color,
              child: SvgPicture.asset(
                'assets/feeds/feeds_logo.svg',
                width: iconSize * 0.6,
                height: iconSize * 0.6,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 获取订阅源描述信息
  String _getFeedDescription(Map<String, dynamic> feed) {
    return feed['description']?.toString() ??
        feed['remark']?.toString() ??
        feed['link']?.toString() ??
        '';
  }
}
