import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/theme_controller.dart';
import 'package:subfeeds/app/core/theme/app_theme.dart';

/// 带有动画效果的主题包装器
class AnimatedThemeWrapper extends StatefulWidget {
  final Widget child;

  const AnimatedThemeWrapper({super.key, required this.child});

  @override
  State<AnimatedThemeWrapper> createState() => _AnimatedThemeWrapperState();
}

class _AnimatedThemeWrapperState extends State<AnimatedThemeWrapper>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  ThemeData? _currentTheme;
  ThemeData? _previousTheme;
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // 创建淡入淡出动画
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // 创建缩放动画
    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    // 获取初始主题
    final themeController = Get.find<ThemeController>();
    _currentTheme = themeController.isDarkMode.value
        ? AppTheme.darkTheme
        : AppTheme.lightTheme;

    // 监听主题变化
    ever(themeController.isDarkMode, (bool isDark) {
      _handleThemeChange(isDark);
    });

    // 启动初始动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 处理主题变化
  void _handleThemeChange(bool isDark) {
    if (_isAnimating) return;

    setState(() {
      _isAnimating = true;
      _previousTheme = _currentTheme;
      _currentTheme = isDark ? AppTheme.darkTheme : AppTheme.lightTheme;
    });

    // 重置动画并开始新的过渡
    _animationController.reset();
    _animationController.forward().then((_) {
      setState(() {
        _isAnimating = false;
        _previousTheme = null;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.ltr,
          child: Stack(
            children: [
              // 如果有前一个主题，显示它作为背景
              if (_previousTheme != null && _isAnimating)
                Theme(
                  data: _previousTheme!,
                  child: Opacity(
                    opacity: 1.0 - _fadeAnimation.value,
                    child: widget.child,
                  ),
                ),

              // 当前主题
              Theme(
                data: _currentTheme ?? AppTheme.lightTheme,
                child: AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: ScaleTransition(
                        scale: _scaleAnimation,
                        child: widget.child,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// 简化版的主题过渡包装器
class SimpleAnimatedThemeWrapper extends StatefulWidget {
  final Widget child;

  const SimpleAnimatedThemeWrapper({super.key, required this.child});

  @override
  State<SimpleAnimatedThemeWrapper> createState() =>
      _SimpleAnimatedThemeWrapperState();
}

class _SimpleAnimatedThemeWrapperState
    extends State<SimpleAnimatedThemeWrapper> {
  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();

    return Obx(() {
      debugPrint(
          'SimpleAnimatedThemeWrapper: 构建主题 isDark=${themeController.isDarkMode.value}');

      return AnimatedTheme(
        data: themeController.isDarkMode.value
            ? AppTheme.darkTheme
            : AppTheme.lightTheme,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        child: widget.child,
      );
    });
  }
}

/// 带有涟漪效果的主题过渡包装器
class RippleAnimatedThemeWrapper extends StatefulWidget {
  final Widget child;

  const RippleAnimatedThemeWrapper({super.key, required this.child});

  @override
  State<RippleAnimatedThemeWrapper> createState() =>
      _RippleAnimatedThemeWrapperState();
}

class _RippleAnimatedThemeWrapperState extends State<RippleAnimatedThemeWrapper>
    with TickerProviderStateMixin {
  late AnimationController _rippleController;
  late Animation<double> _rippleAnimation;

  Offset? _rippleCenter;
  bool _showRipple = false;
  ThemeData? _newTheme;
  ThemeData? _oldTheme;

  @override
  void initState() {
    super.initState();

    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _rippleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rippleController, curve: Curves.easeInOut),
    );

    // 监听主题变化
    final themeController = Get.find<ThemeController>();
    _oldTheme = themeController.isDarkMode.value
        ? AppTheme.darkTheme
        : AppTheme.lightTheme;

    ever(themeController.isDarkMode, (bool isDark) {
      _startRippleTransition(isDark);
    });
  }

  @override
  void dispose() {
    _rippleController.dispose();
    super.dispose();
  }

  /// 开始涟漪过渡动画
  void _startRippleTransition(bool isDark) {
    final themeController = Get.find<ThemeController>();

    debugPrint(
        'RippleAnimatedThemeWrapper: 收到主题变化 isDark=$isDark, 当前动画状态: _showRipple=$_showRipple');

    // 如果正在动画中，先停止当前动画
    if (_showRipple) {
      debugPrint('停止当前动画，开始新动画');
      _rippleController.stop();
      _rippleController.reset();
    }

    setState(() {
      _newTheme = isDark ? AppTheme.darkTheme : AppTheme.lightTheme;
      _showRipple = true;
      // 使用控制器中的位置，如果没有则使用默认位置
      _rippleCenter = themeController.themeTogglePosition.value ??
          const Offset(0.5, 0.5); // 屏幕中心作为默认位置
    });

    debugPrint('开始涟漪动画，中心位置: $_rippleCenter');

    _rippleController.forward().then((_) {
      if (mounted) {
        setState(() {
          _oldTheme = _newTheme;
          _showRipple = false;
        });
        _rippleController.reset();

        debugPrint('涟漪动画完成');
      }

      // 清除位置信息
      themeController.themeTogglePosition.value = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_showRipple && _newTheme != null && _rippleCenter != null) {
      // 在动画期间，使用 ClipPath 创建明显的涟漪效果
      return AnimatedBuilder(
        animation: _rippleAnimation,
        builder: (context, child) {
          debugPrint('构建涟漪动画，进度: ${_rippleAnimation.value}');

          return Directionality(
            textDirection: TextDirection.ltr,
            child: Stack(
              children: [
                // 旧主题背景
                Theme(
                  data: _oldTheme ?? AppTheme.lightTheme,
                  child: widget.child,
                ),

                // 涟漪遮罩 - 新主题
                ClipPath(
                  clipper: CircleRevealClipper(
                    center: _rippleCenter!,
                    radius: _rippleAnimation.value,
                  ),
                  child: Container(
                    color: _newTheme!.scaffoldBackgroundColor,
                    child: Theme(
                      data: _newTheme!,
                      child: widget.child,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    } else {
      // 正常状态，显示当前主题
      return Theme(
        data: _oldTheme ?? AppTheme.lightTheme,
        child: widget.child,
      );
    }
  }
}

/// 涟漪过渡绘制器
class RippleTransitionPainter extends CustomPainter {
  final ThemeData oldTheme;
  final ThemeData newTheme;
  final Offset rippleCenter;
  final double rippleProgress;

  RippleTransitionPainter({
    required this.oldTheme,
    required this.newTheme,
    required this.rippleCenter,
    required this.rippleProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (rippleProgress <= 0.0) return;

    // 计算涟漪半径
    final maxRadius =
        math.sqrt(size.width * size.width + size.height * size.height);
    final currentRadius = maxRadius * rippleProgress;
    final center = Offset(
      rippleCenter.dx * size.width,
      rippleCenter.dy * size.height,
    );

    // 创建涟漪遮罩
    final paint = Paint()
      ..color = newTheme.scaffoldBackgroundColor.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;

    // 绘制涟漪圆形
    canvas.drawCircle(center, currentRadius, paint);

    // 添加涟漪边缘效果
    final borderPaint = Paint()
      ..color = newTheme.primaryColor.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    canvas.drawCircle(center, currentRadius, borderPaint);
  }

  @override
  bool shouldRepaint(RippleTransitionPainter oldDelegate) {
    return oldDelegate.rippleProgress != rippleProgress ||
        oldDelegate.rippleCenter != rippleCenter;
  }
}

/// 圆形揭示裁剪器
class CircleRevealClipper extends CustomClipper<Path> {
  final Offset center;
  final double radius;

  CircleRevealClipper({required this.center, required this.radius});

  @override
  Path getClip(Size size) {
    final path = Path();
    final maxRadius = math.sqrt(
      size.width * size.width + size.height * size.height,
    );
    final currentRadius = maxRadius * radius;
    final actualCenter =
        Offset(center.dx * size.width, center.dy * size.height);

    debugPrint(
        'CircleRevealClipper: size=$size, center=$center, actualCenter=$actualCenter, radius=$radius, currentRadius=$currentRadius');

    path.addOval(
      Rect.fromCircle(
        center: actualCenter,
        radius: currentRadius,
      ),
    );

    return path;
  }

  @override
  bool shouldReclip(CircleRevealClipper oldClipper) {
    return oldClipper.center != center || oldClipper.radius != radius;
  }
}
