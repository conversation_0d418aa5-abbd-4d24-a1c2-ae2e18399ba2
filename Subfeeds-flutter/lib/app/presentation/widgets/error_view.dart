import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 错误状态视图组件
class ErrorView extends StatelessWidget {
  /// 错误信息
  final String message;

  /// 重试回调
  final VoidCallback onRetry;

  /// 自定义图标
  final Widget? icon;

  /// 错误图标大小
  final double iconSize;

  /// 错误图标颜色
  final Color iconColor;

  /// 重试按钮文本
  final String retryText;

  const ErrorView({
    Key? key,
    required this.message,
    required this.onRetry,
    this.icon,
    this.iconSize = 48,
    this.iconColor = Colors.red,
    this.retryText = 'retry',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon ?? Icon(Icons.error_outline, size: iconSize, color: iconColor),
          const SizedBox(height: 16),
          Text(
            message,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRetry,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
            child: Text(retryText.tr),
          ),
        ],
      ),
    );
  }
}
