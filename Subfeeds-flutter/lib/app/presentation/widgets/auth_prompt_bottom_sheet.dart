import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/data/services/auth_guard_service.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

/// 认证提示底部弹窗
///
/// 这个组件提供了一个美观的底部弹窗，用于提示用户登录以访问受保护的功能。
/// 设计风格与应用的其他底部弹窗保持一致。
class AuthPromptBottomSheet extends StatefulWidget {
  final String title;
  final String description;
  final ProtectedFeatureType featureType;
  final VoidCallback onLoginSuccess;
  final VoidCallback onCancel;

  const AuthPromptBottomSheet({
    Key? key,
    required this.title,
    required this.description,
    required this.featureType,
    required this.onLoginSuccess,
    required this.onCancel,
  }) : super(key: key);

  @override
  State<AuthPromptBottomSheet> createState() => _AuthPromptBottomSheetState();
}

class _AuthPromptBottomSheetState extends State<AuthPromptBottomSheet>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  final UserController _userController = Get.find<UserController>();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimation();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
  }

  void _startAnimation() {
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * 100),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.6,
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF212534)
                    : const Color(0xFFFFFFFF),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  _buildDragHandle(),
                  _buildHeader(),
                  Expanded(child: _buildContent()),
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建拖动手柄
  Widget _buildDragHandle() {
    return Center(
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 8.spx),
        width: 40.spx,
        height: 4.spx,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(2.spx),
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 16.spx),
      child: Row(
        children: [
          Expanded(
            child: Text(
              widget.title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 16.spx,
                  ),
            ),
          ),
          IconButton(
            onPressed: _handleCancel,
            icon: Icon(
              Icons.close,
              size: 24.spx,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white70
                  : Colors.black54,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.spx),
      child: Column(
        children: [
          _buildFeatureIcon(),
          SizedBox(height: 24.spx),
          _buildDescription(),
          SizedBox(height: 32.spx),
          // _buildBenefitsList(),
        ],
      ),
    );
  }

  /// 构建功能图标
  Widget _buildFeatureIcon() {
    return Container(
      width: 80.spx,
      height: 80.spx,
      decoration: BoxDecoration(
        gradient: _getFeatureGradient(),
        borderRadius: BorderRadius.circular(20.spx),
      ),
      child: Icon(
        _getFeatureIcon(),
        size: 40.spx,
        color: Colors.white,
      ),
    );
  }

  /// 构建描述文本
  Widget _buildDescription() {
    return Text(
      widget.description,
      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontSize: 14.spx,
            height: 1.5,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white70
                : Colors.black87,
          ),
      textAlign: TextAlign.center,
    );
  }

  /// 构建好处列表
  Widget _buildBenefitsList() {
    final benefits = _getFeatureBenefits();
    return Column(
      children: benefits.map((benefit) => _buildBenefitItem(benefit)).toList(),
    );
  }

  /// 构建单个好处项
  Widget _buildBenefitItem(String benefit) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.spx),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            size: 20.spx,
            color: const Color(0xFF4CAF50),
          ),
          SizedBox(width: 12.spx),
          Expanded(
            child: Text(
              benefit,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 14.spx,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white70
                        : Colors.black87,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Padding(
      padding: EdgeInsets.all(20.spx),
      child: Column(
        children: [
          // 主要登录按钮
          SizedBox(
            width: double.infinity,
            height: 48.spx,
            child: ElevatedButton(
              onPressed: _handleLogin,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.spx),
                ),
              ),
              child: Text(
                'auth_prompt_login_button'.tr,
                style: TextStyle(
                  fontSize: 16.spx,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(height: 12.spx),
          // 取消按钮
          SizedBox(
            width: double.infinity,
            height: 48.spx,
            child: TextButton(
              onPressed: _handleCancel,
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white70
                    : Colors.black54,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.spx),
                ),
              ),
              child: Text(
                'auth_prompt_cancel_button'.tr,
                style: TextStyle(
                  fontSize: 16.spx,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 处理登录按钮点击
  void _handleLogin() {
    Get.back(); // 关闭底部弹窗

    // 导航到登录选择页面
    Get.toNamed(Routes.LOGIN_SELECTION)?.then((_) {
      // 检查登录状态，如果成功则执行回调
      if (_userController.isLoggedIn) {
        widget.onLoginSuccess();
      }
    });
  }

  /// 处理取消按钮点击
  void _handleCancel() {
    Get.back(); // 关闭底部弹窗
    widget.onCancel();
  }

  /// 获取功能图标
  IconData _getFeatureIcon() {
    switch (widget.featureType) {
      case ProtectedFeatureType.personalInfo:
        return Icons.person;
      case ProtectedFeatureType.premiumFeatures:
        return Icons.star;
      case ProtectedFeatureType.syncData:
        return Icons.sync;
      case ProtectedFeatureType.manageSubscriptions:
        return Icons.subscriptions;
      case ProtectedFeatureType.changePassword:
        return Icons.lock;
      case ProtectedFeatureType.accountSettings:
        return Icons.settings;
      case ProtectedFeatureType.readLater:
        return Icons.bookmark;
      case ProtectedFeatureType.favorites:
        return Icons.favorite;
      case ProtectedFeatureType.customFeature:
        return Icons.security;
    }
  }

  /// 获取功能渐变色
  LinearGradient _getFeatureGradient() {
    switch (widget.featureType) {
      case ProtectedFeatureType.personalInfo:
        return const LinearGradient(
          colors: [Color(0xFF6D8BFF), Color(0xFF8B9DFF)],
        );
      case ProtectedFeatureType.premiumFeatures:
        return const LinearGradient(
          colors: [Color(0xFFF7A071), Color(0xFFC472DF)],
        );
      case ProtectedFeatureType.syncData:
        return const LinearGradient(
          colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
        );
      case ProtectedFeatureType.readLater:
      case ProtectedFeatureType.favorites:
        return const LinearGradient(
          colors: [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
        );
      default:
        return const LinearGradient(
          colors: [Color(0xFF6D8BFF), Color(0xFFC472DF)],
        );
    }
  }

  /// 获取功能好处列表
  List<String> _getFeatureBenefits() {
    switch (widget.featureType) {
      case ProtectedFeatureType.personalInfo:
        return [
          'auth_benefit_personal_info_1'.tr,
          'auth_benefit_personal_info_2'.tr,
          'auth_benefit_personal_info_3'.tr,
        ];
      case ProtectedFeatureType.premiumFeatures:
        return [
          'auth_benefit_premium_1'.tr,
          'auth_benefit_premium_2'.tr,
          'auth_benefit_premium_3'.tr,
        ];
      case ProtectedFeatureType.syncData:
        return [
          'auth_benefit_sync_1'.tr,
          'auth_benefit_sync_2'.tr,
          'auth_benefit_sync_3'.tr,
        ];
      case ProtectedFeatureType.readLater:
        return [
          'auth_benefit_read_later_1'.tr,
          'auth_benefit_read_later_2'.tr,
        ];
      case ProtectedFeatureType.favorites:
        return [
          'auth_benefit_favorites_1'.tr,
          'auth_benefit_favorites_2'.tr,
        ];
      default:
        return [
          'auth_benefit_default_1'.tr,
          'auth_benefit_default_2'.tr,
        ];
    }
  }
}
