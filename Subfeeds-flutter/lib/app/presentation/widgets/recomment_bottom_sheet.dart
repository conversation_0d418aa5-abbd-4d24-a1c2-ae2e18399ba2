import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

class RecommendBottomSheet extends StatefulWidget {
  final String title;
  final String hintText;
  final Function(String) onSearch;
  final List<String> searchHistory;
  final Function(String) addSearchHistory;
  final Function() clearSearchHistory;
  final int searchType;

  const RecommendBottomSheet({
    Key? key,
    required this.title,
    required this.hintText,
    required this.onSearch,
    required this.searchHistory,
    required this.addSearchHistory,
    required this.clearSearchHistory,
    this.searchType = 1,
  }) : super(key: key);

  @override
  State<RecommendBottomSheet> createState() => _RecommendBottomSheetState();
}

class _RecommendBottomSheetState extends State<RecommendBottomSheet> {
  late final TextEditingController searchController;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部拖动条
          Center(
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              width: 40.spx,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          // 添加新的一行：取消按钮、标题和搜索按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Text(
                    'cancel'.tr,
                    style: TextStyle(
                        color: const Color(0xFF8e8e8e),
                        fontSize: 14,
                        fontWeight: FontWeight.w500),
                  ),
                ),
                // 中间标题
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
                // // 搜索按钮
                // ElevatedButton(
                //   onPressed: () {
                //     // 获取输入的值并执行搜索
                //     final searchText = searchController.text;
                //     if (searchText.isNotEmpty) {
                //       widget.addSearchHistory(searchText);
                //       Navigator.pop(context);
                //       widget.onSearch(searchText);
                //     }
                //   },
                //   style: ElevatedButton.styleFrom(
                //       backgroundColor: Theme.of(context).colorScheme.primary,
                //       foregroundColor: Colors.white,
                //       shape: RoundedRectangleBorder(
                //         borderRadius: BorderRadius.circular(6),
                //       ),
                //       minimumSize: const Size(58, 34),
                //       padding: const EdgeInsets.symmetric(horizontal: 8)),
                //   child: Text(
                //     'search'.tr,
                //     style: const TextStyle(
                //         fontSize: 12,
                //         fontWeight: FontWeight.bold,
                //         color: Colors.white),
                //   ),
                // ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // 搜索框
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextField(
                autofocus: true,
                controller: searchController,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey,
                      ),
                  prefixIcon:
                      const Icon(Icons.search, color: Color(0xFF72849c)),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    widget.addSearchHistory(value);
                    Navigator.pop(context);
                    widget.onSearch(value);
                  }
                },
              ),
            ),
          ),
          // 历史记录标题和清除按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'search_history'.tr,
                  style: Theme.of(context)
                      .textTheme
                      .bodySmall
                      ?.copyWith(fontSize: 14, color: const Color(0xFF8e8e8e)),
                ),
                Obx(() => widget.searchHistory.isNotEmpty
                    ? IconButton(
                        onPressed: widget.clearSearchHistory,
                        icon: SvgPicture.asset(
                          'assets/icons/delete.svg',
                          colorFilter: const ColorFilter.mode(
                              Color(0xFF8e8e8e), BlendMode.srcIn),
                        ))
                    : const SizedBox.shrink()),
              ],
            ),
          ),
          // 历史记录标签
          Expanded(
            child: Obx(
              () => widget.searchHistory.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/note-empty.png',
                            width: 120.spx,
                            height: 120.spx,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'no_search_history'.tr,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.grey,
                                    ),
                          ),
                        ],
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 5,
                      ),
                      child: GridView.builder(
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 6,
                        ),
                        itemCount: widget.searchHistory.length,
                        itemBuilder: (context, index) {
                          final query = widget.searchHistory[index];
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 0,
                            ),
                            child: InkWell(
                              onTap: () {
                                Get.toNamed(Routes.SEARCH, arguments: {
                                  'type': widget.searchType,
                                  'query': query,
                                });
                              },
                              child: Text(
                                query,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                        fontSize: 12,
                                        color: Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.white
                                            : Colors.black,
                                        fontWeight: FontWeight.w600),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
