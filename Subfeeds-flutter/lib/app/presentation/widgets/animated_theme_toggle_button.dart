import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/theme_controller.dart';

/// 带动画效果的主题切换按钮
class AnimatedThemeToggleButton extends StatefulWidget {
  /// 按钮大小
  final double size;
  
  /// 是否显示文本
  final bool showText;
  
  /// 自定义图标
  final IconData? lightIcon;
  final IconData? darkIcon;
  
  /// 按钮样式
  final ButtonStyle? style;
  
  const AnimatedThemeToggleButton({
    super.key,
    this.size = 48.0,
    this.showText = false,
    this.lightIcon,
    this.darkIcon,
    this.style,
  });

  @override
  State<AnimatedThemeToggleButton> createState() => _AnimatedThemeToggleButtonState();
}

class _AnimatedThemeToggleButtonState extends State<AnimatedThemeToggleButton>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    // 旋转动画控制器
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    // 旋转动画
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.elasticOut,
    ));
    
    // 缩放动画
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  /// 处理按钮点击
  void _handleTap(TapDownDetails details) {
    final themeController = Get.find<ThemeController>();
    
    // 播放点击动画
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });
    
    // 播放旋转动画
    _rotationController.forward().then((_) {
      _rotationController.reset();
    });
    
    // 获取按钮的全局位置
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final buttonPosition = renderBox.localToGlobal(Offset.zero);
    final buttonCenter = buttonPosition + Offset(widget.size / 2, widget.size / 2);
    
    // 使用按钮中心位置切换主题
    themeController.toggleThemeModeFromGlobalPosition(buttonCenter);
  }

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();
    
    return Obx(() {
      final isDark = themeController.isDarkMode.value;
      
      return GestureDetector(
        onTapDown: _handleTap,
        child: AnimatedBuilder(
          animation: Listenable.merge([_rotationAnimation, _scaleAnimation]),
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Transform.rotate(
                angle: _rotationAnimation.value * 2 * 3.14159,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDark 
                        ? Colors.orange.withOpacity(0.2)
                        : Colors.blue.withOpacity(0.2),
                    border: Border.all(
                      color: isDark ? Colors.orange : Colors.blue,
                      width: 2,
                    ),
                  ),
                  child: widget.showText
                      ? Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              isDark 
                                  ? (widget.darkIcon ?? Icons.dark_mode)
                                  : (widget.lightIcon ?? Icons.light_mode),
                              color: isDark ? Colors.orange : Colors.blue,
                              size: widget.size * 0.4,
                            ),
                            Text(
                              isDark ? '深色' : '浅色',
                              style: TextStyle(
                                fontSize: widget.size * 0.15,
                                color: isDark ? Colors.orange : Colors.blue,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        )
                      : Icon(
                          isDark 
                              ? (widget.darkIcon ?? Icons.dark_mode)
                              : (widget.lightIcon ?? Icons.light_mode),
                          color: isDark ? Colors.orange : Colors.blue,
                          size: widget.size * 0.5,
                        ),
                ),
              ),
            );
          },
        ),
      );
    });
  }
}

/// 简单的主题切换开关
class SimpleThemeToggleSwitch extends StatelessWidget {
  const SimpleThemeToggleSwitch({super.key});

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();
    
    return Obx(() => Switch(
      value: themeController.isDarkMode.value,
      onChanged: (value) {
        // 获取开关的位置
        final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          final switchPosition = renderBox.localToGlobal(Offset.zero);
          final switchCenter = switchPosition + Offset(
            renderBox.size.width / 2,
            renderBox.size.height / 2,
          );
          themeController.toggleThemeModeFromGlobalPosition(switchCenter);
        } else {
          themeController.toggleThemeMode();
        }
      },
      activeColor: Colors.orange,
      inactiveThumbColor: Colors.blue,
    ));
  }
}

/// 浮动主题切换按钮
class FloatingThemeToggleButton extends StatelessWidget {
  final VoidCallback? onPressed;
  
  const FloatingThemeToggleButton({
    super.key,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();
    
    return Obx(() {
      final isDark = themeController.isDarkMode.value;
      
      return FloatingActionButton(
        onPressed: () {
          // 获取按钮位置
          final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
          if (renderBox != null) {
            final buttonPosition = renderBox.localToGlobal(Offset.zero);
            final buttonCenter = buttonPosition + Offset(
              renderBox.size.width / 2,
              renderBox.size.height / 2,
            );
            themeController.toggleThemeModeFromGlobalPosition(buttonCenter);
          } else {
            themeController.toggleThemeMode();
          }
          
          onPressed?.call();
        },
        backgroundColor: isDark ? Colors.orange : Colors.blue,
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: Icon(
            isDark ? Icons.light_mode : Icons.dark_mode,
            key: ValueKey(isDark),
            color: Colors.white,
          ),
        ),
      );
    });
  }
}
