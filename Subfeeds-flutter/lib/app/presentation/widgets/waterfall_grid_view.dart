import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

/// 瀑布流网格视图组件
class WaterfallGridView extends StatelessWidget {
  /// 子项构建器
  final IndexedWidgetBuilder itemBuilder;

  /// 子项数量
  final int itemCount;

  /// 列数
  final int columnCount;

  /// 列间距
  final double crossAxisSpacing;

  /// 行间距
  final double mainAxisSpacing;

  /// 内边距
  final EdgeInsets padding;

  /// 是否禁用滚动
  final bool disableScroll;

  const WaterfallGridView({
    Key? key,
    required this.itemBuilder,
    required this.itemCount,
    this.columnCount = 2,
    this.crossAxisSpacing = 12.0,
    this.mainAxisSpacing = 0.0,
    this.padding = const EdgeInsets.fromLTRB(12, 0, 12, 16),
    this.disableScroll = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MasonryGridView.count(
      physics: disableScroll ? const NeverScrollableScrollPhysics() : null,
      shrinkWrap: true,
      crossAxisCount: columnCount,
      mainAxisSpacing: mainAxisSpacing,
      crossAxisSpacing: crossAxisSpacing,
      padding: padding,
      itemBuilder: itemBuilder,
      itemCount: itemCount,
    );
  }
}

/// 响应式布局瀑布流网格视图
/// 会根据屏幕宽度自动调整列数
class ResponsiveWaterfallGridView extends StatelessWidget {
  /// 子项构建器
  final IndexedWidgetBuilder itemBuilder;

  /// 子项数量
  final int itemCount;

  /// 列间距
  final double crossAxisSpacing;

  /// 行间距
  final double mainAxisSpacing;

  /// 内边距
  final EdgeInsets padding;

  /// 是否禁用滚动
  final bool disableScroll;

  /// 小屏幕列数 (默认2)
  final int smallScreenColumns;

  /// 中等屏幕列数 (默认3)
  final int mediumScreenColumns;

  /// 大屏幕列数 (默认4)
  final int largeScreenColumns;

  /// 中等屏幕宽度阈值
  final double mediumScreenWidth;

  /// 大屏幕宽度阈值
  final double largeScreenWidth;

  const ResponsiveWaterfallGridView({
    Key? key,
    required this.itemBuilder,
    required this.itemCount,
    this.crossAxisSpacing = 5.0,
    this.mainAxisSpacing = 0.0,
    this.padding = const EdgeInsets.fromLTRB(5, 0, 5, 16),
    this.disableScroll = true,
    this.smallScreenColumns = 2,
    this.mediumScreenColumns = 3,
    this.largeScreenColumns = 4,
    this.mediumScreenWidth = 600,
    this.largeScreenWidth = 900,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取屏幕宽度
    final screenWidth = MediaQuery.of(context).size.width;

    // 根据屏幕宽度计算列数
    int columnCount = smallScreenColumns;

    if (screenWidth > largeScreenWidth) {
      columnCount = largeScreenColumns;
    } else if (screenWidth > mediumScreenWidth) {
      columnCount = mediumScreenColumns;
    }

    return WaterfallGridView(
      itemBuilder: itemBuilder,
      itemCount: itemCount,
      columnCount: columnCount,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      padding: padding,
      disableScroll: disableScroll,
    );
  }
}
