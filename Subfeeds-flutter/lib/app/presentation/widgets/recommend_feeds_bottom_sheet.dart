import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:intl/intl.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>';

/// 推荐订阅源底部弹窗
class RecommendFeedsBottomSheet extends StatelessWidget {
  /// 标题
  final String title;

  /// 推荐源列表
  final List<Map<String, dynamic>> feeds;

  /// HomeController
  final HomeController controller;

  const RecommendFeedsBottomSheet({
    Key? key,
    required this.title,
    required this.feeds,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部拖动条
          Center(
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              width: 40.spx,
              height: 4.spx,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Text(
                    'cancel'.tr,
                    style: TextStyle(
                        color: const Color(0xFF8e8e8e),
                        fontSize: 14,
                        fontWeight: FontWeight.w500),
                  ),
                ),
                // 中间标题
                const Spacer(),
                // 搜索按钮
                ElevatedButton(
                  onPressed: () {
                    Get.back();
                  },
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      minimumSize: const Size(58, 34),
                      padding: const EdgeInsets.symmetric(horizontal: 8)),
                  child: Text(
                    'save'.tr,
                    style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          // 标题描述
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            child: Text(
              'recommend_bottom_sheet_title'.tr,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
          const SizedBox(height: 5),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            child: Text(
              'recommend_bottom_sheet_description'.tr,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 15,
                  ),
            ),
          ),
          const SizedBox(height: 10),
          // 列表内容
          Expanded(
            child: Obx(() {
              if (controller.isRecommendFeedsLoading.value &&
                  controller.recommendFeeds.isEmpty) {
                // 加载中且无数据时显示加载指示器
                return const Center(
                  child: CircularProgressIndicator(),
                );
              } else if (controller.recommendFeeds.isEmpty) {
                // 无数据但不在加载中
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        'assets/feeds/empty.svg',
                        width: 80.spx,
                        height: 80.spx,
                        colorFilter: ColorFilter.mode(
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white54
                              : Colors.black38,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'no_recommend_feeds'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey,
                            ),
                      ),
                    ],
                  ),
                );
              } else {
                // 有数据，显示列表
                return ListView.builder(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                  itemCount: controller.recommendFeeds.length,
                  itemBuilder: (context, index) {
                    final feed = controller.recommendFeeds[index];
                    final bool isSubscribed =
                        (feed['isSub'] as int? ?? 0) == 1 ||
                            controller.subscribedFeeds[
                                    feed['id']?.toString() ?? ''] ==
                                true;

                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Logo
                          _buildFeedLogo(context, feed),
                          const SizedBox(width: 12),
                          // 标题和更新时间
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  feed['name']?.toString() ?? '',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleSmall
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 14,
                                      ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _formatLastVerifyTime(feed['lastVerifyTime']),
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        fontSize: 12,
                                        color: Colors.grey,
                                      ),
                                ),
                              ],
                            ),
                          ),
                          // 订阅按钮
                          Container(
                            margin: const EdgeInsets.only(left: 10),
                            child: Obx(() {
                              final String feedId =
                                  feed['id']?.toString() ?? '';
                              final bool isCurrentlySubscribed =
                                  (feed['isSub'] as int? ?? 0) == 1 ||
                                      controller.subscribedFeeds[feedId] ==
                                          true;

                              return ElevatedButton(
                                onPressed: () {
                                  if (isCurrentlySubscribed) {
                                    controller.unsubscribeFeed(feed);
                                  } else {
                                    controller.subscribeFeed(feed);
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20, vertical: 4),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  minimumSize: const Size(0, 32),
                                  backgroundColor: isCurrentlySubscribed
                                      ? Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? const Color(0xFF212534)
                                          : Colors.grey[300]
                                      : Theme.of(context).primaryColor,
                                  elevation: 0,
                                ),
                                child: Text(
                                  isCurrentlySubscribed
                                      ? 'unfollow'.tr
                                      : 'follow'.tr,
                                  style: TextStyle(
                                    color: isCurrentlySubscribed
                                        ? Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.white
                                            : Colors.black87
                                        : Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              );
                            }),
                          ),
                        ],
                      ),
                    );
                  },
                );
              }
            }),
          ),

          // 刷新按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Obx(() {
              return ElevatedButton(
                onPressed: controller.isRecommendFeedsLoading.value
                    ? null
                    : () async {
                        await controller.refreshRecommendFeeds();
                      },
                style: ElevatedButton.styleFrom(
                  minimumSize: Size.fromHeight(45.spx),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  // 禁用时的背景色
                  disabledBackgroundColor:
                      Theme.of(context).colorScheme.primary.withAlpha(100),
                  disabledForegroundColor:
                      Theme.of(context).colorScheme.primary.withAlpha(100),
                ),
                child: controller.isRecommendFeedsLoading.value
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20.spx,
                            height: 20.spx,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'refreshing'.tr,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            'assets/icons/refresh.svg',
                            width: 24.spx,
                            height: 24.spx,
                            colorFilter: const ColorFilter.mode(
                              Colors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'refresh_recommend_feeds'.tr,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
              );
            }),
          ),
        ],
      ),
    );
  }

  /// 构建Feed Logo
  Widget _buildFeedLogo(BuildContext context, Map<String, dynamic> feed) {
    final primaryColor = Theme.of(context).primaryColor;
    const double iconSize = 40;

    return Container(
      width: 44.spx,
      height: 44.spx,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(2),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(iconSize / 2),
          child: Image.network(
            feed['img'] ??
                'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['link']}&size=64',
            width: iconSize,
            height: iconSize,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
              width: iconSize,
              height: iconSize,
              color: Theme.of(context).textTheme.bodySmall?.color,
              child: SvgPicture.asset(
                'assets/feeds/feeds_logo.svg',
                width: iconSize * 0.6,
                height: iconSize * 0.6,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 格式化最后验证时间
  String _formatLastVerifyTime(dynamic timestamp) {
    if (timestamp == null) {
      return 'no_update_time'.tr;
    }

    try {
      // 时间戳转换为日期时间
      final dateTime = DateTime.fromMillisecondsSinceEpoch((timestamp is int)
          ? timestamp * 1000
          : int.parse(timestamp.toString()) * 1000);

      // 格式化日期
      final formatter = DateFormat('yyyy-MM-dd');
      return 'updated_at'.tr + ' ${formatter.format(dateTime)}';
    } catch (e) {
      return 'no_update_time'.tr;
    }
  }
}
