import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import '../../routes/app_pages.dart';

class ArticleMasonryGrid extends StatelessWidget {
  final List<Map<String, dynamic>> articles;
  final Function(String) onToggleFavorite;
  final bool isLoading;
  final Function()? onLoadMore;
  final Function()? onRefresh;

  const ArticleMasonryGrid({
    Key? key,
    required this.articles,
    required this.onToggleFavorite,
    this.isLoading = false,
    this.onLoadMore,
    this.onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 根据屏幕宽度计算列数
    int crossAxisCount = _calculateColumnCount(context);

    return RefreshIndicator(
      onRefresh: () async {
        if (onRefresh != null) {
          onRefresh!();
        }
      },
      child: MasonryGridView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: articles.length + (isLoading ? 1 : 0),
        gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
        ),
        mainAxisSpacing: 16.0,
        crossAxisSpacing: 16.0,
        itemBuilder: (context, index) {
          if (index == articles.length) {
            // 显示加载指示器
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            );
          }

          // 检查是否需要加载更多
          if (index == articles.length - 3 && onLoadMore != null) {
            onLoadMore!();
          }

          return _buildArticleCard(context, articles[index]);
        },
      ),
    );
  }

  // 根据屏幕宽度计算列数
  int _calculateColumnCount(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) {
      return 4; // 大屏幕设备
    } else if (screenWidth > 800) {
      return 3; // 平板设备
    } else if (screenWidth > 600) {
      return 2; // 大手机
    } else {
      return 2; // 标准手机
    }
  }

  // 构建文章卡片
  Widget _buildArticleCard(BuildContext context, Map<String, dynamic> article) {
    bool hasImage =
        article['imageUrl'] != null && article['imageUrl'].isNotEmpty;

    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      elevation: 2.0,
      child: InkWell(
        onTap: () {
          Get.toNamed(
            Routes.ARTICLE,
            arguments: {'article': article},
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图片部分
            if (hasImage)
              Stack(
                children: [
                  AspectRatio(
                    aspectRatio: 16 / 9,
                    child: Image.network(
                      article['imageUrl'],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: const Center(
                            child: Icon(Icons.broken_image, color: Colors.grey),
                          ),
                        );
                      },
                    ),
                  ),
                  // 收藏按钮
                  Positioned(
                    top: 8.0,
                    right: 8.0,
                    child: _buildFavoriteButton(article),
                  ),
                ],
              ),

            // 内容部分
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Text(
                    article['title'] ?? '无标题',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8.0),

                  // 摘要 (如果没有图片，显示摘要)
                  if (!hasImage && article['summary'] != null)
                    Text(
                      article['summary'],
                      style: Theme.of(context).textTheme.bodyMedium,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),

                  const SizedBox(height: 8.0),

                  // 底部信息
                  Row(
                    children: [
                      // 来源
                      if (article['feedTitle'] != null)
                        Expanded(
                          child: Text(
                            article['feedTitle'],
                            style: Theme.of(context)
                                .textTheme
                                .labelMedium
                                ?.copyWith(
                                  color:
                                      Theme.of(context).colorScheme.secondary,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                      // 价格/出价信息
                      if (article['lastBid'] != null)
                        Text(
                          'Last Bid: ${article['lastBid']}',
                          style: Theme.of(context).textTheme.labelMedium,
                        ),

                      // 如果没有图片，在右侧显示收藏按钮
                      if (!hasImage) _buildFavoriteButton(article),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建收藏按钮
  Widget _buildFavoriteButton(Map<String, dynamic> article) {
    bool isFavorite = article['isStarred'] ?? false;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          onToggleFavorite(article['id']);
        },
        customBorder: const CircleBorder(),
        child: Padding(
          padding: const EdgeInsets.all(6.0),
          child: Icon(
            isFavorite ? Icons.favorite : Icons.favorite_border,
            color: isFavorite ? Colors.red : Colors.white,
            size: 20.0,
          ),
        ),
      ),
    );
  }
}
