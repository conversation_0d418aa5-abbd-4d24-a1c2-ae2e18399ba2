import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

class SpecialNewsScreen extends StatelessWidget {
  const SpecialNewsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 模拟数据，实际项目中应该从API或其他数据源获取
    final List<Map<String, dynamic>> specialSources = [
      {
        'name': 'YouTube',
        'icon': SvgPicture.asset('assets/share/search_youtube.svg')
      },
      {
        'name': 'Reddit',
        'icon': SvgPicture.asset('assets/share/search_reddit.svg')
      },
      {
        'name': 'Google News',
        'icon': SvgPicture.asset('assets/share/search_google.svg')
      },
      {
        'name': 'Telegram',
        'icon': SvgPicture.asset('assets/share/search_telegram.svg')
      },
    ];

    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF292c42)
          : const Color(0xFFFFffff),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.black,
            size: 20,
          ),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'discover_others'.tr,
          style: TextStyle(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.black,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'add_news'.tr,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 2,
                  crossAxisSpacing: 0,
                  mainAxisSpacing: 0,
                ),
                itemCount: specialSources.length,
                itemBuilder: (context, index) {
                  final source = specialSources[index];
                  return _buildSpecialSourceCard(context, source);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecialSourceCard(
      BuildContext context, Map<String, dynamic> source) {
    return Card(
      elevation: 0,
      color: const Color(0xFFe6eaff),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // 根据不同的源类型设置type值
          int type = 0;
          switch (source['name']) {
            case 'YouTube':
              type = 5;
              break;
            case 'Reddit':
              type = 6;
              break;
            case 'Google News':
              type = 4;
              break;
            case 'Telegram':
              type = 3;
              break;
          }

          // 跳转到详情页面
          Get.toNamed(
            Routes.SPECIAL_NEWS_DETAIL,
            arguments: {
              'source': source,
              'type': type,
            },
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: Center(child: source['icon']),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      source['name'],
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFF333333)
                            : const Color(0xFF333333),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
