import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/services.dart';
import 'dart:io';

class SpecialNewsDetailScreen extends StatefulWidget {
  final Map<String, dynamic> source;
  final int type;

  const SpecialNewsDetailScreen({
    super.key,
    required this.source,
    required this.type,
  });

  @override
  State<SpecialNewsDetailScreen> createState() =>
      _SpecialNewsDetailScreenState();
}

class _SpecialNewsDetailScreenState extends State<SpecialNewsDetailScreen>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;

  // 视频控制器映射，为每个tab维护独立的视频控制器
  final Map<int, VideoPlayerController?> _videoControllers = {};
  final Map<int, ChewieController?> _chewieControllers = {};
  final Map<int, bool> _videoInitialized = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 4,
      vsync: this,
      initialIndex: _getTabIndexFromType(widget.type),
    );

    // 初始化视频控制器
    _initializeVideos();
  }

  int _getTabIndexFromType(int type) {
    switch (type) {
      case 5:
        return 0; // YouTube
      case 6:
        return 1; // Reddit
      case 4:
        return 2; // Google News
      case 3:
        return 3; // Telegram
      default:
        return 0;
    }
  }

  int _getTypeFromTabIndex(int index) {
    switch (index) {
      case 0:
        return 5; // YouTube
      case 1:
        return 6; // Reddit
      case 2:
        return 4; // Google News
      case 3:
        return 3; // Telegram
      default:
        return 5;
    }
  }

  String _getSourceNameFromTabIndex(int index) {
    switch (index) {
      case 0:
        return 'YouTube';
      case 1:
        return 'Reddit';
      case 2:
        return 'Google News';
      case 3:
        return 'Telegram';
      default:
        return 'YouTube';
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();

    // 释放所有视频控制器
    for (var controller in _videoControllers.values) {
      controller?.dispose();
    }
    for (var controller in _chewieControllers.values) {
      controller?.dispose();
    }

    super.dispose();
  }

  // 初始化视频控制器
  void _initializeVideos() {
    // 为有教程视频的tab初始化视频控制器（排除Google News）
    final videoTabs = [0, 1, 3]; // YouTube, Reddit, Telegram

    for (int tabIndex in videoTabs) {
      _initializeVideoForTab(tabIndex);
    }
  }

  // 为特定tab初始化视频
  void _initializeVideoForTab(int tabIndex) {
    String videoPath;

    switch (tabIndex) {
      case 0: // YouTube
        videoPath = 'assets/video/youtube.mp4';
        break;
      case 1: // Reddit
        videoPath = 'assets/video/reddit.mp4';
        break;
      case 3: // Telegram
        videoPath = 'assets/video/telegram.mp4';
        break;
      default:
        return;
    }
    try {
      final videoController = VideoPlayerController.asset(
        videoPath,
      );
      _videoControllers[tabIndex] = videoController;

      videoController.initialize().then((_) {
        if (mounted) {
          // 创建 ChewieController
          final chewieController = ChewieController(
            videoPlayerController: videoController,
            autoPlay: false,
            looping: false,
            aspectRatio: 16 / 9,
            allowFullScreen: true,
            draggableProgressBar: false,
            allowMuting: true,
            showControls: true,
            showControlsOnInitialize: false,
            showOptions: false,
            deviceOrientationsAfterFullScreen: [DeviceOrientation.portraitUp],
            progressIndicatorDelay:
                Platform.isAndroid ? const Duration(days: 1) : null,
            materialProgressColors: ChewieProgressColors(
              playedColor: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Theme.of(context).colorScheme.primary,
              handleColor: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Theme.of(context).colorScheme.primary,
              backgroundColor: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF5B5B5B)
                  : const Color(0xFFf0f1f7),
              bufferedColor: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF5B5B5B)
                  : const Color(0xFFf0f1f7),
            ),
            errorBuilder: (context, errorMessage) {
              return Center(
                child: Text(
                  '视频加载失败: $errorMessage',
                  style: TextStyle(color: Colors.white),
                ),
              );
            },
          );

          // 添加视频播放状态监听（放在这里确保chewieController已创建）
          videoController.addListener(() {
            if (mounted &&
                videoController.value.position >=
                    videoController.value.duration &&
                videoController.value.duration > Duration.zero) {
              // 重置到开始位置并暂停
              videoController.seekTo(Duration.zero);
              videoController.pause();
            }
          });

          setState(() {
            _chewieControllers[tabIndex] = chewieController;
            _videoInitialized[tabIndex] = true;
          });
        }
      }).catchError((error) {
        debugPrint('视频初始化失败: $error');
        setState(() {
          _videoInitialized[tabIndex] = false;
        });
      });
    } catch (e) {
      debugPrint('视频控制器创建失败: $e');
      setState(() {
        _videoInitialized[tabIndex] = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF3D3D3D)
          : const Color(0xFFF7FAFF),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        titleSpacing: 0,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: Icon(
            Icons.arrow_back,
            color: const Color(0xFF7F8EA7),
            size: 20.spx,
          ),
        ),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(90.spx),
          child: Column(
            children: [
              // 搜索框
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 30.spx, vertical: 10.spx),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                        vertical: 12.spx, horizontal: 12.spx),
                    filled: true,
                    fillColor: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFF5B5B5B)
                        : const Color(0xFFE5E9F1),
                    hintStyle: TextStyle(
                      fontSize: 14.spx,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Color(0XFF909090)
                          : Color(0XFFBCC2CC),
                    ),
                    suffixIcon: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 0.spx),
                      child: SvgPicture.asset(
                        'assets/feeds/feeds_search_weight.svg',
                        width: 16.spx,
                        height: 16.spx,
                        colorFilter: ColorFilter.mode(
                          Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFF909090)
                              : const Color(0xFFBCC2CC),
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    suffixIconConstraints: BoxConstraints(
                      minWidth: 36.spx,
                      maxHeight: 36.spx,
                    ),
                    hintText: 'search_feeds_placeholder'.tr,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(40.spx),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(40.spx),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(40.spx),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 1,
                      ),
                    ),
                  ),
                  onSubmitted: (value) {
                    if (value.isNotEmpty) {
                      Get.toNamed(Routes.SEARCH, arguments: {
                        'type': _getTypeFromTabIndex(_tabController.index),
                        'query': value,
                        'source':
                            _getSourceNameFromTabIndex(_tabController.index),
                      });
                    }
                  },
                ),
              ),
              // TabBar
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.spx),
                width: MediaQuery.of(context).size.width,
                child: AnimatedBuilder(
                  animation: _tabController,
                  builder: (context, child) {
                    return TabBar(
                      padding: EdgeInsets.zero,
                      dividerColor: Colors.transparent,
                      indicatorPadding: EdgeInsets.zero,
                      controller: _tabController,
                      indicatorSize: TabBarIndicatorSize.label,
                      indicatorAnimation: TabIndicatorAnimation.elastic,
                      indicatorWeight: 1,
                      isScrollable: false,
                      labelPadding: EdgeInsets.zero,
                      indicatorColor:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Theme.of(context).colorScheme.primary,
                      labelColor:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Theme.of(context).colorScheme.primary,
                      unselectedLabelColor:
                          Theme.of(context).brightness == Brightness.dark
                              ? Color(0xffD1D1D1)
                              : Color(0xff999999),
                      labelStyle: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 12.spx,
                      ),
                      tabs: [
                        _buildTab(
                            'YouTube', 'assets/share/search_youtube.svg', 0),
                        _buildTab(
                            'Reddit', 'assets/share/search_reddit.svg', 1),
                        _buildTab(
                            'Google', 'assets/share/search_google_news.svg', 2),
                        _buildTab(
                            'Telegram', 'assets/share/search_telegram.svg', 3),
                      ],
                    );
                  },
                ),
              ),
              Divider(
                  height: 1,
                  thickness: 1,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF747474)
                      : Color(0xffD5D5D5)),
            ],
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTabContent(0, 'YouTube'),
          _buildTabContent(1, 'Reddit'),
          _buildTabContent(2, 'Google News'),
          _buildTabContent(3, 'Telegram'),
        ],
      ),
    );
  }

  // 构建Tab
  Widget _buildTab(String title, String iconPath, int tabIndex) {
    return Tab(
      iconMargin: EdgeInsets.zero,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedBuilder(
            animation: _tabController,
            builder: (context, child) {
              // 计算当前tab的选中程度，考虑滑动动画
              double animationValue = _tabController.animation?.value ??
                  _tabController.index.toDouble();
              double distance = (animationValue - tabIndex).abs();
              bool isSelected = distance < 0.5; // 当距离小于0.5时认为是选中状态

              return SvgPicture.asset(iconPath,
                  width: 12.spx,
                  height: 14.spx,
                  colorFilter: !isSelected
                      ? ColorFilter.mode(
                          Theme.of(context).brightness == Brightness.dark
                              ? Color(0xffD1D1D1)
                              : Color(0xffb4bac5),
                          BlendMode.srcIn)
                      : null);
            },
          ),
          SizedBox(width: 3.spx),
          Text(title),
        ],
      ),
    );
  }

  // 构建Tab内容
  Widget _buildTabContent(int tabIndex, String sourceName) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(vertical: 20.spx, horizontal: 20.spx),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Text(
            'search_title'.tr + sourceName,
            style: TextStyle(
              fontSize: 14.spx,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          // 描述
          Text(
            _getSourceDescription(sourceName),
            style: TextStyle(
              fontSize: 12.spx,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Color(0xffD1D1D1)
                  : Color(0xff999999),
            ),
          ),
          const SizedBox(height: 16),

          // 教程视频（仅对YouTube、Reddit、Telegram显示）
          if (tabIndex != 2 && _videoInitialized[tabIndex] == true) ...[
            Container(
              width: double.infinity,
              height: 200.spx,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.black,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _chewieControllers[tabIndex] != null
                    ? Chewie(controller: _chewieControllers[tabIndex]!)
                    : Center(
                        child: CircularProgressIndicator(),
                      ),
              ),
            ),
            SizedBox(height: 15.spx),
          ],

          // 搜索规则
          ..._buildSearchRules(sourceName),
          SizedBox(height: 15.spx),

          // 搜索示例
          Text(
            'search_examples'.tr,
            style: TextStyle(
              fontSize: 12.spx,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Color(0xffD1D1D1)
                  : Color(0xff999999),
            ),
          ),
          SizedBox(height: 10.spx),
          Wrap(
            spacing: 10.spx,
            children: _buildSearchExamples(sourceName, tabIndex),
          ),
        ],
      ),
    );
  }

  String _getSourceDescription(String sourceName) {
    switch (sourceName) {
      case 'YouTube':
        return 'youtube_description'.tr;
      case 'Reddit':
        return 'reddit_description'.tr;
      case 'Google News':
        return 'google_news_description'.tr;
      case 'Telegram':
        return 'telegram_description'.tr;
      default:
        return '';
    }
  }

  List<Widget> _buildSearchRules(String sourceName) {
    switch (sourceName) {
      case 'YouTube':
        return [
          _buildRuleItem(
            'channel_search'.tr,
            'youtube_channel_example'.tr,
          ),
          const SizedBox(height: 8),
          _buildRuleItem(
            'url_search'.tr,
            'youtube_url_example'.tr,
          ),
        ];
      case 'Reddit':
        return [
          _buildRuleItem(
            'reddit_search'.tr,
            'reddit_url_example'.tr,
          ),
        ];
      case 'Google News':
        return [
          _buildRuleItem(
            'keyword_search'.tr,
            'google_news_keyword_example'.tr,
          ),
        ];
      case 'Telegram':
        return [
          _buildRuleItem(
            'telegram_channel_search'.tr,
            'telegram_channel_example'.tr,
          ),
          const SizedBox(height: 8),
          _buildRuleItem(
            'url_search'.tr,
            'telegram_url_example'.tr,
          ),
        ];
      default:
        return [];
    }
  }

  List<Widget> _buildSearchExamples(String sourceName, int tabIndex) {
    switch (sourceName) {
      case 'YouTube':
        return [
          _buildExampleItem('@livespeedy7451', tabIndex),
          _buildExampleItem(
              'https://www.youtube.com/@livespeedy7451', tabIndex),
          _buildExampleItem(
              'http://youtube.com/feeds/videos.xml?channel_id=UC2bW_AY9BlbYLGJSXAbjS4Q',
              tabIndex),
          _buildExampleItem('@TED', tabIndex),
        ];
      case 'Reddit':
        return [
          _buildExampleItem('https://www.reddit.com/r/malaysia', tabIndex),
          _buildExampleItem('https://www.reddit.com/r/malaysia.rss', tabIndex),
          _buildExampleItem('https://www.reddit.com/r/Helldivers', tabIndex),
          _buildExampleItem('https://www.reddit.com/r/chatGPT', tabIndex),
        ];
      case 'Google News':
        return [
          _buildExampleItem('technology news', tabIndex),
          _buildExampleItem('latest updates', tabIndex),
          _buildExampleItem('breaking news', tabIndex),
          _buildExampleItem('world news', tabIndex),
        ];
      case 'Telegram':
        return [
          _buildExampleItem('https://t.me/s/Durov', tabIndex),
          _buildExampleItem('durov', tabIndex),
          _buildExampleItem('News', tabIndex),
          _buildExampleItem('crypto news', tabIndex),
        ];
      default:
        return [];
    }
  }

  Widget _buildRuleItem(String title, String content) {
    return Container(
        padding: EdgeInsets.symmetric(vertical: 14.spx, horizontal: 10.spx),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.spx),
          color: Theme.of(context).brightness == Brightness.dark
              ? Color(0XFF565656)
              : Color(0XFFf0f1f7),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 13.spx,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 6.spx),
            Text(
              content,
              style: TextStyle(
                fontSize: 12.spx,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Color(0xffD1D1D1)
                    : Color(0xff999999),
              ),
            ),
          ],
        ));
  }

  Widget _buildExampleItem(String example, int tabIndex) {
    return InkWell(
      onTap: () {
        _searchController.text = example;
        Get.toNamed(Routes.SEARCH, arguments: {
          'type': _getTypeFromTabIndex(tabIndex),
          'query': example,
          'source': _getSourceNameFromTabIndex(tabIndex),
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 10.spx),
        padding: EdgeInsets.symmetric(horizontal: 14.spx, vertical: 13.spx),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Color(0XFF565656)
              : Color(0XFFF2F3F8),
          borderRadius: BorderRadius.circular(6.spx),
        ),
        child: Text(
          example,
          style: TextStyle(
            fontSize: 12.spx,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).brightness == Brightness.dark
                ? Color(0xffD1D1D1)
                : Color(0xff999999),
          ),
        ),
      ),
    );
  }
}
