import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/controllers/theme_controller.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:subfeeds/app/presentation/screens/settings/personal_info_screen.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>';
import 'package:subfeeds/app/presentation/widgets/custom_app_bar.dart';
import 'package:subfeeds/app/data/services/chat_history_service.dart';

/// 设置选项卡
class SettingsTab extends StatefulWidget {
  const SettingsTab({super.key});

  @override
  State<SettingsTab> createState() => _SettingsTabState();
}

class _SettingsTabState extends State<SettingsTab> with WidgetsBindingObserver {
  bool _isDarkMode = false;
  ThemeMode _themeMode = ThemeMode.system;
  String _selectedLanguage = 'English';
  String _selectedFontSize = 'settings_medium'.tr;
  String _selectedFontFamily = 'Roboto';
  late UserController _userController;
  late ThemeController _themeController;
  late HomeController _homeController;

  // 缓存大小相关
  String _cacheSize = 'calculating'.tr;
  bool _isCalculatingCacheSize = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _themeController = Get.find<ThemeController>();
    _userController = Get.find<UserController>();
    _homeController = Get.find<HomeController>();
    _loadSettings();
    _userController.checkLoginStatus();
    _calculateCacheSize(); // 计算缓存大小
    // 监听主题变化
    ever(_themeController.currentThemeMode, (ThemeMode mode) {
      if (mounted) {
        setState(() {
          _themeMode = mode;
          if (mode == ThemeMode.system) {
            _isDarkMode =
                WidgetsBinding.instance.platformDispatcher.platformBrightness ==
                    Brightness.dark;
          } else {
            _isDarkMode = mode == ThemeMode.dark;
          }
        });
      }
    });

    // 监听暗黑模式变化
    ever(_themeController.isDarkMode, (bool darkMode) {
      if (mounted) {
        setState(() {
          _isDarkMode = darkMode;
        });
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    if (_themeMode == ThemeMode.system) {
      setState(() {
        _isDarkMode =
            WidgetsBinding.instance.platformDispatcher.platformBrightness ==
                Brightness.dark;
      });
    }
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      // 从 ThemeController 获取主题设置
      _themeMode = _themeController.currentThemeMode.value;

      if (_themeMode == ThemeMode.system) {
        _isDarkMode =
            WidgetsBinding.instance.platformDispatcher.platformBrightness ==
                Brightness.dark;
      } else {
        _isDarkMode = _themeMode == ThemeMode.dark;
      }

      _selectedLanguage =
          prefs.getString('locale') == 'en_US' ? 'English' : '简体中文';
      _selectedFontSize = prefs.getString('fontSize') ?? 'settings_medium'.tr;
      _selectedFontFamily = prefs.getString('fontFamily') ?? 'Roboto';
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      'locale',
      _selectedLanguage == 'English' ? 'en_US' : 'zh_CN',
    );
    await prefs.setString('fontSize', _selectedFontSize);
    await prefs.setString('fontFamily', _selectedFontFamily);
  }

  void _updateLocale() {
    final locale = _selectedLanguage == 'English'
        ? const Locale('en', 'US')
        : const Locale('zh', 'CN');
    Get.updateLocale(locale);
  }

  @override
  Widget build(BuildContext context) {
    // 获取HomeController实例以访问抽屉控制方法
    final homeController = Get.find<HomeController>();

    // 创建背景色，基于当前主题
    final backgroundColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF1e2020)
        : const Color(0xFFeef2f9);
    final appBarBackgroundColor =
        Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1e2020)
            : const Color(0xFFFFFFFF);
    return Scaffold(
        backgroundColor: backgroundColor,
        // 替换原有AppBar为CustomAppBar
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(50.0),
          child: CustomAppBar(
            title: 'nav_settings'.tr,
            backgroundColor: appBarBackgroundColor,
            titleStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontSize: 16.spx,
                  fontWeight: FontWeight.w600,
                ),
            height: 56.0,
            extendToTopSafeArea: true,
          ),
        ),
        body: SafeArea(
          top: false, // 因为AppBar已经处理了顶部安全区域
          child: Obx(
            () => ListView(
              children: [
                Divider(
                  height: 0.5,
                  thickness: 0.5,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF747474)
                      : const Color(0xFFD5D5D5),
                ),
                // Padding(
                //   padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                //   child: Container(
                //     height: 72,
                //     width: double.infinity,
                //     decoration: BoxDecoration(
                //       borderRadius: BorderRadius.circular(12),
                //       image: const DecorationImage(
                //         image: AssetImage('assets/setting/plan_bg.png'),
                //         fit: BoxFit.cover,
                //       ),
                //     ),
                //     child: InkWell(
                //       onTap: () {
                //         Get.toNamed(Routes.PREMIUM_PLAN);
                //       },
                //       child: Padding(
                //         padding: const EdgeInsets.symmetric(horizontal: 16),
                //         child: Row(
                //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //           children: [
                //             Expanded(
                //               child: Column(
                //                 crossAxisAlignment: CrossAxisAlignment.start,
                //                 mainAxisAlignment: MainAxisAlignment.center,
                //                 children: [
                //                   ShaderMask(
                //                     shaderCallback: (bounds) => LinearGradient(
                //                       colors: [
                //                         Color(0xFFF7A071),
                //                         Color(0xFFC472DF),
                //                         Color(0xFF6D8BFF)
                //                       ],
                //                       begin: Alignment.centerLeft,
                //                       end: Alignment.centerRight,
                //                     ).createShader(bounds),
                //                     child: Text(
                //                       'Free Plan',
                //                       style: TextStyle(
                //                         color: Colors.white,
                //                         fontSize: 18,
                //                         fontWeight: FontWeight.w900,
                //                       ),
                //                     ),
                //                   ),
                //                   const SizedBox(height: 8),
                //                   Text(
                //                     'settings_upgrade_tip'.tr,
                //                     style: Theme.of(context)
                //                         .textTheme
                //                         .bodySmall
                //                         ?.copyWith(
                //                           color: const Color(0xFF8E8E93),
                //                           fontSize: 12,
                //                         ),
                //                   ),
                //                 ],
                //               ),
                //             ),
                //             Container(
                //               height: 36,
                //               decoration: BoxDecoration(
                //                 color: Colors.white,
                //                 borderRadius: BorderRadius.circular(18),
                //               ),
                //               child: TextButton(
                //                 onPressed: () {
                //                   Get.toNamed(Routes.PREMIUM_PLAN);
                //                 },
                //                 style: TextButton.styleFrom(
                //                   padding:
                //                       const EdgeInsets.symmetric(horizontal: 16),
                //                 ),
                //                 child: Text(
                //                   'upgrade'.tr,
                //                   style: Theme.of(context)
                //                       .textTheme
                //                       .labelLarge
                //                       ?.copyWith(
                //                         color: Colors.black,
                //                         fontWeight: FontWeight.bold,
                //                       ),
                //                 ),
                //               ),
                //             ),
                //           ],
                //         ),
                //       ),
                //     ),
                //   ),
                // ),
                if (_userController.user.value != null) ...[
                  _buildSettingGroup(context, 'settings_account'.tr, [
                    _buildSettingItem(
                      context,
                      icon: 'assets/setting/setting_info.svg',
                      title: 'settings_personal_info'.tr,
                      // subtitle: 'settings_personal_info_description'.tr,
                      onTap: () {
                        Get.to(() => const PersonalInfoScreen(),
                            transition: Transition.rightToLeft,
                            duration: const Duration(milliseconds: 100));
                      },
                    ),
                  ]),
                ],
                _buildSettingGroup(context, 'settings_preferences'.tr, [
                  _buildSettingItem(
                    context,
                    icon: 'assets/setting/setting_theme.svg',
                    title: 'settings_theme'.tr,
                    // subtitle: _themeMode == ThemeMode.system
                    //     ? 'settings_theme_follow_system'.tr
                    //     : _isDarkMode
                    //         ? 'settings_dark_mode'.tr
                    //         : 'settings_light_mode'.tr,
                    onTap: () {
                      _showThemeSelectionBottomSheet();
                    },
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.spx),
                    child: Divider(
                      height: 0.5,
                      thickness: 0.5,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFF747474)
                          : const Color(0xFFD5D5D5),
                    ),
                  ),
                  _buildSettingItem(
                    context,
                    icon: 'assets/setting/setting_lang.svg',
                    title: 'settings_language'.tr,
                    subtitle: _selectedLanguage,
                    onTap: () {
                      _showLanguageSelectionDialog();
                    },
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.spx),
                    child: Divider(
                      height: 0.5,
                      thickness: 0.5,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFF747474)
                          : const Color(0xFFD5D5D5),
                    ),
                  ),
                  _buildSettingItem(
                    context,
                    icon: 'assets/setting/setting_fontsize.svg',
                    title: 'settings_font_size'.tr,
                    subtitle: _selectedFontSize,
                    onTap: () {
                      _showFontSizeSelectionDialog();
                    },
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.spx),
                    child: Divider(
                      height: 0.5,
                      thickness: 0.5,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFF747474)
                          : const Color(0xFFD5D5D5),
                    ),
                  ),
                  _buildSettingItem(
                    context,
                    icon: 'assets/setting/setting_fontfamily.svg',
                    title: 'settings_font_family'.tr,
                    subtitle: _selectedFontFamily,
                    onTap: () {
                      _showFontFamilySelectionDialog();
                    },
                  ),
                ]),
                _buildSettingGroup(context, 'settings_help'.tr, [
                  _buildSettingItem(
                    context,
                    icon: 'assets/setting/setting_cache.svg',
                    title: 'clear_cache'.tr,
                    subtitle: 'clear_cache_description'.tr,
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _isCalculatingCacheSize
                            ? SizedBox(
                                width: 12.spx,
                                height: 12.spx,
                                child: CircularProgressIndicator(
                                  strokeWidth: 1.5,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? const Color(0xFFD1D1D1)
                                        : const Color(0xFF999999),
                                  ),
                                ),
                              )
                            : Text(
                                _cacheSize,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      fontSize: 10.spx,
                                      color: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? const Color(0xFFD1D1D1)
                                          : const Color(0xFF999999),
                                    ),
                              ),
                        SizedBox(width: 2.spx),
                        Icon(
                          Icons.chevron_right,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFFD1D1D1)
                              : const Color(0xFF999999),
                          size: 20.spx,
                        ),
                      ],
                    ),
                    onTap: () {
                      _clearCache();
                    },
                  ),
                  // _buildSettingItem(
                  //   context,
                  //   icon: 'assets/setting/rate.svg',
                  //   title: 'settings_rate'.tr,
                  //   subtitle: 'settings_rate_description'.tr,
                  //   onTap: () {
                  //     // TODO: 打开应用商店评分
                  //   },
                  // ),
                  // Padding(
                  //   padding: EdgeInsets.symmetric(horizontal: 16),
                  //   child: Divider(
                  //     height: 0.5,
                  //     color: Them\
                  if (_userController.user.value != null)
                    _buildSettingItem(
                      context,
                      icon: 'assets/setting/signout.svg',
                      title: 'settings_logout'.tr,
                      subtitle: 'settings_logout_description'.tr,
                      onTap: () {
                        _logout();
                      },
                    )
                  else
                    _buildSettingItem(
                      context,
                      icon: 'assets/setting/setting_info.svg',
                      title: 'settings_login'.tr,
                      subtitle: 'settings_login_description'.tr,
                      onTap: () {
                        Get.offAllNamed(Routes.LOGIN_SELECTION);
                      },
                    ),
                ]),
                const SizedBox(height: 32),
              ],
            ),
          ),
        ));
  }

  Widget _buildSettingGroup(
    BuildContext context,
    String title,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.fromLTRB(20.spx, 10.spx, 20.spx, 8.spx),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFFD1D1D1)
                      : const Color(0xFF999999),
                  fontSize: 12.spx,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.spx),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF3B3B3B)
                  : Colors.white,
              borderRadius: BorderRadius.circular(8.spx),
            ),
            child: Column(
              children: children,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSettingItem(
    BuildContext context, {
    required String icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      // leading: SvgPicture.asset(
      //   icon,
      //   width: 30.spx,
      //   height: 30.spx,
      // ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontSize: 14.spx,
              fontWeight: FontWeight.w500,
            ),
      ),
      subtitle: null,
      // ? Text(
      //     subtitle,
      //     style: Theme.of(context).textTheme.bodySmall?.copyWith(
      //           fontSize: 14,
      //         ),
      //   )
      // : null,
      trailing: trailing ??
          Icon(
            Icons.chevron_right,
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFFD1D1D1)
                : const Color(0xFF999999),
            size: 20.spx,
          ),
      onTap: onTap,
    );
  }

  void _showLanguageSelectionDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF3B3B3B)
                : const Color(0xFFF7FAFF),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 8),
              Container(
                width: 32.spx,
                height: 4.spx,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF575b6b)
                      : const Color(0xFFe5e6e9),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                  child: Text(
                    'settings_language'.tr,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 12.spx,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFFD1D1D1)
                            : const Color(0xFF999999)),
                  ),
                ),
              ),
              ListTile(
                title: Text(
                  '简体中文',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 14.spx,
                        color: _selectedLanguage == '简体中文'
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF333333),
                      ),
                ),
                trailing: _selectedLanguage == '简体中文'
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () {
                  _setLanguage('简体中文');
                },
              ),
              ListTile(
                title: Text(
                  'English',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 14.spx,
                        color: _selectedLanguage == 'English'
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF333333),
                      ),
                ),
                trailing: _selectedLanguage == 'English'
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () {
                  _setLanguage('English');
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  void _showFontSizeSelectionDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF3B3B3B)
                : const Color(0xFFF7FAFF),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 8),
              Container(
                width: 32.spx,
                height: 4.spx,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF575b6b)
                      : const Color(0xFFe5e6e9),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                  child: Text(
                    'settings_font_size'.tr,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 12.spx,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFFD1D1D1)
                            : const Color(0xFF999999)),
                  ),
                ),
              ),
              ListTile(
                title: Text(
                  'settings_small'.tr,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 14.spx,
                        color: _selectedFontSize == 'settings_small'.tr
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF333333),
                      ),
                ),
                trailing: _selectedFontSize == 'settings_small'.tr
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () {
                  _setFontSize('settings_small'.tr);
                },
              ),
              ListTile(
                title: Text(
                  'settings_medium'.tr,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 14.spx,
                        color: _selectedFontSize == 'settings_medium'.tr
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF333333),
                      ),
                ),
                trailing: _selectedFontSize == 'settings_medium'.tr
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () {
                  _setFontSize('settings_medium'.tr);
                },
              ),
              ListTile(
                title: Text(
                  'settings_large'.tr,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 14.spx,
                        color: _selectedFontSize == 'settings_large'.tr
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF333333),
                      ),
                ),
                trailing: _selectedFontSize == 'settings_large'.tr
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () {
                  _setFontSize('settings_large'.tr);
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  void _showFontFamilySelectionDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF3B3B3B)
                : const Color(0xFFF7FAFF),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 8),
              Container(
                width: 32.spx,
                height: 4.spx,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF575b6b)
                      : const Color(0xFFe5e6e9),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                  child: Text(
                    'settings_font_family'.tr,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 12.spx,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFFD1D1D1)
                            : const Color(0xFF999999)),
                  ),
                ),
              ),
              ListTile(
                title: Text(
                  'Source Serif 4',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 14.spx,
                        color: _selectedFontFamily == 'SourceSerif4'
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF333333),
                      ),
                ),
                trailing: _selectedFontFamily == 'SourceSerif4'
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () {
                  _setFontFamily('SourceSerif4');
                },
              ),
              ListTile(
                title: Text(
                  'Roboto',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 14.spx,
                        color: _selectedFontFamily == 'Roboto'
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF333333),
                      ),
                ),
                trailing: _selectedFontFamily == 'Roboto'
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () {
                  _setFontFamily('Roboto');
                },
              ),
              ListTile(
                title: Text(
                  'Montserrat',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 14.spx,
                        color: _selectedFontFamily == 'Montserrat'
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF333333),
                      ),
                ),
                trailing: _selectedFontFamily == 'Montserrat'
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () {
                  _setFontFamily('Montserrat');
                },
              ),
              ListTile(
                title: Text(
                  'Poppins',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 14.spx,
                        color: _selectedFontFamily == 'Poppins'
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFFFFFFFF)
                                : const Color(0xFF333333),
                      ),
                ),
                trailing: _selectedFontFamily == 'Poppins'
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () {
                  _setFontFamily('Poppins');
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  Future _logout() async {
    Get.dialog(
      Dialog(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF3B3B3B)
            : const Color(0xFFF7FAFF),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 14.spx, vertical: 16.spx),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.info_rounded,
                color: const Color(0xFFFFC107),
                size: 40.spx,
              ),
              SizedBox(height: 16.spx),
              Text(
                'settings_logout_confirm'.tr,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontSize: 14.spx,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFFD1D1D1)
                          : const Color(0xFF999999),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        backgroundColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFF555555)
                                : const Color(0xFFE6E6E6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFFD1D1D1)
                                  : const Color(0xFF999999),
                              fontSize: 14.spx,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ),
                  ),
                  SizedBox(width: 16.spx),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        Get.back(); // 关闭对话框
                        await Get.find<UserController>().logout();
                        Get.offAllNamed(Routes.LOGIN_SELECTION);
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'confirm'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontSize: 14.spx,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _setLanguage(String language) {
    setState(() {
      _selectedLanguage = language;
    });
    _saveSettings();
    _updateLocale();
    Navigator.pop(context);
  }

  void _setFontSize(String fontSize) {
    setState(() {
      _selectedFontSize = fontSize;
    });
    _saveSettings();
    Navigator.pop(context);
  }

  void _setFontFamily(String fontFamily) {
    setState(() {
      _selectedFontFamily = fontFamily;
    });
    _saveSettings();
    Navigator.pop(context);
  }

  void _showThemeSelectionBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF3B3B3B)
                : const Color(0xFFF7FAFF),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 8),
              Container(
                width: 32.spx,
                height: 4.spx,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF575b6b)
                      : const Color(0xFFe5e6e9),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 24),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildThemeOption(
                      context,
                      title: 'settings_theme_follow_system'.tr,
                      imagePath: 'assets/setting/theme_os.png',
                      isSelected: _themeMode == ThemeMode.system,
                      onTap: () {
                        _applyThemeChangeWithPosition(
                            context, ThemeMode.system);
                      },
                    ),
                    _buildThemeOption(
                      context,
                      title: 'settings_theme_light'.tr,
                      imagePath: 'assets/setting/theme_light.png',
                      isSelected: _themeMode == ThemeMode.light,
                      onTap: () {
                        _applyThemeChangeWithPosition(context, ThemeMode.light);
                      },
                    ),
                    _buildThemeOption(
                      context,
                      title: 'settings_theme_dark'.tr,
                      imagePath: 'assets/setting/theme_dark.png',
                      isSelected: _themeMode == ThemeMode.dark,
                      onTap: () {
                        _applyThemeChangeWithPosition(context, ThemeMode.dark);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
            ],
          ),
        );
      },
    );
  }

  Widget _buildThemeOption(
    BuildContext context, {
    required String title,
    required String imagePath,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 100.spx,
            height: 100.spx,
            decoration: BoxDecoration(
              border: isSelected
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    )
                  : null,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: Image.asset(
                imagePath,
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontSize: 14,
                ),
          ),
        ],
      ),
    );
  }

  /// 应用主题切换 - 带位置感知的涟漪动画
  void _applyThemeChangeWithPosition(
      BuildContext buttonContext, ThemeMode mode) {
    try {
      debugPrint('开始切换主题: $mode');

      // 添加触觉反馈
      HapticFeedback.heavyImpact();

      // 获取按钮的渲染盒子，用于计算动画起始位置
      final RenderBox? buttonRenderBox =
          buttonContext.findRenderObject() as RenderBox?;

      if (buttonRenderBox != null) {
        // 计算按钮中心位置作为动画起始点
        final buttonSize = buttonRenderBox.size;
        final buttonCenter =
            Offset(buttonSize.width / 2, buttonSize.height / 2);
        final globalPosition = buttonRenderBox.localToGlobal(buttonCenter);

        debugPrint('动画起始位置: $globalPosition');

        // 设置动画起始位置
        _themeController.toggleThemeModeFromGlobalPosition(globalPosition);
      } else {
        debugPrint('无法获取按钮位置，使用默认位置');
        // 使用屏幕中心作为默认位置
        _themeController.themeTogglePosition.value = const Offset(0.5, 0.5);
      }

      // 先关闭底部弹窗
      Navigator.pop(context);

      // 设置主题模式
      _themeController.setThemeMode(mode);

      // 更新本地状态
      if (mounted) {
        setState(() {
          _themeMode = mode;
          if (mode == ThemeMode.system) {
            _isDarkMode =
                WidgetsBinding.instance.platformDispatcher.platformBrightness ==
                    Brightness.dark;
          } else {
            _isDarkMode = mode == ThemeMode.dark;
          }
        });
      }

      debugPrint('主题切换完成: $mode');
    } catch (e) {
      debugPrint('应用主题变更失败: $e');
    }
  }

  /// 清除所有缓存
  Future<void> _clearCache() async {
    // 显示确认对话框，复用_logout方法的样式
    Get.dialog(
      Dialog(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF3B3B3B)
            : const Color(0xFFF7FAFF),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 14.spx, vertical: 16.spx),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.cleaning_services_rounded,
                color: const Color(0xFFFFC107),
                size: 40.spx,
              ),
              SizedBox(height: 16.spx),
              Text(
                'clear_cache_description'.tr,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontSize: 14.spx,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFFD1D1D1)
                          : const Color(0xFF999999),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        backgroundColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFF555555)
                                : const Color(0xFFE6E6E6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFFD1D1D1)
                                  : const Color(0xFF999999),
                              fontSize: 14.spx,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ),
                  ),
                  SizedBox(width: 16.spx),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        Get.back(); // 关闭对话框
                        await _performClearCache();
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'confirm'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontSize: 14.spx,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 执行清除缓存操作
  Future<void> _performClearCache() async {
    try {
      // 显示加载指示器
      Get.dialog(
        Dialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF3B3B3B)
              : const Color(0xFFF7FAFF),
          child: Padding(
            padding: EdgeInsets.all(20.spx),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                SizedBox(height: 16.spx),
                Text(
                  'clearing_cache'.tr,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: 14.spx,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFFD1D1D1)
                            : const Color(0xFF999999),
                      ),
                ),
              ],
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 1. 清除聊天历史记录
      final chatHistoryService = ChatHistoryService();
      await chatHistoryService.clearAllHistory();

      // 关闭加载指示器
      Get.back();
      // 显示成功提示
      Get.snackbar(
        'success'.tr,
        'cache_cleared_successfully'.tr,
        icon: SvgPicture.asset(
          'assets/feeds/right.svg',
        ),
        snackPosition: SnackPosition.TOP,
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF161616)
            : const Color(0xFF161616),
        duration: const Duration(seconds: 2),
        margin: EdgeInsets.all(16.spx),
        colorText: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFFffffff)
            : const Color(0xFFffffff),
      );

      // 重新计算缓存大小
      _calculateCacheSize();
    } catch (e) {
      debugPrint('❌ 清除缓存失败: $e');

      // 关闭加载指示器
      Get.back();

      // 显示错误提示
      Get.snackbar(
        'error'.tr,
        'cache_clear_failed'.tr,
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
        margin: EdgeInsets.all(16.spx),
      );
    }
  }

  /// 计算缓存大小
  Future<void> _calculateCacheSize() async {
    try {
      debugPrint('开始计算缓存大小...');

      int totalSize = 0;

      // 1. 计算聊天历史数据库大小
      final chatHistoryService = ChatHistoryService();
      final dbSize = await chatHistoryService.getDatabaseSize();

      // 检查数据库是否真的有数据
      final conversationCount = await chatHistoryService.getConversationCount();

      // 如果没有对话记录，且数据库小于50KB，认为是空数据库
      if (conversationCount == 0 && dbSize < 50 * 1024) {
        totalSize = 0; // 显示为0KB
        debugPrint('数据库为空，显示缓存大小为0KB（实际文件大小: ${_formatBytes(dbSize)}）');
      } else {
        totalSize += dbSize;
        debugPrint('聊天历史数据库大小: ${_formatBytes(dbSize)}');
      }

      // 更新UI
      if (mounted) {
        setState(() {
          _cacheSize = _formatBytes(totalSize);
          _isCalculatingCacheSize = false;
        });
      }

      debugPrint('✅ 缓存大小计算完成: ${_formatBytes(totalSize)}');
    } catch (e) {
      if (mounted) {
        setState(() {
          _cacheSize = 'unknown'.tr;
          _isCalculatingCacheSize = false;
        });
      }
    }
  }

  /// 格式化字节大小
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }
}
