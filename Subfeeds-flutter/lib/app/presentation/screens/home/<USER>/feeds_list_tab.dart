import 'package:flutter/material.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/feeds_list_view.dart';

/// 订阅选项卡
class FeedsListTab extends StatelessWidget {
  /// 控制器
  final FeedsController controller;

  const FeedsListTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FeedsListView(controller: controller);
  }
}
