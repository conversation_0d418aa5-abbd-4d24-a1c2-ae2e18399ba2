import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>';
import 'package:subfeeds/app/controllers/theme_controller.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/home_utils.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/home_app_bar.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/carousel_slider_widget.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/article_list.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';

/// 首页选项卡
class HomeTab extends StatefulWidget {
  const HomeTab({super.key});
  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  final HomeController _homeController = Get.find<HomeController>();
  final ThemeController _themeController = Get.find<ThemeController>();
  final UserController _userController = Get.find<UserController>();
  final TextEditingController _searchController = TextEditingController();

  // 控制加载更多状态
  final RxBool _isLoadingMore = false.obs;

  @override
  void initState() {
    super.initState();
    _homeController.getIsDarkMode();

    // 监听搜索框文本变化
    _searchController.addListener(() {
      _homeController.setSearchText(_searchController.text);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 上拉加载更多
  Future<void> _onLoading() async {
    if (_isLoadingMore.value || !_homeController.hasMoreData.value) return;

    _isLoadingMore.value = true;
    await _homeController.loadMoreData();
    _isLoadingMore.value = false;
  }

  /// 获取骨架屏颜色
  static Color? getSkeletonColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color.fromRGBO(97, 97, 97, 1)
        : const Color.fromRGBO(224, 224, 224, 1);
  }

  /// 生成骨架屏轮播图数据
  List<Map<String, dynamic>> _generateSkeletonSlideData() {
    return List.generate(
        3,
        (index) => {
              'id': 'skeleton_slide_$index',
              'title': 'Loading Article Title ${index + 1}',
              'description':
                  'This is a loading description for the article that will be replaced with real content when the data loads from the server.',
              'img': '',
              'feedsImg': '',
              'feedsName': 'Loading Feed Name',
              'pubDate': DateTime.now().millisecondsSinceEpoch,
              'isSkeleton': true,
            });
  }

  /// 生成骨架屏文章列表数据
  List<Map<String, dynamic>> _generateSkeletonArticleData() {
    return List.generate(
        8,
        (index) => {
              'id': 'skeleton_article_$index',
              'title': 'Loading Article Title ${index + 1}',
              'description':
                  'This is a loading description for the article that will be replaced with real content when the data loads from the server.',
              'img': '',
              'feedsImg': '',
              'feedsName': 'Loading Feed Name',
              'pubDate': DateTime.now().millisecondsSinceEpoch,
              'isSkeleton': true,
            });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isLoading = _homeController.isLoading.value;
      final baseColor = getSkeletonColor(context);

      return Scaffold(
          backgroundColor: HomeColors.getBackgroundColor(context),
          resizeToAvoidBottomInset: false,
          body: Skeletonizer(
            enabled: isLoading && _homeController.filteredArticles.isEmpty,
            effect: ShimmerEffect(
              baseColor: baseColor ?? Colors.grey[300]!,
              highlightColor: const Color.fromRGBO(245, 245, 245, 1),
              duration: const Duration(seconds: 1),
            ),
            child: RefreshIndicator(
              onRefresh: () async {
                await _homeController.refreshAllData();
              },
              child: NotificationListener<ScrollNotification>(
                onNotification: (ScrollNotification scrollInfo) {
                  // 检测是否接近底部（还剩20%的内容），并且不是正在加载更多
                  if (scrollInfo.metrics.pixels >
                          scrollInfo.metrics.maxScrollExtent * 0.8 &&
                      !_isLoadingMore.value &&
                      _homeController.hasMoreData.value) {
                    _onLoading();
                  }
                  return false;
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 6.spx),
                  child: CustomScrollView(
                    controller: _homeController.scrollController,
                    slivers: [
                      // AppBar
                      SliverToBoxAdapter(
                        child: HomeAppBar(
                          themeController: _themeController,
                          userController: _userController,
                          homeController: _homeController,
                        ),
                      ),

                      // 轮播图区域
                      SliverToBoxAdapter(
                        child: Container(
                          decoration: BoxDecoration(
                            color: HomeColors.getCardColor(context),
                            borderRadius: BorderRadius.all(
                              Radius.circular(15.spx),
                            ),
                          ),
                          padding: EdgeInsets.only(top: 8.spx),
                          margin: EdgeInsets.only(bottom: 13.5.spx),
                          child: CarouselSliderWidget(
                            articles: isLoading &&
                                    _homeController.slideArticles.isEmpty
                                ? RxList<Map<String, dynamic>>.from(
                                    _generateSkeletonSlideData())
                                : _homeController.slideArticles,
                            isLoading: _homeController.isLoading.value,
                          ),
                        ),
                      ),

                      // 推荐标题
                      SliverToBoxAdapter(
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 12.spx,
                            vertical: 12.spx,
                          ),
                          decoration: BoxDecoration(
                            color: HomeColors.getCardColor(context),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15.spx),
                              topRight: Radius.circular(15.spx),
                            ),
                          ),
                          child: Text(
                            'home_recommended'.tr,
                            textAlign: TextAlign.left,
                            style: TextStyle(
                              fontSize: 16.spx,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? Colors.white
                                  : Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                      ),

                      // 内容区域（文章列表）
                      SliverToBoxAdapter(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(15.spx),
                              bottomRight: Radius.circular(15.spx),
                            ),
                            color: HomeColors.getCardColor(context),
                          ),
                          child: ArticleList(
                            articles: isLoading &&
                                    _homeController.filteredArticles.isEmpty
                                ? RxList<Map<String, dynamic>>.from(
                                    _generateSkeletonArticleData())
                                : _homeController.filteredArticles,
                            isLoading: _homeController.isLoading.value,
                            homeController: _homeController,
                            searchText: _homeController.searchText.value,
                          ),
                        ),
                      ),

                      // 加载更多指示器
                      if (_isLoadingMore.value)
                        SliverToBoxAdapter(
                          child: LoadingIndicator(),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ));
    });
  }
}
