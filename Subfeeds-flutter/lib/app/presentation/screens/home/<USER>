import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>';
import 'package:subfeeds/app/presentation/screens/home/<USER>/discover_tab.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_tab.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_tab.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/settings_tab.dart';

/// 首页（主页面）
class HomeScreen extends GetView<HomeController> {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 检测当前设备是否为桌面或平板
    final bool isLargeScreen = MediaQuery.of(context).size.width > 600;
    final bool isDesktop = MediaQuery.of(context).size.width > 1024;

    return Obx(() {
      if (isDesktop) {
        // 桌面布局 - 左侧导航栏
        return Scaffold(
          body: Row(
            children: [
              _buildNavigationRail(context),
              const VerticalDivider(width: 1, thickness: 1),
              Expanded(child: _buildCurrentPage()),
            ],
          ),
        );
      } else if (isLargeScreen &&
          MediaQuery.of(context).orientation == Orientation.landscape) {
        // 平板横屏布局 - 左侧导航栏
        return Scaffold(
          body: Row(
            children: [
              _buildNavigationRail(context),
              const VerticalDivider(width: 1, thickness: 1),
              Expanded(child: _buildCurrentPage()),
            ],
          ),
        );
      } else {
        // 手机和平板竖屏布局 - 底部导航栏
        return Scaffold(
          body: _buildCurrentPage(),
          bottomNavigationBar: _buildBottomNavigationBar(),
        );
      }
    });
  }

  /// 构建当前页面
  Widget _buildCurrentPage() {
    switch (controller.currentIndex.value) {
      case 0:
        return const HomeTab();
      case 1:
        return const DiscoverTab();
      case 2:
        return const FeedsTab();
      case 3:
        return const SettingsTab();
      default:
        return const HomeTab();
    }
  }

  /// 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: controller.currentIndex.value,
      onTap: controller.changePage,
      type: BottomNavigationBarType.fixed,
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.home_outlined),
          activeIcon: const Icon(Icons.home),
          label: 'nav_home'.tr,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.search_outlined),
          activeIcon: const Icon(Icons.search),
          label: 'nav_discover'.tr,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.rss_feed_outlined),
          activeIcon: const Icon(Icons.rss_feed),
          label: 'nav_feeds'.tr,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.settings_outlined),
          activeIcon: const Icon(Icons.settings),
          label: 'nav_settings'.tr,
        ),
      ],
    );
  }

  /// 构建导航栏（桌面端和平板横屏）
  Widget _buildNavigationRail(BuildContext context) {
    return NavigationRail(
      selectedIndex: controller.currentIndex.value,
      onDestinationSelected: controller.changePage,
      labelType: NavigationRailLabelType.all,
      destinations: [
        NavigationRailDestination(
          icon: const Icon(Icons.home_outlined),
          selectedIcon: const Icon(Icons.home),
          label: Text('nav_home'.tr),
        ),
        NavigationRailDestination(
          icon: const Icon(Icons.search_outlined),
          selectedIcon: const Icon(Icons.search),
          label: Text('nav_discover'.tr),
        ),
        NavigationRailDestination(
          icon: const Icon(Icons.rss_feed_outlined),
          selectedIcon: const Icon(Icons.rss_feed),
          label: Text('nav_feeds'.tr),
        ),
        NavigationRailDestination(
          icon: const Icon(Icons.settings_outlined),
          selectedIcon: const Icon(Icons.settings),
          label: Text('nav_settings'.tr),
        ),
      ],
    );
  }
}
