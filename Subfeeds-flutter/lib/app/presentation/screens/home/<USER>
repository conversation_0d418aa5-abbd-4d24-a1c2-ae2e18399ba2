import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart'; // 导入SVG包
import 'package:flutter_animate/flutter_animate.dart'; // 导入flutter_animate库
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>';
import 'package:subfeeds/app/presentation/screens/home/<USER>/discover_tab.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_tab.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_tab.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/settings_tab.dart';

/// 首页（主页面）
class HomeScreen extends GetView<HomeController> {
  const HomeScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // 手机和平板竖屏布局 - 使用抽屉导航
      return Scaffold(
        key: controller.scaffoldKey,
        body: Stack(
          children: [
            _buildCurrentPage(), //
            Positioned(
              bottom: 25.spx,
              left: 0,
              right: 0,
              child: _buildBottomNavigationBar(context),
            ),
          ],
        ),
      );
    });
  }

  /// 构建当前页面
  Widget _buildCurrentPage() {
    // 添加底部内边距，防止内容被底部导航栏遮挡
    return _buildTabContent();
  }

  /// 构建Tab内容
  Widget _buildTabContent() {
    switch (controller.currentIndex.value) {
      case 0:
        return const HomeTab();
      case 1:
        return const DiscoverTab();
      case 2:
        return FeedsTab();
      case 3:
        return const SettingsTab();
      default:
        return const HomeTab();
    }
  }

// 构建底部导航栏
  Widget _buildBottomNavigationBar(BuildContext context) {
    final controller = Get.find<HomeController>();

    return Container(
      color: Colors.transparent, // 透明背景
      child: Center(
        child: Container(
          width: 292.spx,
          height: 70.spx,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF919192) // 暗色模式背景色
                : const Color(0xFFdddde7), // 亮色模式背景色
            borderRadius: BorderRadius.circular(60.spx), // 圆角
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Obx(() => Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildNavItem(
                    context,
                    'assets/nav/Home.svg',
                    'nav_home'.tr,
                    0,
                    controller.currentIndex.value,
                  ),
                  SizedBox(width: 20.spx),
                  _buildNavItem(
                    context,
                    'assets/nav/search.svg',
                    'nav_discover'.tr,
                    1,
                    controller.currentIndex.value,
                  ),
                  SizedBox(width: 20.spx),
                  _buildNavItem(
                    context,
                    'assets/nav/Feeds.svg',
                    'nav_feeds'.tr,
                    2,
                    controller.currentIndex.value,
                  ),
                  SizedBox(width: 20.spx),
                  _buildNavItem(
                    context,
                    'assets/nav/Setting.svg',
                    'nav_settings'.tr,
                    3,
                    controller.currentIndex.value,
                  ),
                ],
              )),
        )
            .animate()
            .fade(
              duration: 600.ms,
              curve: Curves.easeOutQuad,
              begin: 0.0,
              end: 1.0,
            )
            .slideY(
              duration: 600.ms,
              curve: Curves.easeOutQuad,
              begin: 0.2,
              end: 0.0,
            ),
      ),
    );
  }

// 构建导航项
  Widget _buildNavItem(BuildContext context, String iconPath, String label,
      int index, int currentIndex) {
    final bool isSelected = currentIndex == index;
    final Color activeColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFFdcdcdc)
        : const Color(0xFF93a2fe);
    final Color inactiveColor = Theme.of(context).brightness == Brightness.dark
        ? Colors.white
        : const Color(0xFFb7c0ce);
    final Color boxColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xff818181)
        : Colors.white;
    // 创建一个ValueNotifier来控制点击动画
    final tapAnimationController = ValueNotifier<bool>(false);
    return StatefulBuilder(
      builder: (context, setState) {
        return GestureDetector(
          onTap: () {
            // 触发点击动画
            tapAnimationController.value = true;
            // 延迟执行导航操作，等待动画完成
            final controller = Get.find<HomeController>();
            controller.changePage(index);
            // 重置动画状态
            tapAnimationController.value = false;
          },
          child: ValueListenableBuilder<bool>(
            valueListenable: tapAnimationController,
            builder: (context, isTapped, child) {
              return Container(
                padding: EdgeInsets.all(10.spx),
                width: 51.spx,
                height: 51.spx,
                decoration: BoxDecoration(
                  color: boxColor,
                  borderRadius: BorderRadius.circular(51.spx),
                ),
                child: SvgPicture.asset(
                  isSelected
                      ? 'assets/nav/active-${iconPath.split('/').last.toLowerCase()}'
                      : iconPath,
                  colorFilter: ColorFilter.mode(
                    isSelected ? activeColor : inactiveColor,
                    BlendMode.srcIn,
                  ),
                ),
              )
                  .animate(target: isTapped ? 1 : 0)
                  .scale(
                    duration: 200.ms,
                    curve: Curves.easeInOut,
                    begin: const Offset(1.0, 1.0),
                    end: const Offset(0.9, 0.9),
                  )
                  .then()
                  .scale(
                    duration: 200.ms,
                    curve: Curves.bounceOut,
                    begin: const Offset(0.9, 0.9),
                    end: const Offset(1.0, 1.0),
                  )
                  // 为选中项添加额外的动画效果
                  .animate(target: isSelected ? 1 : 0, autoPlay: false)
                  .scale(
                    duration: 300.ms,
                    curve: Curves.easeOutBack,
                    begin: const Offset(0.8, 0.8),
                    end: const Offset(1.0, 1.0),
                  );
            },
          ),
        );
      },
    );
  }
}
