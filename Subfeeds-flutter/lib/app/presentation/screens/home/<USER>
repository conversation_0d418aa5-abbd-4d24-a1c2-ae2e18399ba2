import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/data/repositories/rss_repository.dart';
import 'package:subfeeds/app/data/repositories/user_repository.dart';
import 'package:subfeeds/app/data/services/http_service.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:subfeeds/app/data/repositories/article_repository.dart'
    as article_repo;
import 'package:subfeeds/app/controllers/theme_controller.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/data/repositories/feeds_repository.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/presentation/screens/article/article_controller.dart';
import 'package:subfeeds/app/data/models/user_model.dart';

import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';

/// 首页控制器
class HomeController extends GetxController {
  final RssRepository _rssRepository = RssRepository();
  final HttpService _httpService = HttpService();
  final RxBool isDarkMode = false.obs;
  final article_repo.ArticleRepository _articleRepository =
      article_repo.ArticleRepository();
  // 推荐源相关
  final ArticleRepository _feedsRepository = ArticleRepository();
  final RxList<Map<String, dynamic>> recommendFeeds =
      <Map<String, dynamic>>[].obs;
  final RxMap<String, bool> subscribedFeeds = <String, bool>{}.obs;
  final UserRepository _userRepository = UserRepository();
  // 添加UserController实例
  late UserController userController;

  // 添加本地用户信息变量
  final Rx<UserModel?> localUser = Rx<UserModel?>(null);

  // 当前选中的tab索引
  final RxInt currentIndex = 0.obs;

  // Scaffold的全局键，用于控制抽屉
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  // 文章列表
  final RxList<Map<String, dynamic>> articles = <Map<String, dynamic>>[].obs;

  // 搜索相关
  final RxString searchText = ''.obs;
  final RxList<Map<String, dynamic>> filteredArticles =
      <Map<String, dynamic>>[].obs;
  final RxBool isSearching = false.obs;

  // 加载状态
  final RxBool isLoading = false.obs;
  final RxBool isRecommendFeedsLoading = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool isRefreshing = false.obs;

  // 分页信息
  int _page = 1;
  final int _pageSize = 20;
  final RxBool hasMoreData = true.obs;

  // 轮播图文章列表
  final RxList<Map<String, dynamic>> slideArticles =
      <Map<String, dynamic>>[].obs;

  // 搜索历史记录
  final RxList<String> searchHistory = <String>[].obs;
  static const String searchHistoryKey = 'search_history';
  static const int maxHistoryItems = 10;

  // 添加ScrollController和滚动状态变量
  final ScrollController scrollController = ScrollController();
  final isScrolled = false.obs;

  // 文章状态变更流的订阅
  StreamSubscription? _articleStatusSubscription;
  //newsfeeds未读数量
  final RxInt newsCount = 0.obs;
  @override
  void onInit() {
    super.onInit();
    // 初始化UserController
    userController = Get.find<UserController>();

    // 加载本地用户信息
    loadLocalUserInfo();

    // 监听用户信息变化
    ever(userController.user, (user) {
      if (user == null) {
        // 用户已注销
        clearLocalUserInfo();
      } else {
        // 用户已登录或更新信息
        loadLocalUserInfo();
      }
    });

    loadSearchHistory();

    // 监听搜索文本变化，自动更新过滤后的文章列表
    ever(searchText, (_) {
      _filterArticles();
    });

    // 监听文章列表变化，确保过滤列表同步更新
    ever(articles, (_) {
      _filterArticles();
    });

    // 加载初始数据
    loadInitialData();

    // 监听滚动事件
    scrollController.addListener(_scrollListener);

    // 监听文章状态变更事件
    _articleStatusSubscription = ArticleController.articleStatusChanged.stream
        .listen(_handleArticleStatusChanged);
  }

  @override
  void onClose() {
    // 移除滚动监听器
    scrollController.removeListener(_scrollListener);
    scrollController.dispose();
    // 取消文章状态变更监听
    _articleStatusSubscription?.cancel();
    super.onClose();
  }

  /// 过滤文章列表
  void _filterArticles() {
    if (searchText.value.isEmpty) {
      filteredArticles.assignAll(articles);
      return;
    }

    final query = searchText.value.toLowerCase();
    final filtered = articles.where((article) {
      final title = article['title']?.toString().toLowerCase() ?? '';
      final description =
          article['description']?.toString().toLowerCase() ?? '';
      final source = article['source']?.toString().toLowerCase() ?? '';

      return title.contains(query) ||
          description.contains(query) ||
          source.contains(query);
    }).toList();

    filteredArticles.assignAll(filtered);
  }

  /// 设置搜索文本
  void setSearchText(String text) {
    searchText.value = text;
  }

  /// 切换搜索状态
  void toggleSearch() {
    isSearching.value = !isSearching.value;
    if (!isSearching.value) {
      searchText.value = '';
    }
  }

  /// 切换页面
  void changePage(int index) {
    currentIndex.value = index;
  }

  /// 加载初始数据
  Future<void> loadInitialData() async {
    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';

    try {
      // 加载文章列表
      await _loadArticles();
      // 加载newfeeds
      await _loadNewsFeeds();

      await _loadSlideArticles();
      debugPrint('用户: ${userController.user.value}');
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载轮播图数据
  Future<void> _loadSlideArticles() async {
    try {
      debugPrint('开始加载轮播图数据');
      final response = await _httpService.get(
        '/api/v1/slideArticle',
        queryParameters: {
          'pageNum': '1',
          'pageSize': '5',
        },
      );

      debugPrint('轮播图响应数据: ${response.data}');

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        slideArticles.value =
            List<Map<String, dynamic>>.from(responseData['pageList'] ?? []);
      } else {
        debugPrint('轮播图数据加载失败: ${response.msg}');
      }
    } catch (e) {
      debugPrint('加载轮播图数据失败: $e');
    }
  }

  /// 加载文章列表
  Future<void> _loadArticles() async {
    try {
      debugPrint('开始加载文章列表，页码：$_page');
      final response = await _httpService.get(
        '/api/v1/home',
        queryParameters: {
          'pageNum': _page.toString(),
          'pageSize': _pageSize.toString(),
        },
      );

      debugPrint('文章列表响应数据: ${response.data}');

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final List<Map<String, dynamic>> newArticles =
            List<Map<String, dynamic>>.from(responseData['pageList'] ?? []);

        if (_page == 1) {
          articles.value = newArticles;
        } else {
          articles.addAll(newArticles);
        }

        hasMoreData.value = newArticles.length >= _pageSize;
        _page++;
        _filterArticles();
        debugPrint('文章列表加载成功: ${newArticles.length}条');
      } else {
        debugPrint('文章列表加载失败: ${response.msg}');
        throw Exception(response.msg);
      }
    } catch (e) {
      debugPrint('加载文章列表失败: $e');
      throw e;
    }
  }

  Future<void> _loadNewsFeeds() async {
    try {
      final response = await _articleRepository.getRssNewArticle(1, 1, 2);

      if (response.isSuccess && response.data != null) {
        final responseData = response.data;
        final data = responseData?['data'];
        newsCount.value = data?['other'] as int? ?? 0;
      }
    } catch (e) {}
  }

  /// 加载更多数据
  Future<void> loadMoreData() async {
    if (!hasMoreData.value || isLoading.value) return;

    isLoading.value = true;
    try {
      await _loadArticles();
    } catch (e) {
      debugPrint('加载更多数据失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 刷新数据
  Future<void> refreshData() async {
    _page = 1;
    isRefreshing.value = true;
    try {
      // 刷新用户信息
      await loadLocalUserInfo();

      await loadInitialData();
    } finally {
      isRefreshing.value = false;
    }
  }

  /// 刷新所有数据（轮播图、文章和推荐订阅源）
  Future<void> refreshAllData() async {
    _page = 1;
    isRefreshing.value = true;
    hasError.value = false;
    errorMessage.value = '';

    try {
      // 并行加载所有数据
      await Future.wait([
        _loadSlideArticles(),
        _loadArticles(),
        _loadRecommendFeeds(),
      ]);
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
    } finally {
      isRefreshing.value = false;
    }
  }

  /// 切换文章收藏状态
  Future<void> toggleBookmark(int index) async {
    if (index < 0 || index >= articles.length) return;

    try {
      final article = articles[index];
      final bool isCurrentlyCollect = article['isCollect'] == 1;
      final int articleId = int.parse(article['id'].toString());

      // 先更新UI状态
      article['isCollect'] = isCurrentlyCollect ? 0 : 1;
      articles[index] = article;
      _filterArticles();

      // 发送API请求
      final response = isCurrentlyCollect
          ? await _articleRepository.deleteCollect([articleId])
          : await _articleRepository.insertCollect(
              articleId,
              int.tryParse(article['feedsId'].toString()) ?? -1,
              article['suffixTable'] ?? '',
            );

      if (!response.isSuccess) {
        // 如果请求失败，回退状态
        article['isCollect'] = isCurrentlyCollect ? 1 : 0;
        articles[index] = article;
        _filterArticles();

        Get.snackbar(
          'error'.tr,
          response.msg,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // 发生错误，回退状态
      final article = articles[index];
      final bool isCurrentlyCollect = article['isCollect'] == 1;
      article['isCollect'] = isCurrentlyCollect ? 0 : 1;
      articles[index] = article;
      _filterArticles();

      debugPrint('切换收藏状态失败: $e');
      Get.snackbar(
        'error'.tr,
        'article_collect_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 切换稍后阅读状态
  Future<void> toggleReadLater(int index) async {
    if (index < 0 || index >= articles.length) return;

    try {
      final article = articles[index];
      final bool isCurrentlyReadLater = article['isLaterRead'] == 1;
      final int articleId = int.parse(article['id'].toString());

      // 先更新UI状态
      article['isLaterRead'] = isCurrentlyReadLater ? 0 : 1;
      articles[index] = article;
      _filterArticles();

      // 发送API请求
      final response = isCurrentlyReadLater
          ? await _articleRepository.deleteLaterRead([articleId])
          : await _articleRepository.setReadLater(
              articleId,
              int.tryParse(article['feedsId'].toString()) ?? -1,
              article['suffixTable'] ?? '',
            );

      if (!response.isSuccess) {
        // 如果请求失败，回退状态
        article['isLaterRead'] = isCurrentlyReadLater ? 1 : 0;
        articles[index] = article;
        _filterArticles();

        Get.snackbar(
          'error'.tr,
          response.msg,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // 发生错误，回退状态
      final article = articles[index];
      final bool isCurrentlyReadLater = article['isLaterRead'] == 1;
      article['isLaterRead'] = isCurrentlyReadLater ? 0 : 1;
      articles[index] = article;
      _filterArticles();

      debugPrint('切换稍后阅读状态失败: $e');
      Get.snackbar(
        'error'.tr,
        'article_readlater_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  // 设置已读
  Future<void> setRead(int index, String feedsId, String suffixTable) async {
    if (index < 0 || index >= articles.length) return;

    try {
      final article = articles[index];
      final int articleId = int.parse(article['id'].toString());

      final response = await _articleRepository.insertHistory(
        articleId,
        int.tryParse(feedsId) ?? -1,
        suffixTable,
      );

      if (response.isSuccess) {
        // 更新状态
        article['isRead'] = 1;
        articles[index] = article;
        _filterArticles();
      } else {
        debugPrint('设置已读失败: ${response.msg}');
      }
    } catch (e) {
      debugPrint('设置已读失败: $e');
    }
  }

  // 获取当前主题
  Future<bool> getIsDarkMode() async {
    try {
      final themeController = Get.find<ThemeController>();
      isDarkMode.value = themeController.isDarkMode.value;
      return isDarkMode.value;
    } catch (e) {
      // 如果找不到 ThemeController，则使用 SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      isDarkMode.value = prefs.getBool('isDarkMode') ?? false;
      return isDarkMode.value;
    }
  }

  /// 格式化时间
  String formatTime(dynamic time) {
    try {
      if (time == null) return '';

      DateTime dateTime;
      if (time is String) {
        // 处理pubDate格式
        dateTime = DateTime.parse(time);
      } else if (time is int) {
        // 处理createTime时间戳
        dateTime = DateTime.fromMillisecondsSinceEpoch(time * 1000);
      } else {
        return '';
      }

      final now = DateTime.now();
      final difference = now.difference(dateTime);
      if (difference.inHours < 3) {
        return 'just_now'.tr;
      }
      // 1天以内，返回小时
      if (difference.inDays < 1) {
        final hours = difference.inHours;
        return '${hours}H';
      }
      // 30天以内，返回天数
      else if (difference.inDays < 30) {
        return '${difference.inDays}D';
      }
      // 超过30天，返回完整日期
      else {
        return DateFormat('yyyy-MM-dd').format(dateTime);
      }
    } catch (e) {
      debugPrint('时间格式化失败: $e');
      return '';
    }
  }

  /// 加载搜索历史记录
  Future<void> loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = prefs.getStringList(searchHistoryKey) ?? [];
      searchHistory.value = history;
    } catch (e) {
      debugPrint('加载搜索历史记录失败: $e');
    }
  }

  /// 添加搜索历史记录
  Future<void> addSearchHistory(String query) async {
    if (query.isEmpty) return;

    try {
      // 移除重复项
      searchHistory.remove(query);

      // 添加到开头
      searchHistory.insert(0, query);

      // 如果超过最大数量，删除最后一项
      if (searchHistory.length > maxHistoryItems) {
        searchHistory.removeLast();
      }

      // 保存到本地
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(searchHistoryKey, searchHistory.toList());
    } catch (e) {
      debugPrint('保存搜索历史记录失败: $e');
    }
  }

  /// 清除搜索历史记录
  Future<void> clearSearchHistory() async {
    try {
      searchHistory.clear();
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(searchHistoryKey);
    } catch (e) {
      debugPrint('清除搜索历史记录失败: $e');
    }
  }

  /// 删除单个搜索历史记录
  Future<void> removeSearchHistory(String query) async {
    try {
      searchHistory.remove(query);
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(searchHistoryKey, searchHistory.toList());
    } catch (e) {
      debugPrint('删除搜索历史记录失败: $e');
    }
  }

  // 滚动监听函数
  void _scrollListener() {
    // 当滚动位置超过100时，isScrolled为true
    if (scrollController.offset > 100 && !isScrolled.value) {
      isScrolled.value = true;
    } else if (scrollController.offset <= 100 && isScrolled.value) {
      isScrolled.value = false;
    }
  }

  // 回到顶部方法
  void scrollToTop() {
    scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  /// 加载推荐订阅源
  Future<void> _loadRecommendFeeds() async {
    try {
      final recommendResponse = await _feedsRepository.getRecommendFeeds();
      if (recommendResponse.isSuccess && recommendResponse.data != null) {
        final data = recommendResponse.data;
        final List<dynamic> pageList = data?['pageList'] as List<dynamic>;
        recommendFeeds.value =
            pageList.map((item) => item as Map<String, dynamic>).toList();
        debugPrint('推荐订阅源加载成功: ${recommendFeeds.length}条');
      } else {
        debugPrint('推荐订阅源加载失败: ${recommendResponse.msg}');
      }
    } catch (e) {
      debugPrint('加载推荐订阅源失败: $e');
    }
  }

  // 刷新推荐订阅源
  Future<void> refreshRecommendFeeds() async {
    try {
      isRecommendFeedsLoading.value = true;
      recommendFeeds.clear();
      await _loadRecommendFeeds();
    } catch (e) {
      debugPrint('刷新推荐订阅源失败: $e');
      Get.snackbar(
        'error'.tr,
        'load_recommend_feeds_failed'.tr,
        duration: const Duration(seconds: 2),
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    } finally {
      isRecommendFeedsLoading.value = false;
    }
  }

  /// 订阅源
  Future<bool> subscribeFeed(Map<String, dynamic> feed) async {
    try {
      final String feedId = feed['id']?.toString() ?? '';
      if (feedId.isEmpty) {
        Get.closeAllSnackbars();
        Get.snackbar(
          'error'.tr,
          'invalid_feed_id'.tr,
          duration: const Duration(seconds: 2),
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return false;
      }

      // 如果已经订阅，则不再重复订阅
      if (subscribedFeeds[feedId] == true) {
        Get.closeAllSnackbars();
        Get.snackbar(
          'notice'.tr,
          'already_subscribed'.tr,
          duration: const Duration(seconds: 2),
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      }

      // 构建请求参数
      final params = {
        "searchValue": feedId,
        "type": feed['type'] ?? 1,
      };

      // 调用订阅API
      final response = await _feedsRepository.subscribeRss(params);

      if (response.isSuccess) {
        // 更新订阅状态
        subscribedFeeds[feedId] = true;
        Get.closeAllSnackbars();
        Get.snackbar(
          'success'.tr,
          'subscribe_success'.tr,
          duration: const Duration(seconds: 2),
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      } else {
        Get.closeAllSnackbars();
        Get.snackbar(
          'error'.tr,
          response.msg,
          duration: const Duration(seconds: 2),
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return false;
      }
    } catch (e) {
      Get.closeAllSnackbars();
      Get.snackbar(
        'error'.tr,
        e.toString(),
        duration: const Duration(seconds: 2),
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      return false;
    }
  }

  /// 取消订阅源
  Future<bool> unsubscribeFeed(Map<String, dynamic> feed) async {
    try {
      final String feedId = feed['id']?.toString() ?? '';
      if (feedId.isEmpty) {
        Get.closeAllSnackbars();
        Get.snackbar(
          'error'.tr,
          'invalid_feed_id'.tr,
          duration: const Duration(seconds: 2),
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return false;
      }
      // 如果未订阅，则不需要取消订阅
      if (subscribedFeeds[feedId] != true) {
        Get.closeAllSnackbars();
        Get.snackbar(
          'notice'.tr,
          'not_subscribed'.tr,
          duration: const Duration(seconds: 2),
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      }

      // 调用取消订阅API
      final response = await _feedsRepository.deleteRss([int.parse(feedId)]);

      if (response.isSuccess) {
        // 更新订阅状态
        subscribedFeeds[feedId] = false;
        Get.closeAllSnackbars();
        Get.snackbar(
          'success'.tr,
          'unsubscribe_success'.tr,
          duration: const Duration(seconds: 2),
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      } else {
        Get.closeAllSnackbars();
        Get.snackbar(
          'error'.tr,
          response.msg,
          duration: const Duration(seconds: 2),
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return false;
      }
    } catch (e) {
      Get.closeAllSnackbars();
      Get.snackbar(
        'error'.tr,
        e.toString(),
        duration: const Duration(seconds: 2),
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      return false;
    }
  }

  // 处理文章状态变更事件
  void _handleArticleStatusChanged(Map<String, dynamic> data) {
    if (data.containsKey('id')) {
      final String articleId = data['id'];

      // 更新文章列表中的状态
      final index = articles
          .indexWhere((article) => article['id'].toString() == articleId);

      if (index != -1) {
        // 创建文章的副本
        final updatedArticle = Map<String, dynamic>.from(articles[index]);

        // 更新收藏状态
        if (data.containsKey('isCollect')) {
          updatedArticle['isCollect'] = data['isCollect'];
        }

        // 更新稍后阅读状态
        if (data.containsKey('isLaterRead')) {
          updatedArticle['isLaterRead'] = data['isLaterRead'];
        }

        // 更新文章列表
        articles[index] = updatedArticle;

        // 刷新过滤后的文章列表
        filteredArticles.refresh();
      }

      // 更新轮播图文章列表中的状态
      final slideIndex = slideArticles
          .indexWhere((article) => article['id'].toString() == articleId);

      if (slideIndex != -1) {
        // 创建文章的副本
        final updatedSlideArticle =
            Map<String, dynamic>.from(slideArticles[slideIndex]);

        // 更新收藏状态
        if (data.containsKey('isCollect')) {
          updatedSlideArticle['isCollect'] = data['isCollect'];
        }

        // 更新稍后阅读状态
        if (data.containsKey('isLaterRead')) {
          updatedSlideArticle['isLaterRead'] = data['isLaterRead'];
        }

        // 更新轮播图文章列表
        slideArticles[slideIndex] = updatedSlideArticle;

        // 刷新轮播图文章列表
        slideArticles.refresh();
      }
    }
  }

  /// 打开抽屉菜单
  void openDrawer(int index) {
    if (scaffoldKey.currentState?.isDrawerOpen == false) {
      scaffoldKey.currentState?.openDrawer();
      //激活抽屉菜单
      currentIndex.value = index;
      // 刷新未读数量
      _loadNewsFeeds();

      // 刷新feeds列表
      final feedsController = Get.isRegistered<FeedsController>()
          ? Get.find<FeedsController>()
          : null;
      if (feedsController != null) {
        feedsController.refreshFeeds();
      }
    }
  }

  /// 关闭抽屉菜单
  void closeDrawer() {
    if (scaffoldKey.currentState?.isDrawerOpen == true) {
      scaffoldKey.currentState?.closeDrawer();
    }
  }

  /// 从本地存储加载用户信息
  Future<void> loadLocalUserInfo() async {
    try {
      final userData = await _userRepository.getUserInfoFromLocal();
      localUser.value = userData;
      debugPrint('从本地加载用户信息成功: ${userData?.name}');
    } catch (e) {
      debugPrint('从本地加载用户信息失败: $e');
    }
  }

  /// 清除本地用户信息
  void clearLocalUserInfo() {
    localUser.value = null;
  }
}
