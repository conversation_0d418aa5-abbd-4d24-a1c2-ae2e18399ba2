import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>';
import 'package:subfeeds/app/presentation/screens/home/<USER>/discover_tab.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_tab.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_tab.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/settings_tab.dart';

/// 首页（主页面）
class HomeScreen extends GetView<HomeController> {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 检测当前设备是否为桌面或平板
    final bool isLargeScreen = MediaQuery.of(context).size.width > 600;
    final bool isDesktop = MediaQuery.of(context).size.width > 1024;

    return Obx(() {
      if (isDesktop) {
        // 桌面布局 - 左侧导航栏
        return Scaffold(
          body: Row(
            children: [
              _buildNavigationRail(context),
              const VerticalDivider(width: 1, thickness: 1),
              Expanded(child: _buildCurrentPage()),
            ],
          ),
        );
      } else if (isLargeScreen &&
          MediaQuery.of(context).orientation == Orientation.landscape) {
        // 平板横屏布局 - 左侧导航栏
        return Scaffold(
          body: Row(
            children: [
              _buildNavigationRail(context),
              const VerticalDivider(width: 1, thickness: 1),
              Expanded(child: _buildCurrentPage()),
            ],
          ),
        );
      } else {
        // 手机和平板竖屏布局 - 使用NavigationDrawer（抽屉菜单）
        return Scaffold(
          appBar: AppBar(
            title: _getAppBarTitle(),
          ),
          drawer: _buildNavigationDrawer(context),
          body: _buildCurrentPage(),
        );
      }
    });
  }

  /// 获取AppBar标题
  Widget _getAppBarTitle() {
    switch (controller.currentIndex.value) {
      case 0:
        return Text('nav_home'.tr);
      case 1:
        return Text('nav_discover'.tr);
      case 2:
        return Text('nav_feeds'.tr);
      case 3:
        return Text('nav_settings'.tr);
      default:
        return Text('nav_home'.tr);
    }
  }

  /// 构建当前页面
  Widget _buildCurrentPage() {
    switch (controller.currentIndex.value) {
      case 0:
        return const HomeTab();
      case 1:
        return const DiscoverTab();
      case 2:
        return const FeedsTab();
      case 3:
        return const SettingsTab();
      default:
        return const HomeTab();
    }
  }

  /// 构建导航抽屉
  Widget _buildNavigationDrawer(BuildContext context) {
    return NavigationDrawer(
      onDestinationSelected: (index) {
        controller.changePage(index);
        Navigator.pop(context); // 选择后关闭抽屉
      },
      selectedIndex: controller.currentIndex.value,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 28, 16, 16),
          child: Text(
            'SubFeeds',
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ),
        const Divider(),
        NavigationDrawerDestination(
          icon: const Icon(Icons.home_outlined),
          selectedIcon: const Icon(Icons.home),
          label: Text('nav_home'.tr),
        ),
        NavigationDrawerDestination(
          icon: const Icon(Icons.search_outlined),
          selectedIcon: const Icon(Icons.search),
          label: Text('nav_discover'.tr),
        ),
        NavigationDrawerDestination(
          icon: const Icon(Icons.rss_feed_outlined),
          selectedIcon: const Icon(Icons.rss_feed),
          label: Text('nav_feeds'.tr),
        ),
        NavigationDrawerDestination(
          icon: const Icon(Icons.settings_outlined),
          selectedIcon: const Icon(Icons.settings),
          label: Text('nav_settings'.tr),
        ),
      ],
    );
  }

  /// 构建导航栏（桌面端和平板横屏）
  Widget _buildNavigationRail(BuildContext context) {
    return NavigationRail(
      selectedIndex: controller.currentIndex.value,
      onDestinationSelected: controller.changePage,
      labelType: NavigationRailLabelType.all,
      destinations: [
        NavigationRailDestination(
          icon: const Icon(Icons.home_outlined),
          selectedIcon: const Icon(Icons.home),
          label: Text('nav_home'.tr),
        ),
        NavigationRailDestination(
          icon: const Icon(Icons.search_outlined),
          selectedIcon: const Icon(Icons.search),
          label: Text('nav_discover'.tr),
        ),
        NavigationRailDestination(
          icon: const Icon(Icons.rss_feed_outlined),
          selectedIcon: const Icon(Icons.rss_feed),
          label: Text('nav_feeds'.tr),
        ),
        NavigationRailDestination(
          icon: const Icon(Icons.settings_outlined),
          selectedIcon: const Icon(Icons.settings),
          label: Text('nav_settings'.tr),
        ),
      ],
    );
  }
}
