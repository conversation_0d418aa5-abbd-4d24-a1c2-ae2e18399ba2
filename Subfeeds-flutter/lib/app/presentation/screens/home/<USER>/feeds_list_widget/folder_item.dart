import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/feed_item.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';

/// 文件夹项组件，显示单个文件夹及其内容
class FolderItem extends StatelessWidget {
  /// 文件夹数据
  final Map<String, dynamic> folder;

  /// 控制器
  final FeedsController controller;

  /// 显示文件夹选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowFolderOptions;

  /// 显示Feed选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowFeedOptions;

  const FolderItem({
    Key? key,
    required this.folder,
    required this.controller,
    required this.onShowFolderOptions,
    required this.onShowFeedOptions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isExpanded =
          controller.expandedFolders.contains(folder['id'].toString());
      final feedsList = folder['userFeedsPos'] as List? ?? [];
      // 使用folderLoadingStatus来判断是否正在加载，而不是仅仅根据列表是否为空
      final bool isLoading =
          controller.folderLoadingStatus.value[folder['id'].toString()] ??
              false;

      return DragTarget<Map<String, dynamic>>(
        onWillAccept: (data) {
          if (data == null) return false;
          final dragFeed = data['feed'] as Map<String, dynamic>;
          final sourceFolderId = data['folderId'] as String?;
          return controller.canAcceptDrag(
              folder['id'].toString(), sourceFolderId);
        },
        onAccept: (data) async {
          final dragFeed = data['feed'] as Map<String, dynamic>;
          final sourceFolderId = data['folderId'] as String?;
          final feedId = int.tryParse(dragFeed['id'].toString()) ?? -1;

          if (feedId != -1) {
            await controller.moveFeedToFolder(
              feedId,
              sourceFolderId,
              folder['id'].toString(),
            );
          }
        },
        builder: (context, candidateData, rejectedData) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 文件夹头部
              _buildFolderHeader(context, isExpanded, candidateData.isNotEmpty),

              // 展开时显示文件夹内的订阅源
              if (isExpanded)
                _buildFolderContent(context, isLoading, feedsList),
            ],
          );
        },
      );
    });
  }

  /// 构建文件夹头部
  Widget _buildFolderHeader(
      BuildContext context, bool isExpanded, bool isDragTarget) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => controller.toggleFolder(folder['id'].toString()),
      child: Container(
        decoration: BoxDecoration(
          color: isDragTarget
              ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
              : null,
          borderRadius: BorderRadius.circular(8),
        ),
        padding:
            const EdgeInsets.only(left: 12, right: 16, top: 10, bottom: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 展开/折叠图标
            GestureDetector(
              onTap: () => controller.toggleFolder(folder['id'].toString()),
              child: Icon(
                isExpanded ? Icons.expand_more_outlined : Icons.chevron_right,
                size: 23,
              ),
            ),
            const SizedBox(width: 3),

            // 文件夹名称
            Expanded(
              child: Text(
                folder['groupName'] ?? '',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
              ),
            ),

            const SizedBox(width: 2),

            // 更多选项按钮
            GestureDetector(
              onTap: () => onShowFolderOptions(context, folder),
              child: Container(
                padding:
                    const EdgeInsets.only(top: 4, left: 4, bottom: 4, right: 0),
                child: Icon(
                  Icons.more_vert,
                  size: 20,
                  color: Color(0XFF7F8EA7),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建文件夹内容
  Widget _buildFolderContent(
      BuildContext context, bool isLoading, List feedsList) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: isLoading ? 100 : feedsList.length * 50.0, // 加载时高度较小
      ),
      child: Container(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF3b3b3b)
            : const Color(0xFFffffff),
        child: isLoading
            ? Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: LoadingIndicator(),
                ),
              )
            : ListView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: feedsList.length,
                itemBuilder: (context, index) {
                  final feed = feedsList[index];
                  return Padding(
                    padding: const EdgeInsets.only(left: 23),
                    child: FeedItem(
                      feed: feed,
                      folderId: folder['id'].toString(),
                      onShowOptions: onShowFeedOptions,
                    ),
                  );
                },
              ),
      ),
    );
  }
}
