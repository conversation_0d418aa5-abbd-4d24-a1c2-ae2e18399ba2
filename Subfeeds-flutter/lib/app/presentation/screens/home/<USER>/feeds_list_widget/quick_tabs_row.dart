import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/quick_tab_item.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

/// 快捷标签行组件
class QuickTabsRow extends StatelessWidget {
  /// 用户计数数据
  final Map<String, dynamic> userCounts;

  const QuickTabsRow({
    Key? key,
    required this.userCounts,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.symmetric(vertical: 5.spx, horizontal: 9.spx),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.spx),
        ),
        child: Column(
          children: [
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.spx),
              ),
              elevation: 0,
              margin: EdgeInsets.zero,
              child: Column(
                children: [
                  QuickTabItem(
                    iconPath: 'assets/icons/to_new.svg',
                    label: 'add_news'.tr,
                    onTap: () {
                      Navigator.pushNamed(context, Routes.SPECIAL_NEWS_DETAIL,
                          arguments: {
                            'type': 5,
                            'source': {
                              'name': 'YouTube',
                            },
                          });
                    },
                  ),
                  QuickTabItem(
                    iconPath: 'assets/icons/to_feeds.svg',
                    label: 'add_feeds'.tr,
                    onTap: () {
                      Navigator.pushNamed(context, Routes.ADD_FEEDS);
                    },
                  ),
                ],
              ),
            ),
            SizedBox(height: 8.spx),
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.spx),
              ),
              elevation: 0,
              margin: EdgeInsets.zero,
              child: Column(
                children: [
                  QuickTabItem(
                    iconPath: 'assets/feeds/menu_star.svg',
                    label: 'feeds_starred'.tr,
                    count: Text(
                      '${userCounts['collectCount'] ?? 0}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 10.spx,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF999999),
                          ),
                    ),
                  ),
                  QuickTabItem(
                    iconPath: 'assets/feeds/menu_readlater.svg',
                    label: 'feeds_read_later'.tr,
                    count: Text(
                      '${userCounts['laterReadCount'] ?? 0}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 10.spx,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF999999),
                          ),
                    ),
                  ),
                  QuickTabItem(
                    iconPath: 'assets/feeds/menu_note.svg',
                    label: 'feeds_annotated'.tr,
                    count: Text(
                      '${userCounts['noteCount'] ?? 0}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 10.spx,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF999999),
                          ),
                    ),
                  ),
                  QuickTabItem(
                    iconPath: 'assets/feeds/menu_history.svg',
                    label: 'feeds_history'.tr,
                    count: Text(
                      '${userCounts['readerCount'] ?? 100}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 10.spx,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF999999),
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }
}
