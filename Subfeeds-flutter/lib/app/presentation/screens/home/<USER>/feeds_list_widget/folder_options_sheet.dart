import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 文件夹选项底部弹窗组件
class FolderOptionsSheet extends StatelessWidget {
  /// 文件夹数据
  final Map<String, dynamic> folder;

  /// 重命名回调
  final Function(Map<String, dynamic>) onRename;

  /// 删除回调
  final Function(Map<String, dynamic>) onDelete;

  const FolderOptionsSheet({
    Key? key,
    required this.folder,
    required this.onRename,
    required this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.spx, horizontal: 20.spx),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFFF7FAFF)
            : const Color(0xFF444444),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            contentPadding: EdgeInsets.zero,
            // leading: const Icon(Icons.edit),
            dense: true,
            title: Text('rename'.tr,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14.spx,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Color(0xFF333333),
                    )),
            onTap: () {
              Get.back();
              onRename(folder);
            },
          ),
          ListTile(
            contentPadding: EdgeInsets.zero,
            dense: true,
            title: Text('delete'.tr,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14.spx,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Color(0xFF333333),
                    )),
            onTap: () {
              Get.back();
              onDelete(folder);
            },
          ),
        ],
      ),
    );
  }

  /// 显示文件夹选项底部弹窗
  static void show(
    BuildContext context,
    Map<String, dynamic> folder, {
    required Function(Map<String, dynamic>) onRename,
    required Function(Map<String, dynamic>) onDelete,
  }) {
    Get.bottomSheet(
      FolderOptionsSheet(
        folder: folder,
        onRename: onRename,
        onDelete: onDelete,
      ),
      backgroundColor: Colors.transparent,
    );
  }
}
