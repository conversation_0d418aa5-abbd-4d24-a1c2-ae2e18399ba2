import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// Feed选项底部弹窗组件
class FeedOptionsSheet extends StatelessWidget {
  /// Feed数据
  final Map<String, dynamic> feed;

  /// 重命名回调
  final Function(Map<String, dynamic>) onRename;

  /// 查看属性回调
  final Function(Map<String, dynamic>) onViewProperties;

  /// 标记全部已读回调
  final Function(Map<String, dynamic>) onMarkAllAsRead;

  /// 取消订阅回调
  final Function(Map<String, dynamic>) onUnfollow;

  const FeedOptionsSheet({
    Key? key,
    required this.feed,
    required this.onRename,
    required this.onViewProperties,
    required this.onMarkAllAsRead,
    required this.onUnfollow,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFFF7FAFF)
            : const Color(0xFF444444),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            // leading: const Icon(Icons.edit),
            title: Text('rename'.tr,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14.spx,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Color(0xFF333333),
                    )),
            onTap: () {
              Get.back();
              onRename(feed);
            },
          ),
          ListTile(
            // leading: const Icon(Icons.info_outline),
            title: Text('properties'.tr,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14.spx,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Color(0xFF333333),
                    )),
            onTap: () {
              Get.back();
              onViewProperties(feed);
            },
          ),
          ListTile(
            // leading: const Icon(Icons.mark_email_read),
            title: Text('mark_all_as_read'.tr,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14.spx,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Color(0xFF333333),
                    )),
            onTap: () {
              Get.back();
              onMarkAllAsRead(feed);
            },
          ),
          ListTile(
            // leading: const Icon(Icons.unsubscribe),
            title: Text('unfollow'.tr,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14.spx,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Color(0xFF333333),
                    )),
            onTap: () {
              Get.back();
              onUnfollow(feed);
            },
          ),
        ],
      ),
    );
  }

  /// 显示Feed选项底部弹窗
  static void show(
    BuildContext context,
    Map<String, dynamic> feed, {
    required Function(Map<String, dynamic>) onRename,
    required Function(Map<String, dynamic>) onViewProperties,
    required Function(Map<String, dynamic>) onMarkAllAsRead,
    required Function(Map<String, dynamic>) onUnfollow,
  }) {
    Get.bottomSheet(
      FeedOptionsSheet(
        feed: feed,
        onRename: onRename,
        onViewProperties: onViewProperties,
        onMarkAllAsRead: onMarkAllAsRead,
        onUnfollow: onUnfollow,
      ),
      backgroundColor: Colors.transparent,
    );
  }
}
