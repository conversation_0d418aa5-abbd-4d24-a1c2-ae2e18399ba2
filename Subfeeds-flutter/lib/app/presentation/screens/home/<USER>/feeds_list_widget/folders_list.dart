import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/folder_header.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/folder_item.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 文件夹列表组件，显示所有文件夹
class FoldersList extends StatelessWidget {
  /// 控制器
  final FeedsController controller;

  /// 显示文件夹选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowFolderOptions;

  /// 显示Feed选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowFeedOptions;

  const FoldersList({
    Key? key,
    required this.controller,
    required this.onShowFolderOptions,
    required this.onShowFeedOptions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      elevation: 0,
      margin: EdgeInsets.zero,
      child: Container(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: controller.foldersList
              .map((folder) => FolderItem(
                    folder: folder,
                    controller: controller,
                    onShowFolderOptions: onShowFolderOptions,
                    onShowFeedOptions: onShowFeedOptions,
                  ))
              .toList(),
        ),
      ),
    );
  }
}

/// 固定在顶部的文件夹列表组件
class FixedFoldersList extends StatelessWidget {
  /// 控制器
  final FeedsController controller;

  /// 显示文件夹选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowFolderOptions;

  /// 显示Feed选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowFeedOptions;

  /// 是否固定
  final bool isFixed;

  const FixedFoldersList({
    Key? key,
    required this.controller,
    required this.onShowFolderOptions,
    required this.onShowFeedOptions,
    required this.isFixed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!isFixed) return const SizedBox.shrink();

    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Material(
        elevation: 2,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF444444)
                : const Color(0xFFf7faff),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 9.spx),
                child: FolderHeader(
                  isFixed: isFixed,
                  onCreateFolder: () =>
                      controller.showCreateFolderDialog(context),
                  onToggleFixed: () => controller.toggleFolderFixed(),
                  controller: controller,
                  onShowFeedOptions: onShowFeedOptions,
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 9.spx),
                child: FoldersList(
                  controller: controller,
                  onShowFolderOptions: onShowFolderOptions,
                  onShowFeedOptions: onShowFeedOptions,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
