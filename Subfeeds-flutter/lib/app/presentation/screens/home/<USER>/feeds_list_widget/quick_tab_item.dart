import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/footpoint_screen.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 快捷标签项组件
class QuickTabItem extends StatelessWidget {
  /// 图标路径
  final String iconPath;

  /// 标签文本
  final String label;

  /// 计数组件
  final Widget count;
  final Function()? onTap;
  const QuickTabItem({
    Key? key,
    required this.iconPath,
    required this.label,
    this.onTap,
    this.count = const SizedBox.shrink(),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap ??
          () {
            FootpointType type;
            if (label == 'feeds_starred'.tr) {
              type = FootpointType.starred;
            } else if (label == 'feeds_read_later'.tr) {
              type = FootpointType.readLater;
            } else if (label == 'feeds_history'.tr) {
              type = FootpointType.history;
            } else {
              type = FootpointType.annotated;
            }
            Navigator.of(context).pushNamed(Routes.FOOTPOINT, arguments: type);
          },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.spx, vertical: 8.spx),
        child: Row(
          children: [
            Container(
              width: 20.spx,
              height: 20.spx,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: SvgPicture.asset(
                  iconPath,
                  width: 20.spx,
                  height: 20.spx,
                  colorFilter: ColorFilter.mode(
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : const Color(0xFF333333),
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
            SizedBox(width: 10.spx),
            Expanded(
              child: Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14.spx,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ),
            count,
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              size: 12.spx,
              color: Color(0XFF7F8EA7),
            ),
          ],
        ),
      ),
    );
  }
}
