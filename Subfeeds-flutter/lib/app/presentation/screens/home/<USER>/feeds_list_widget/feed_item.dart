import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

/// 自动保持活动状态的 Widget 包装器
class AutomaticKeepAliveWidget extends StatefulWidget {
  final Widget child;

  const AutomaticKeepAliveWidget({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  AutomaticKeepAliveWidgetState createState() =>
      AutomaticKeepAliveWidgetState();
}

class AutomaticKeepAliveWidgetState extends State<AutomaticKeepAliveWidget>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}

/// Feed项组件，显示单个订阅源
class FeedItem extends StatelessWidget {
  /// Feed数据
  final Map<String, dynamic> feed;

  /// 文件夹ID，如果该Feed在文件夹中
  final String? folderId;

  /// 显示选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowOptions;

  const FeedItem({
    Key? key,
    required this.feed,
    this.folderId,
    required this.onShowOptions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AutomaticKeepAliveWidget(
      child: LongPressDraggable<Map<String, dynamic>>(
        data: {
          'feed': feed,
          'folderId': folderId,
        },
        maxSimultaneousDrags: 1,
        hapticFeedbackOnStart: true,
        delay: const Duration(milliseconds: 100), // 拖拽延迟时间
        feedback: Material(
          elevation: 4.0,
          child: Container(
            width: MediaQuery.of(context).size.width - 64.spx,
            padding: EdgeInsets.symmetric(horizontal: 16.spx, vertical: 8.spx),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF3b3b3b)
                  : const Color(0xFFffffff),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                _buildFeedIcon(context),
                SizedBox(width: 10.spx),
                Expanded(
                  child: Text(
                    feed['feedsName']?.trim() ?? '',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 14.spx,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
        child: GestureDetector(
          onTap: () {
            // 跳转到该Feed的文章列表页
            Get.toNamed(
              Routes.FEEDS_ARTICLES,
              arguments: {'feed': feed},
            );
          },
          child: Container(
            color: Colors.transparent,
            padding: EdgeInsets.symmetric(horizontal: 16.spx, vertical: 8.spx),
            child: Row(
              children: [
                _buildFeedIcon(context),
                SizedBox(width: 10.spx),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        feed['feedsName'] != null
                            ? feed['feedsName']?.trim() ?? ''
                            : '',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14.spx,
                                ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                _buildUnreadCount(context),
                _buildOptionsButton(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建Feed图标
  Widget _buildFeedIcon(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10),
      child: CachedNetworkImage(
        imageUrl: feed['rssFeeds']['img'] ??
            'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['rssFeeds']['originUrl']}&size=64',
        width: 20.spx,
        height: 20.spx,
        key: Key(feed['rssFeeds']['img']),
        fit: BoxFit.cover,
        errorWidget: (context, error, stackTrace) => Container(
          width: 20.spx,
          height: 20.spx,
          color: Theme.of(context).textTheme.bodySmall?.color,
          child: SvgPicture.asset(
            'assets/feeds/feeds_logo.svg',
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  /// 构建未读数量
  Widget _buildUnreadCount(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 2.spx,
        vertical: 2.spx,
      ),
      child: Text(
        '${feed['unreadCount'] ?? 0}',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontSize: 10.spx,
            fontWeight: FontWeight.w500,
            color: const Color(0XFF7F8EA7)),
      ),
    );
  }

  /// 构建选项按钮
  Widget _buildOptionsButton(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => onShowOptions(context, feed),
      child: Container(
        padding: const EdgeInsets.only(top: 4, left: 4, bottom: 4, right: 0),
        child: Icon(
          Icons.more_vert,
          size: 20.spx,
          color: const Color(0XFF7F8EA7),
        ),
      ),
    );
  }
}
