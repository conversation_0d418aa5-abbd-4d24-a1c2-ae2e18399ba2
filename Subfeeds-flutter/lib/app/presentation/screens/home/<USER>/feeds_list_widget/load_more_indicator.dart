import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';

/// 加载更多指示器组件，显示在列表底部
class LoadMoreIndicator extends StatelessWidget {
  /// 是否有更多数据
  final bool hasMoreData;

  /// 是否正在加载
  final bool isLoading;

  /// 是否有内容（用于决定是否显示"没有更多数据"的文本）
  final bool hasContent;

  const LoadMoreIndicator({
    Key? key,
    required this.hasMoreData,
    required this.isLoading,
    required this.hasContent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (hasMoreData) {
      return Padding(
        padding: const EdgeInsets.all(10.0),
        child: Center(
          child: isLoading
              ? LoadingIndicator()
              : Text(
                  'pull_up_to_load'.tr,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                ),
        ),
      );
    } else if (hasContent) {
      return Padding(
        padding: const EdgeInsets.all(10.0),
        child: Center(
          child: Text(
            'no_more_feeds'.tr,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey,
                  fontSize: 12,
                ),
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
