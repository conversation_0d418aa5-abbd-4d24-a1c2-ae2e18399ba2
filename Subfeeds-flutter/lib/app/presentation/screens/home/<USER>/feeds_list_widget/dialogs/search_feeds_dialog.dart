import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/empty_results.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

/// 搜索订阅源对话框组件
class SearchFeedsDialog extends StatefulWidget {
  /// 控制器
  final FeedsController controller;

  /// 显示选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowOptions;

  const SearchFeedsDialog({
    super.key,
    required this.controller,
    required this.onShowOptions,
  });

  @override
  State<SearchFeedsDialog> createState() => _SearchFeedsDialogState();
}

class _SearchFeedsDialogState extends State<SearchFeedsDialog> {
  late final TextEditingController searchController;
  late final RxList<Map<String, dynamic>> searchResults;
  late final RxBool isSearching;
  late final RxString searchText;
  Worker? searchDebounceWorker;

  // 创建焦点节点
  late final FocusNode searchFocusNode;

  @override
  void initState() {
    super.initState();

    // 初始化控制器和状态
    searchController = TextEditingController();
    searchResults = <Map<String, dynamic>>[].obs;
    isSearching = false.obs;
    searchText = ''.obs;

    // 初始化焦点节点
    searchFocusNode = FocusNode();

    // 在对话框显示时创建防抖worker
    searchDebounceWorker = debounce(
      searchText,
      (value) async {
        if (value.isEmpty) {
          searchResults.clear();
          return;
        }
        isSearching.value = true;
        final results = await widget.controller.searchFeeds(value);
        searchResults.value = results;
        isSearching.value = false;
      },
      time: const Duration(milliseconds: 300), // 300毫秒的防抖时间
    );
  }

  @override
  void dispose() {
    // 清理资源
    searchDebounceWorker?.dispose();
    searchController.dispose();
    searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕高度
    final screenHeight = MediaQuery.of(context).size.height;
    // 计算键盘高度
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    // 计算弹窗最大高度（考虑键盘高度）
    final maxDialogHeight = keyboardHeight > 0
        ? screenHeight * 0.95 // 键盘弹出时，弹窗高度为屏幕的90%
        : screenHeight * 0.95; // 键盘收起时，弹窗高度为屏幕的95%

    // 创建手势检测器，用于点击空白区域时取消焦点
    return GestureDetector(
      onTap: () {
        // 点击空白区域时取消焦点，收起键盘
        searchFocusNode.unfocus();
      },
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.light
              ? const Color(0xFFF7FAFF)
              : const Color(0xFF444444),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        height: maxDialogHeight,
        padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 2.spx),
        constraints: BoxConstraints(
          maxHeight: maxDialogHeight,
        ),
        child: SingleChildScrollView(
          // 添加滚动视图
          child: Column(
            mainAxisSize: MainAxisSize.min, // 使用最小高度
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 拖动条
              Center(
                child: Container(
                  margin: EdgeInsets.symmetric(vertical: 5.spx),
                  width: 40.spx,
                  height: 4.spx,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              SizedBox(height: 4.spx),
              _buildSearchField(
                  context, searchController, searchText, searchFocusNode),
              const SizedBox(height: 8),
              Container(
                constraints: BoxConstraints(
                  maxHeight: maxDialogHeight * 0.6, // 列表最大高度为弹窗高度的60%
                ),
                child: _buildFeedsList(
                    context, isSearching, searchResults, searchText),
              ),
              // 添加额外的底部空间，避免内容被键盘遮挡
              SizedBox(height: keyboardHeight > 0 ? 16 : 16),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建搜索输入框
  Widget _buildSearchField(
      BuildContext context,
      TextEditingController searchController,
      RxString searchText,
      FocusNode focusNode) {
    return TextField(
      autofocus: true, // 自动获取焦点，弹出键盘
      controller: searchController,
      focusNode: focusNode, // 关联焦点节点
      decoration: InputDecoration(
        isDense: true,
        filled: true,
        fillColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF5B5B5B)
            : const Color(0xFFE5E9F1),
        contentPadding:
            EdgeInsets.symmetric(horizontal: 12.spx, vertical: 12.spx),
        hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0XFF909090)
                  : const Color(0XFFBCC2CC),
            ),
        prefixIcon: Padding(
          padding: EdgeInsets.only(left: 12.spx, right: 4.spx),
          child: SvgPicture.asset(
            'assets/feeds/feeds_search.svg',
            width: 20.spx,
            height: 20.spx,
            colorFilter: ColorFilter.mode(
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0XFF909090)
                  : const Color(0XFFBCC2CC),
              BlendMode.srcIn,
            ),
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 20.spx,
          maxHeight: 20.spx,
        ),
        hintText: 'search_feeds'.tr,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 1,
          ),
        ),
        suffixIcon: Obx(() => searchText.value.isNotEmpty
            ? IconButton(
                onPressed: () {
                  searchController.clear();
                  searchText.value = '';
                },
                icon: const Icon(Icons.clear, size: 20),
                color: Colors.grey,
              )
            : const SizedBox.shrink()),
      ),
      onChanged: (value) {
        searchText.value = value;
      },
    );
  }

  /// 构建Feed列表
  Widget _buildFeedsList(BuildContext context, RxBool isSearching,
      RxList<Map<String, dynamic>> searchResults, RxString searchText) {
    return Obx(() {
      if (isSearching.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (searchText.value.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 40),
              Image.asset(
                'assets/images/note-empty.png',
                width: 120,
                height: 120,
                fit: BoxFit.contain,
              ),
              const SizedBox(height: 16),
              Text(
                'search_feeds_hint'.tr,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: const Color(0xFF72849c),
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      if (searchResults.isEmpty) {
        // 使用EmptyResults组件替换原有的空状态
        return const EmptyResults();
      }

      return ListView.builder(
        shrinkWrap: true,
        itemCount: searchResults.length,
        itemBuilder: (context, index) {
          final feed = searchResults[index];
          return _buildFeedItem(context, feed);
        },
      );
    });
  }

  /// 构建Feed项
  Widget _buildFeedItem(BuildContext context, Map<String, dynamic> feed) {
    return InkWell(
      onTap: () {
        // 跳转到该Feed的文章列表页
        Get.back(); // 关闭弹窗
        Get.toNamed(
          Routes.FEEDS_ARTICLES,
          arguments: {'feed': feed},
        );
      },
      child: Padding(
        padding: EdgeInsets.fromLTRB(8.spx, 8.spx, 2.spx, 8.spx),
        child: Row(
          children: [
            // RSS源Logo
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                feed['rssFeeds']['img'] ??
                    'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['rssFeeds']['originUrl']}&size=64',
                width: 25,
                height: 25,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 25,
                  height: 25,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                  child: SvgPicture.asset(
                    'assets/feeds/feeds_logo.svg',
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            // RSS源标题
            Expanded(
              child: Text(
                feed['feedsName']?.trim() ?? '', //去除空格
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            // 未读数量
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: 2.spx,
                vertical: 2.spx,
              ),
              child: Text(
                '${feed['unreadCount'] ?? 0}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontSize: 10.spx,
                    fontWeight: FontWeight.w500,
                    color: const Color(0XFF7F8EA7)),
              ),
            ),
            // 添加更多选项按钮
            _buildOptionsButton(context, feed),
          ],
        ),
      ),
    );
  }

  /// 构建选项按钮
  Widget _buildOptionsButton(BuildContext context, Map<String, dynamic> feed) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => widget.onShowOptions(context, feed),
      child: Container(
        padding: EdgeInsets.only(
            top: 4.spx, left: 4.spx, bottom: 4.spx, right: 0.spx),
        child: Icon(
          Icons.more_vert,
          size: 20.spx,
          color: const Color(0XFF7F8EA7),
        ),
      ),
    );
  }
}

/// 搜索订阅源对话框工具类
class SearchFeedsDialogUtils {
  /// 显示搜索订阅源对话框
  static void show(BuildContext context, FeedsController controller,
      Function(BuildContext, Map<String, dynamic>) onShowOptions) {
    // 使用showModalBottomSheet显示底部弹窗
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // 允许滚动控制
      backgroundColor: Colors.transparent, // 透明背景
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      enableDrag: true, // 允许拖动
      isDismissible: true, // 点击外部可关闭
      useRootNavigator: true, // 使用根导航器
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            // 当键盘弹出时，不调整底部弹窗的位置
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: SearchFeedsDialog(
            controller: controller,
            onShowOptions: onShowOptions,
          ),
        );
      },
    );
  }
}
