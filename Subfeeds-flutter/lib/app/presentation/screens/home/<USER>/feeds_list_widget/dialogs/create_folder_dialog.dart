import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';

/// 创建文件夹对话框组件
class CreateFolderDialog extends StatefulWidget {
  /// 控制器
  final FeedsController controller;

  const CreateFolderDialog({
    super.key,
    required this.controller,
  });

  @override
  State<CreateFolderDialog> createState() => _CreateFolderDialogState();
}

class _CreateFolderDialogState extends State<CreateFolderDialog> {
  late final TextEditingController folderNameController;
  late final TextEditingController searchController;
  late final RxList<int> selectedFeeds;
  late final RxList<Map<String, dynamic>> searchResults;
  late final RxBool isSearching;
  late final RxString searchText;
  Worker? searchDebounceWorker;

  // 创建焦点节点
  late final FocusNode folderNameFocusNode;
  late final FocusNode searchFocusNode;

  @override
  void initState() {
    super.initState();

    // 初始化控制器和状态
    folderNameController = TextEditingController();
    searchController = TextEditingController();
    selectedFeeds = <int>[].obs;
    searchResults = <Map<String, dynamic>>[].obs;
    isSearching = false.obs;
    searchText = ''.obs;

    // 初始化焦点节点
    folderNameFocusNode = FocusNode();
    searchFocusNode = FocusNode();

    // 在对话框显示时创建防抖worker
    searchDebounceWorker = debounce(
      searchText,
      (value) async {
        if (value.isEmpty) {
          searchResults.clear();
          return;
        }
        isSearching.value = true;
        final results = await widget.controller.searchFeeds(value);
        searchResults.value = results;
        isSearching.value = false;
      },
      time: const Duration(milliseconds: 500), // 500毫秒的防抖时间
    );
  }

  @override
  void dispose() {
    // 清理资源
    searchDebounceWorker?.dispose();
    folderNameController.dispose();
    searchController.dispose();
    folderNameFocusNode.dispose();
    searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕高度
    final screenHeight = MediaQuery.of(context).size.height;
    // 计算键盘高度
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    // 计算弹窗最大高度（考虑键盘高度）
    final maxDialogHeight = keyboardHeight > 0
        ? screenHeight // 键盘弹出时，弹窗高度为屏幕的50%
        : screenHeight; // 键盘收起时，弹窗高度为屏幕的70%

    // 创建手势检测器，用于点击空白区域时取消焦点
    return GestureDetector(
      onTap: () {
        // 点击空白区域时取消焦点，收起键盘
        folderNameFocusNode.unfocus();
        searchFocusNode.unfocus();
      },
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.light
              ? const Color(0xFFF7FAFF)
              : const Color(0xFF444444),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 2.spx),
        constraints: BoxConstraints(
            maxHeight: maxDialogHeight, minHeight: maxDialogHeight),
        child: SingleChildScrollView(
          // 添加滚动视图
          child: Column(
            mainAxisSize: MainAxisSize.min, // 使用最小高度
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 0.spx, vertical: 2.spx),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 取消按钮
                    GestureDetector(
                      onTap: () {
                        searchDebounceWorker?.dispose();
                        Get.back();
                      },
                      child: Text(
                        'cancel'.tr,
                        style: TextStyle(
                            color: const Color(0xFF8e8e8e),
                            fontSize: 12.spx,
                            fontFamily: 'PingFang SC',
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    // 中间标题
                    Text(
                      'create_folder'.tr,
                      style: TextStyle(
                        fontSize: 16.spx,
                        fontFamily: 'PingFang SC',
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black,
                      ),
                    ),
                    // 保存按钮
                    _buildSaveButton(context, folderNameController,
                        selectedFeeds, searchDebounceWorker),
                  ],
                ),
              ),
              SizedBox(height: 20.spx),
              Text(
                'folder_name'.tr,
                style: Theme.of(context)
                    .textTheme
                    .bodySmall
                    ?.copyWith(fontSize: 14, color: Color(0Xff8e8e8e)),
              ),
              SizedBox(height: 8.spx),
              _buildFolderNameField(
                  context, folderNameController, folderNameFocusNode),
              SizedBox(height: 16.spx),
              Text(
                'select_feeds'.tr,
                style: Theme.of(context)
                    .textTheme
                    .bodySmall
                    ?.copyWith(fontSize: 14, color: Color(0Xff8e8e8e)),
              ),
              SizedBox(height: 8.spx),
              _buildSearchField(
                  context, searchController, searchText, searchFocusNode),
              SizedBox(height: 16.spx),
              Text(
                'feeds_uncategorized'.tr,
                style: Theme.of(context)
                    .textTheme
                    .bodySmall
                    ?.copyWith(fontSize: 14, color: Color(0Xff8e8e8e)),
              ),
              SizedBox(height: 8.spx),
              Container(
                constraints: BoxConstraints(
                  maxHeight: maxDialogHeight * 0.5, // 列表最大高度为弹窗高度的一半
                ),
                child: _buildFeedsList(
                    context, isSearching, searchResults, selectedFeeds),
              ),
              // 添加额外的底部空间，避免内容被键盘遮挡
              SizedBox(height: keyboardHeight > 0 ? 16 : 16),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建文件夹名称输入框
  Widget _buildFolderNameField(BuildContext context,
      TextEditingController controller, FocusNode focusNode) {
    return TextField(
      autofocus: false, // 修改为false，避免自动弹出键盘
      controller: controller,
      focusNode: focusNode, // 关联焦点节点
      decoration: InputDecoration(
        isDense: true,
        filled: true,
        fillColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF5B5B5B)
            : const Color(0xFFE5E9F1),
        hintStyle: TextStyle(
          fontSize: 14.spx,
          fontWeight: FontWeight.w400,
          color: Theme.of(context).brightness == Brightness.dark
              ? Color(0XFF909090)
              : Color(0XFFBCC2CC),
        ),
        prefixIcon: Padding(
          padding: EdgeInsets.only(left: 13.spx, right: 6.spx),
          child: SvgPicture.asset(
            'assets/feeds/feeds_folder.svg',
            width: 16.spx,
            height: 16.spx,
            colorFilter: ColorFilter.mode(
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0XFF909090)
                  : const Color(0XFFBCC2CC),
              BlendMode.srcIn,
            ),
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 20.spx,
          maxHeight: 20.spx,
        ),
        hintText: 'folder_name'.tr,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 1,
          ),
        ),
      ),
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: 14,
          ),
    );
  }

  /// 构建搜索输入框
  Widget _buildSearchField(
      BuildContext context,
      TextEditingController searchController,
      RxString searchText,
      FocusNode focusNode) {
    return TextField(
      autofocus: false, // 添加autofocus属性并设置为false，防止自动获取焦点导致标签切换
      controller: searchController,
      focusNode: focusNode, // 关联焦点节点
      decoration: InputDecoration(
        isDense: true,
        filled: true,
        fillColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF5B5B5B)
            : const Color(0xFFE5E9F1),
        contentPadding:
            EdgeInsets.symmetric(horizontal: 12.spx, vertical: 12.spx),
        hintStyle: TextStyle(
          fontSize: 14.spx,
          fontWeight: FontWeight.w400,
          color: Theme.of(context).brightness == Brightness.dark
              ? Color(0XFF909090)
              : Color(0XFFBCC2CC),
        ),
        prefixIcon: Padding(
          padding: EdgeInsets.only(left: 13.spx, right: 4.spx),
          child: SvgPicture.asset(
            'assets/feeds/feeds_search.svg',
            width: 20.spx,
            height: 20.spx,
            colorFilter: ColorFilter.mode(
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0XFF909090)
                  : const Color(0XFFBCC2CC),
              BlendMode.srcIn,
            ),
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 20.spx,
          maxHeight: 20.spx,
        ),
        hintText: 'search_feeds'.tr,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 1,
          ),
        ),
        suffixIcon: Obx(() => searchText.value.isNotEmpty
            ? IconButton(
                onPressed: () {
                  searchController.clear();
                  searchText.value = '';
                },
                icon: const Icon(Icons.clear, size: 20),
                color: Colors.grey,
              )
            : const SizedBox.shrink()),
      ),
      onChanged: (value) {
        searchText.value = value;
      },
    );
  }

  /// 构建Feed列表
  Widget _buildFeedsList(BuildContext context, RxBool isSearching,
      RxList<Map<String, dynamic>> searchResults, RxList<int> selectedFeeds) {
    return Obx(() {
      if (isSearching.value) {
        return const Center(child: CircularProgressIndicator());
      }

      final displayList = searchResults.isNotEmpty
          ? searchResults
          : widget.controller.feedsList;

      return ListView.builder(
        shrinkWrap: true,
        itemCount: displayList.length,
        itemBuilder: (context, index) {
          final feed = displayList[index];
          return _buildFeedItem(context, feed, selectedFeeds);
        },
      );
    });
  }

  /// 构建Feed项
  Widget _buildFeedItem(BuildContext context, Map<String, dynamic> feed,
      List<int> selectedFeeds) {
    final feedId = int.tryParse(feed['id'].toString()) ?? -1;

    return Obx(
      () => InkWell(
        onTap: () {
          if (selectedFeeds.contains(feedId)) {
            selectedFeeds.remove(feedId);
          } else {
            selectedFeeds.add(feedId);
          }
        },
        child: Container(
          padding: EdgeInsets.all(12.spx),
          margin: EdgeInsets.only(bottom: 6.spx),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(18.spx),
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF3B3B3B)
                : const Color(0xFFFFFFFF),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFFA2A9A4)
                    : const Color(0xFFFFFFFF),
              ),
            ],
          ),
          child: Row(
            children: [
              // RSS源Logo
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  feed['rssFeeds']['img'] ??
                      'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['rssFeeds']['originUrl']}&size=64',
                  width: 25,
                  height: 25,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    width: 25,
                    height: 25,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                    child: SvgPicture.asset(
                      'assets/feeds/feeds_logo.svg',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // RSS源标题
              Expanded(
                child: Text(
                  feed['feedsName']?.trim() ?? '', //去除空格
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Spacer(),
              SizedBox(
                width: 24,
                height: 24,
                child: Checkbox(
                  value: selectedFeeds.contains(feedId),
                  onChanged: (bool? value) {
                    if (value == true) {
                      selectedFeeds.add(feedId);
                    } else {
                      selectedFeeds.remove(feedId);
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建保存按钮
  Widget _buildSaveButton(
      BuildContext context,
      TextEditingController folderNameController,
      RxList<int> selectedFeeds,
      Worker? searchDebounceWorker) {
    return SizedBox(
      child: TextButton(
        onPressed: () async {
          if (folderNameController.text.isEmpty) {
            Get.snackbar(
              'error'.tr,
              'please_input_folder_name'.tr,
              snackPosition: SnackPosition.TOP,
              icon: const Icon(Icons.error, color: Colors.red),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
            return;
          }
          if (selectedFeeds.isEmpty) {
            Get.snackbar(
              'error'.tr,
              'please_seslect_feeds'.tr,
              snackPosition: SnackPosition.TOP,
              icon: const Icon(Icons.error, color: Colors.red),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
            return;
          }
          final response = await widget.controller.createFolder(
            folderNameController.text,
            selectedFeeds,
          );

          searchDebounceWorker?.dispose();

          if (response.code >= 0) {
            Get.back();
            Get.snackbar(
              'success'.tr,
              'folder_create_success'.tr,
              snackPosition: SnackPosition.TOP,
              icon: SvgPicture.asset('assets/feeds/right.svg'),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
            widget.controller.refreshFeeds();
          } else {
            Get.snackbar(
              'error'.tr,
              response.msg.isNotEmpty
                  ? response.msg
                  : 'folder_create_failed'.tr,
              snackPosition: SnackPosition.TOP,
              icon: const Icon(Icons.error, color: Colors.red),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
          }
        },
        style: TextButton.styleFrom(
            backgroundColor: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF2B2B2B)
                : Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            minimumSize: Size(48.spx, 28.spx), // 设置按钮最小尺寸
            padding: EdgeInsets.symmetric(horizontal: 0)),
        child: Text(
          'save'.tr,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12),
        ),
      ),
    );
  }
}

/// 创建文件夹对话框工具类
class CreateFolderDialogUtils {
  /// 显示创建文件夹对话框
  static void show(BuildContext context, FeedsController controller) {
    // 使用showModalBottomSheet代替Get.bottomSheet，可以更好地控制键盘行为
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // 允许滚动控制
      backgroundColor: Colors.transparent, // 透明背景
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.85,
      ),
      enableDrag: true, // 允许拖动
      isDismissible: true, // 点击外部可关闭
      useRootNavigator: true, // 使用根导航器
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            // 当键盘弹出时，调整底部弹窗的位置以避免被键盘遮挡
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: CreateFolderDialog(controller: controller),
        );
      },
    );
  }
}
