import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_svg/svg.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/dialogs/confirmation_dialog.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/feeds_list_utils.dart';

/// 编辑订阅源底部弹出菜单
class EditFeedsBottomSheet {
  /// 显示编辑订阅源底部弹出菜单
  static Future<void> show(
    BuildContext context,
    FeedsController controller,
  ) async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).brightness == Brightness.light
          ? const Color(0xFFF7FAFF)
          : const Color(0xFF444444),
      builder: (context) => _EditFeedsContent(controller: controller),
    );
  }
}

/// 编辑订阅源弹出菜单内容
class _EditFeedsContent extends StatefulWidget {
  /// 控制器
  final FeedsController controller;

  const _EditFeedsContent({required this.controller});

  @override
  State<_EditFeedsContent> createState() => _EditFeedsContentState();
}

class _EditFeedsContentState extends State<_EditFeedsContent> {
  // 选中的项目ID集合，包含类型信息
  final Set<String> _selectedItems = <String>{};

  // 是否处于选择模式
  bool _isSelectionMode = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFFf7faff)
            : const Color(0xFF444444),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.spx),
          topRight: Radius.circular(16.spx),
        ),
      ),
      height: MediaQuery.of(context).size.height * 0.85,
      child: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: _buildCombinedList(context),
          ),
          // if (_isSelectionMode) _buildBottomActions(context),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 6.spx),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Text(
              'cancel'.tr,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 12.spx,
                    fontFamily: 'PingFang SC',
                  ),
            ),
          ),
          Row(
            children: [
              // if (_isSelectionMode)
              //   GestureDetector(
              //     onTap: _toggleSelectAll,
              //     child: Text(
              //       _isAllSelected() ? 'deselect_all'.tr : 'select_all'.tr,
              //       style: Theme.of(context).textTheme.titleLarge?.copyWith(
              //             fontWeight: FontWeight.w600,
              //             fontSize: 12.spx,
              //             fontFamily: 'PingFang SC',
              //           ),
              //     ),
              //   ),
              // if (_isSelectionMode) SizedBox(width: 8.spx),
              GestureDetector(
                onTap: _toggleSelectionMode,
                child: SvgPicture.asset(
                  'assets/feeds/batch.svg',
                  width: 20.spx,
                  height: 20.spx,
                  colorFilter: ColorFilter.mode(
                    _isSelectionMode
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              SizedBox(width: 8.spx),
              _buildSaveButton(context),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建合并列表
  Widget _buildCombinedList(BuildContext context) {
    return Obx(() {
      // 合并文件夹和订阅源列表
      final List<Map<String, dynamic>> combinedList = [];

      // 添加文件夹
      for (final folder in widget.controller.foldersList) {
        combinedList.add({
          ...folder,
          'itemType': 'folder',
        });
      }

      // 添加订阅源
      for (final feed in widget.controller.feedsList) {
        combinedList.add({
          ...feed,
          'itemType': 'feed',
        });
      }

      if (combinedList.isEmpty) {
        return Center(
          child: Text(
            'no_items_found'.tr,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey,
                ),
          ),
        );
      }

      return ListView.builder(
        padding: EdgeInsets.all(0.spx),
        itemCount: combinedList.length,
        itemBuilder: (context, index) {
          final item = combinedList[index];
          final itemType = item['itemType'] as String;
          final itemId =
              '${itemType == 'folder' ? 'folder_' : 'feed_'}${itemType == 'folder' ? item['id'] : item['feedsId']}';
          final isSelected = _selectedItems.contains(itemId);

          if (itemType == 'folder') {
            return _buildFolderItem(context, item, isSelected);
          } else {
            return _buildFeedItem(context, item, isSelected);
          }
        },
      );
    });
  }

  /// 构建保存按钮
  Widget _buildSaveButton(
    BuildContext context,
  ) {
    return SizedBox(
      child: TextButton(
        onPressed: () async {
          if (_isSelectionMode) {
            if (_selectedItems.isNotEmpty) {
              _showDeleteConfirmation();
            }
          } else {
            Navigator.of(context).pop();
          }
        },
        style: TextButton.styleFrom(
            backgroundColor: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF2B2B2B)
                : Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            minimumSize: Size(48.spx, 28.spx), // 设置按钮最小尺寸
            padding: EdgeInsets.symmetric(horizontal: 12.spx)),
        child: Text(
          'delete'.tr,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12),
        ),
      ),
    );
  }

  /// 切换选择模式
  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedItems.clear();
      }
    });
  }

  /// 构建文件夹项
  Widget _buildFolderItem(
      BuildContext context, Map<String, dynamic> folder, bool isSelected) {
    final folderName =
        folder['groupName'] ?? folder['name'] ?? 'Unnamed Folder';
    final itemId = 'folder_${folder['id']}';

    return Container(
      margin: EdgeInsets.only(bottom: 8.spx),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8.spx),
      ),
      child: GestureDetector(
        onTap: _isSelectionMode ? () => _toggleItemSelection(itemId) : null,
        child: Container(
          color: Colors.transparent,
          padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 8.spx),
          child: Row(
            children: [
              Container(
                width: 20.spx,
                height: 20.spx,
                padding: EdgeInsets.all(4.spx),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(20.spx),
                ),
                child: SvgPicture.asset(
                  'assets/feeds/feeds_folder.svg',
                  fit: BoxFit.cover,
                  colorFilter: ColorFilter.mode(
                    Colors.white,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              SizedBox(width: 10.spx),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      folderName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 14.spx,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.spx),
              _buildActions(context, itemId, isSelected, true),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建订阅源项
  Widget _buildFeedItem(
      BuildContext context, Map<String, dynamic> feed, bool isSelected) {
    final feedTitle = feed['feedsName'] ?? feed['title'] ?? 'Untitled Feed';
    final itemId = 'feed_${feed['feedsId']}';

    return Container(
      margin: EdgeInsets.only(bottom: 8.spx),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8.spx),
      ),
      child: GestureDetector(
        onTap: _isSelectionMode ? () => _toggleItemSelection(itemId) : null,
        child: Container(
          color: Colors.transparent,
          padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 8.spx),
          child: Row(
            children: [
              _buildFeedIcon(context, feed),
              SizedBox(width: 10.spx),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      feedTitle,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 14.spx,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.spx),
              _buildActions(context, itemId, isSelected, false),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActions(
      BuildContext context, String itemId, bool isSelected, bool isFolder) {
    if (_isSelectionMode) {
      // 在批量模式下显示Radio
      return SizedBox(
        width: 30.spx,
        height: 30.spx,
        child: Radio<bool>(
          value: true,
          focusColor: Theme.of(context).colorScheme.primary,
          groupValue: isSelected ? true : null,
          onChanged: (_) => _toggleItemSelection(itemId),
          activeColor: Theme.of(context).colorScheme.primary,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      );
    } else {
      // 在非批量模式下显示编辑和删除按钮R
      return Row(
        children: [
          SizedBox(
            width: 30.spx,
            height: 30.spx,
            child: IconButton(
              style: IconButton.styleFrom(
                backgroundColor: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0XFF3B3B3B)
                    : const Color(0XFFFFFFFF),
                padding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.spx),
                ),
                minimumSize: Size(30.spx, 30.spx),
                maximumSize: Size(30.spx, 30.spx),
              ),
              onPressed: () {
                // 显示重命名对话框
                if (isFolder) {
                  // 获取文件夹信息
                  final folder = widget.controller.foldersList.firstWhere(
                    (f) => f['id'].toString() == itemId,
                    orElse: () => <String, dynamic>{},
                  );
                  if (folder.isNotEmpty) {
                    // 使用FeedsListUtils中的方法重命名文件夹
                    FeedsListUtils.showFolderRenameDialog(
                        context, folder, widget.controller);
                  }
                } else {
                  // 获取订阅源信息
                  final feed = widget.controller.feedsList.firstWhere(
                    (f) => f['feedsId'].toString() == itemId,
                    orElse: () => <String, dynamic>{},
                  );
                  if (feed.isNotEmpty) {
                    // 使用FeedsListUtils中的方法重命名订阅源
                    FeedsListUtils.showFeedRenameDialog(
                        context, feed, widget.controller);
                  }
                }
              },
              icon: SvgPicture.asset(
                width: 20.spx,
                height: 20.spx,
                colorFilter: ColorFilter.mode(
                  const Color(0XFF7F8EA7),
                  BlendMode.srcIn,
                ),
                'assets/feeds/feeds_pen.svg',
                fit: BoxFit.cover,
              ),
            ),
          ),
          SizedBox(width: 10.spx),
          SizedBox(
            width: 30.spx,
            height: 30.spx,
            child: IconButton(
              style: IconButton.styleFrom(
                backgroundColor: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0XFF3B3B3B)
                    : const Color(0XFFFFFFFF),
                padding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.spx),
                ),
                minimumSize: Size(30.spx, 30.spx),
                maximumSize: Size(30.spx, 30.spx),
              ),
              onPressed: () {
                // 显示删除确认对话框
                if (isFolder) {
                  ConfirmationDialog.show(
                    context: context,
                    title: 'delete'.tr,
                    message: 'are_you_sure_to_delete_folder'.tr,
                    confirmText: 'delete',
                    cancelText: 'cancel',
                    confirmColor: const Color(0xFFd0452f),
                    iconPath: 'assets/feeds/feeds_error.svg',
                    onConfirm: () async {
                      final folderId = int.tryParse(itemId.substring(7)) ?? -1;
                      print('folderId: $folderId, itemId: $itemId');
                      if (folderId != -1) {
                        await widget.controller
                            .deleteGroupFeedsNoTips([folderId]);
                      }
                    },
                  );
                } else {
                  ConfirmationDialog.show(
                    context: context,
                    title: 'unfollow'.tr,
                    message: 'are_you_sure_to_unfollow'.tr,
                    confirmText: 'unfollow',
                    cancelText: 'cancel',
                    confirmColor: const Color(0xFFd0452f),
                    iconPath: 'assets/feeds/feeds_error.svg',
                    onConfirm: () async {
                      final feedId = int.tryParse(itemId.substring(5)) ?? -1;
                      print('feedId: $feedId, itemId: $itemId');
                      if (feedId != -1) {
                        await widget.controller.deleteRss(feedId);
                      }
                    },
                  );
                }
              },
              icon: SvgPicture.asset(
                width: 20.spx,
                height: 20.spx,
                'assets/feeds/feeds_delete.svg',
                fit: BoxFit.cover,
              ),
            ),
          ),
        ],
      );
    }
  }

  /// 构建Feed图标
  Widget _buildFeedIcon(BuildContext context, Map<String, dynamic> feed) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10),
      child: CachedNetworkImage(
        imageUrl: feed['rssFeeds']['img'] ??
            'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['rssFeeds']['originUrl']}&size=64',
        width: 20.spx,
        height: 20.spx,
        key: Key(feed['rssFeeds']['img']),
        fit: BoxFit.cover,
        errorWidget: (context, error, stackTrace) => Container(
          width: 20.spx,
          height: 20.spx,
          color: Theme.of(context).textTheme.bodySmall?.color,
          child: SvgPicture.asset(
            'assets/feeds/feeds_logo.svg',
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  /// 切换项目选择状态
  void _toggleItemSelection(String itemId) {
    setState(() {
      if (_selectedItems.contains(itemId)) {
        _selectedItems.remove(itemId);
      } else {
        _selectedItems.add(itemId);
      }
    });
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmation() {
    // 分析选中的项目类型
    final selectedFolders =
        _selectedItems.where((id) => id.startsWith('folder_')).toList();
    final selectedFeeds =
        _selectedItems.where((id) => id.startsWith('feed_')).toList();

    String title;
    String message;
    String confirmText;

    if (selectedFolders.isNotEmpty && selectedFeeds.isNotEmpty) {
      // 同时选中了文件夹和订阅源
      title = 'delete_items'.tr;
      message = 'are_you_sure_to_delete_mixed_items'
          .tr
          .replaceAll('{folders}', selectedFolders.length.toString())
          .replaceAll('{feeds}', selectedFeeds.length.toString());
      confirmText = 'delete'.tr;
    } else if (selectedFolders.isNotEmpty) {
      // 只选中了文件夹
      title = 'delete_folders'.tr;
      message = 'are_you_sure_to_delete_folders'
          .tr
          .replaceAll('{count}', selectedFolders.length.toString());
      confirmText = 'delete'.tr;
    } else {
      // 只选中了订阅源
      title = 'unsubscribe'.tr;
      message = 'are_you_sure_to_unsubscribe_feeds'
          .tr
          .replaceAll('{count}', selectedFeeds.length.toString());
      confirmText = 'unsubscribe'.tr;
    }

    ConfirmationDialog.show(
      context: context,
      title: title,
      message: message,
      confirmText: confirmText,
      cancelText: 'cancel'.tr,
      confirmColor: const Color(0xFFd0452f),
      iconPath: 'assets/feeds/feeds_error.svg',
      onConfirm: () async {
        await _deleteSelectedItems();
      },
    );
  }

  /// 删除选中的项目
  Future<void> _deleteSelectedItems() async {
    // 分离文件夹和订阅源ID
    final folderIds = <int>[];
    final feedIds = <int>[];

    for (final itemId in _selectedItems) {
      if (itemId.startsWith('folder_')) {
        final id = int.tryParse(itemId.substring(7)); // 移除 'folder_' 前缀
        if (id != null) folderIds.add(id);
      } else if (itemId.startsWith('feed_')) {
        final id = int.tryParse(itemId.substring(5)); // 移除 'feed_' 前缀
        if (id != null) feedIds.add(id);
      }
    }

    // 批量删除文件夹
    if (folderIds.isNotEmpty) {
      await widget.controller.deleteGroupFeedsNoTips(folderIds);
    }
    print('feedIds: $feedIds');
    // 批量取消订阅
    if (feedIds.isNotEmpty) {
      print('feedIds: $feedIds');
      await widget.controller.deleteRssBatch(feedIds);
    }

    // 清除选择状态并退出选择模式
    setState(() {
      _selectedItems.clear();
      _isSelectionMode = false;
    });
  }
}
