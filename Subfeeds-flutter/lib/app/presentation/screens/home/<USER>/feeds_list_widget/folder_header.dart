import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/dialogs/search_feeds_dialog.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/feeds_list_utils.dart';

/// 文件夹标题栏组件
class FolderHeader extends StatelessWidget {
  /// 是否固定
  final bool isFixed;

  /// 创建文件夹回调
  final VoidCallback onCreateFolder;

  /// 切换固定状态回调
  final VoidCallback onToggleFixed;

  /// 控制器
  final FeedsController controller;

  /// 显示Feed选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowFeedOptions;

  const FolderHeader({
    Key? key,
    required this.isFixed,
    required this.onCreateFolder,
    required this.onToggleFixed,
    required this.controller,
    required this.onShowFeedOptions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
          left: 9.spx, right: 9.spx, top: 10.spx, bottom: 5.spx),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'folder'.tr,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          Row(
            children: [
              SizedBox(
                width: 15.spx,
                height: 15.spx,
                child: IconButton(
                  onPressed: () {
                    // 打开编辑弹窗
                    FeedsListUtils.showEditFeedsSheet(context, controller);
                  },
                  padding: EdgeInsets.zero,
                  icon: SvgPicture.asset(
                    'assets/feeds/edit.svg',
                    colorFilter: ColorFilter.mode(
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : const Color(0xFF333333),
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 11.spx),
              SizedBox(
                width: 19.spx,
                height: 19.spx,
                child: IconButton(
                  onPressed: () {
                    // 点击搜索图标，显示搜索弹窗
                    SearchFeedsDialogUtils.show(
                      context,
                      controller,
                      onShowFeedOptions,
                    );
                  },
                  padding: EdgeInsets.zero,
                  icon: SvgPicture.asset(
                    'assets/feeds/feeds_search.svg',
                    colorFilter: ColorFilter.mode(
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : const Color(0xFF333333),
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 11.spx),
              SizedBox(
                width: 15.spx,
                height: 15.spx,
                child: IconButton(
                  onPressed: onCreateFolder,
                  padding: EdgeInsets.zero,
                  icon: SvgPicture.asset(
                    'assets/icons/add.svg',
                    colorFilter: ColorFilter.mode(
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : const Color(0xFF333333),
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12.5.spx),
            ],
          ),
        ],
      ),
    );
  }
}
