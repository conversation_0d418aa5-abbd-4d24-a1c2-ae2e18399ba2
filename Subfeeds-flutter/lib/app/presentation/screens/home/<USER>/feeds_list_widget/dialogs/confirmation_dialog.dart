import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 确认对话框组件
class ConfirmationDialog extends StatelessWidget {
  /// 标题
  final String title;

  /// 消息
  final String message;

  /// 确认按钮文本
  final String confirmText;

  /// 取消按钮文本
  final String cancelText;

  /// 确认按钮颜色
  final Color confirmColor;

  /// 图标路径
  final String? iconPath;

  /// 确认回调
  final VoidCallback onConfirm;

  const ConfirmationDialog({
    Key? key,
    required this.title,
    required this.message,
    this.confirmText = 'confirm',
    this.cancelText = 'cancel',
    this.confirmColor = const Color(0xFFd0452f),
    this.iconPath,
    required this.onConfirm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Theme.of(context).brightness == Brightness.light
          ? const Color(0xFFF7FAFF)
          : const Color(0xFF444444),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (iconPath != null)
              SvgPicture.asset(
                iconPath!,
                width: 50,
                height: 50,
              ),
            const SizedBox(height: 16),
            Text(
              message,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontSize: 14.spx,
                    color: const Color(0xFF999999),
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Get.back(),
                    style: TextButton.styleFrom(
                      backgroundColor: const Color(0xFFE6E6E6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                      ),
                    ),
                    child: Text(
                      cancelText.tr,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: const Color(0xFF999999),
                            fontWeight: FontWeight.w600,
                            fontSize: 14.spx,
                          ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      Get.back();
                      onConfirm();
                    },
                    style: TextButton.styleFrom(
                      backgroundColor: confirmColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                      ),
                    ),
                    child: Text(
                      confirmText.tr,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontSize: 14.spx,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 显示确认对话框
  static void show({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = 'confirm',
    String cancelText = 'cancel',
    Color confirmColor = const Color(0xFFd0452f),
    String? iconPath,
    required VoidCallback onConfirm,
  }) {
    Get.dialog(
      ConfirmationDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        confirmColor: confirmColor,
        iconPath: iconPath,
        onConfirm: onConfirm,
      ),
    );
  }
}
