import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/empty_state_widget.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/feeds_list.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/feeds_list_utils.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/folder_header.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/folders_list.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/quick_tabs_row.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';

/// Feeds列表主视图组件
class FeedsListView extends StatefulWidget {
  /// 控制器
  final FeedsController controller;

  const FeedsListView({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<FeedsListView> createState() => _FeedsListViewState();
}

class _FeedsListViewState extends State<FeedsListView> {
  /// 是否固定文件夹
  final RxBool isFolderFixed = false.obs;

  /// 用户计数数据
  final RxMap<String, dynamic> userCounts = <String, dynamic>{}.obs;

  /// 是否已经加载过用户计数数据
  bool _hasLoadedUserCounts = false;

  @override
  void initState() {
    super.initState();
    // 只在初始化时获取一次用户计数数据
    _loadUserCountsOnce();
  }

  /// 只加载一次用户计数数据
  void _loadUserCountsOnce() {
    if (!_hasLoadedUserCounts) {
      _hasLoadedUserCounts = true;
      widget.controller.getUserCount().then((response) {
        if (response.isSuccess) {
          userCounts.value = response.data ?? {};
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Obx(() {
        if (widget.controller.isLoading.value &&
            widget.controller.feedsList.isEmpty) {
          return Center(
            child: LoadingIndicator(
              size: 100.spx,
            ),
          );
        }
        return Stack(
          children: [
            DragTarget<Map<String, dynamic>>(
              onWillAccept: (data) {
                if (data == null) return false;
                final sourceFolderId = data['folderId'] as String?;
                return sourceFolderId != null; // 只接受来自文件夹的拖拽
              },
              onAccept: (data) async {
                final dragFeed = data['feed'] as Map<String, dynamic>;
                final sourceFolderId = data['folderId'] as String?;
                final feedId = int.tryParse(dragFeed['id'].toString()) ?? -1;

                if (feedId != -1 && sourceFolderId != null) {
                  await widget.controller.moveFeedToFolder(
                    feedId,
                    sourceFolderId,
                    "0", // 移动到未分组
                  );
                }
              },
              builder: (context, candidateData, rejectedData) {
                return RefreshIndicator(
                  onRefresh: widget.controller.refreshFeeds,
                  color: Theme.of(context).colorScheme.primary,
                  child: Container(
                    decoration: BoxDecoration(
                      color: candidateData.isNotEmpty
                          ? Theme.of(context)
                              .colorScheme
                              .primary
                              .withOpacity(0.1)
                          : null,
                    ),
                    child: widget.controller.feedsList.isEmpty &&
                            widget.controller.foldersList.isEmpty
                        ? const EmptyStateWidget()
                        : _buildContent(context),
                  ),
                );
              },
            ),
            // 固定在顶部的文件夹列表
            FixedFoldersList(
              controller: widget.controller,
              onShowFolderOptions: (context, folder) =>
                  FeedsListUtils.showFolderOptionsSheet(
                      context, folder, widget.controller),
              onShowFeedOptions: (context, feed) =>
                  FeedsListUtils.showFeedOptionsSheet(
                      context, feed, widget.controller),
              isFixed: isFolderFixed.value,
            ),
          ],
        );
      }),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(widget.controller.errorMessage.value),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: widget.controller.refreshFeeds,
            child: Text('retry'.tr),
          ),
        ],
      ),
    );
  }

  /// 构建内容
  Widget _buildContent(BuildContext context) {
    return CustomScrollView(
      controller: widget.controller.feedsListScrollController,
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        // 顶部快捷标签
        SliverToBoxAdapter(
          child: QuickTabsRow(userCounts: userCounts.value),
        ),

        // 文件夹标题
        SliverToBoxAdapter(
          child: Padding(
            padding: EdgeInsets.only(left: 9.spx, right: 9.spx, top: 10.spx),
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF3b3b3b)
                    : const Color(0xFFffffff),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10.spx),
                  topRight: Radius.circular(10.spx),
                ),
              ),
              child: FolderHeader(
                isFixed: isFolderFixed.value,
                onCreateFolder: () => FeedsListUtils.showCreateFolderDialog(
                    context, widget.controller),
                onToggleFixed: () => isFolderFixed.value = !isFolderFixed.value,
                controller: widget.controller,
                onShowFeedOptions: (context, feed) =>
                    FeedsListUtils.showFeedOptionsSheet(
                        context, feed, widget.controller),
              ),
            ),
          ),
        ),

        // 文件夹列表
        Obx(() => isFolderFixed.value
            ? SliverToBoxAdapter(
                child: SizedBox(), // 当固定显示时，这里只占位
              )
            : SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 9.spx),
                  child: FoldersList(
                    controller: widget.controller,
                    onShowFolderOptions: (context, folder) =>
                        FeedsListUtils.showFolderOptionsSheet(
                            context, folder, widget.controller),
                    onShowFeedOptions: (context, feed) =>
                        FeedsListUtils.showFeedOptionsSheet(
                            context, feed, widget.controller),
                  ),
                ),
              )),

        // 未分组的订阅源
        SliverPadding(
          padding: EdgeInsets.symmetric(horizontal: 9.spx),
          sliver: SliverToBoxAdapter(
            child: FeedsList(
              controller: widget.controller,
              onShowOptions: (context, feed) =>
                  FeedsListUtils.showFeedOptionsSheet(
                      context, feed, widget.controller),
            ),
          ),
        ),

        // 底部间距
        SliverToBoxAdapter(
          child: SizedBox(height: 20),
        ),
      ],
    );
  }
}
