import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/dialogs/confirmation_dialog.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/dialogs/create_folder_dialog.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/dialogs/edit_feeds_bottom_sheet.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/dialogs/rename_dialog.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/feed_options_sheet.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/folder_options_sheet.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// Feeds列表工具类，提供对话框和底部弹窗的显示方法
class FeedsListUtils {
  /// 显示编辑弹窗
  static void showEditFeedsSheet(
    BuildContext context,
    FeedsController controller,
  ) {
    EditFeedsBottomSheet.show(context, controller);
  }

  /// 显示Feed选项底部弹窗
  static void showFeedOptionsSheet(BuildContext context,
      Map<String, dynamic> feed, FeedsController controller) {
    FeedOptionsSheet.show(
      context,
      feed,
      onRename: (feed) => showFeedRenameDialog(context, feed, controller),
      onViewProperties: (feed) => showFeedPropertiesDialog(context, feed),
      onMarkAllAsRead: (feed) =>
          showMarkAllAsReadDialog(context, feed, controller),
      onUnfollow: (feed) => showUnfollowFeedDialog(context, feed, controller),
    );
  }

  /// 显示文件夹选项底部弹窗
  static void showFolderOptionsSheet(BuildContext context,
      Map<String, dynamic> folder, FeedsController controller) {
    FolderOptionsSheet.show(
      context,
      folder,
      onRename: (folder) => showFolderRenameDialog(context, folder, controller),
      onDelete: (folder) =>
          showUnfollowFolderDialog(context, folder, controller),
    );
  }

  /// 显示创建文件夹对话框
  static void showCreateFolderDialog(
      BuildContext context, FeedsController controller) {
    CreateFolderDialogUtils.show(context, controller);
  }

  /// 显示重命名Feed对话框
  static void showFeedRenameDialog(BuildContext context,
      Map<String, dynamic> feed, FeedsController controller) {
    RenameDialog.show(
      context: context,
      title: 'rename_feed'.tr,
      initialName: feed['feedsName'] ?? '',
      hintText: 'enter_new_name'.tr,
      onConfirm: (newName) async {
        await controller.updateRssSub(
          int.tryParse(feed['id'].toString()) ?? -1,
          newName,
        );
      },
    );
  }

  /// 显示重命名文件夹对话框
  static void showFolderRenameDialog(BuildContext context,
      Map<String, dynamic> folder, FeedsController controller) {
    RenameDialog.show(
      context: context,
      title: 'rename'.tr,
      initialName: folder['groupName'] ?? '',
      hintText: 'enter_new_folder_name'.tr,
      onConfirm: (newName) async {
        await controller.updateFeedsGroup(
          int.tryParse(folder['id'].toString()) ?? -1,
          newName,
        );
      },
    );
  }

  /// 显示Feed属性对话框
  static void showFeedPropertiesDialog(
      BuildContext context, Map<String, dynamic> feed) {
    final rssFeeds = feed['rssFeeds'] as Map<String, dynamic>? ?? {};

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF444444)
              : const Color(0xFFF7FAFF),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    feed['img'] ??
                        'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${rssFeeds['originUrl']}&size=64',
                    width: 25.spx,
                    height: 25.spx,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: 25.spx,
                      height: 25.spx,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                      child: SvgPicture.asset('assets/feeds/feeds_logo.svg'),
                    ),
                  ),
                ),
                SizedBox(width: 12.spx),
                Expanded(
                  child: Text(
                    feed['feedsName'] ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontSize: 14.spx,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.spx),
            Container(
              padding: EdgeInsets.all(12.spx),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF565656)
                    : Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildPropertyRow(
                    context,
                    'feed_url'.tr,
                    rssFeeds['link'] ?? '',
                  ),
                  const SizedBox(height: 12),
                  _buildPropertyRow(
                    context,
                    'website'.tr,
                    rssFeeds['originUrl'] ?? '',
                  ),
                  const SizedBox(height: 12),
                  _buildPropertyRow(
                    context,
                    'follow_date'.tr,
                    _formatDateTime(feed['updateTime'] ?? ''),
                  ),
                  const SizedBox(height: 12),
                  _buildPropertyRow(
                    context,
                    'unread'.tr,
                    '${feed['unreadCount'] ?? 0}',
                  ),
                ],
              ),
            ),
            SizedBox(height: 16.spx),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.end,
            //   children: [
            //     TextButton(
            //       onPressed: () => Get.back(),
            //       style: TextButton.styleFrom(
            //         backgroundColor: const Color(0xFFb9c0eb),
            //         shape: RoundedRectangleBorder(
            //           borderRadius: BorderRadius.circular(8),
            //         ),
            //         padding: const EdgeInsets.symmetric(
            //           vertical: 12,
            //         ),
            //       ),
            //       child: Text(
            //         'close'.tr,
            //         style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            //               color: Colors.white,
            //               fontWeight: FontWeight.w500,
            //             ),
            //       ),
            //     ),
            //   ],
            // ),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    );
  }

  /// 构建属性行
  static Widget _buildPropertyRow(
      BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).textTheme.bodySmall?.color,
                fontSize: 13,
              ),
        ),
        const SizedBox(height: 4),
        GestureDetector(
          onTap: () {
            Clipboard.setData(ClipboardData(text: value));
            Get.snackbar(
              'success'.tr,
              'copied'.tr,
              snackPosition: SnackPosition.TOP,
              icon: SvgPicture.asset('assets/feeds/right.svg'),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 14,
                          height: 1.4,
                        ),
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.copy_outlined,
                  size: 16,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 格式化时间戳为日期字符串
  static String _formatDateTime(dynamic timestamp) {
    if (timestamp == null || timestamp.toString().isEmpty) return '';
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(
        int.parse(timestamp.toString()) * 1000,
      );
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  /// 显示标记全部已读对话框
  static void showMarkAllAsReadDialog(BuildContext context,
      Map<String, dynamic> feed, FeedsController controller) {
    final unreadCount = feed['unreadCount'] ?? 0;
    if (unreadCount <= 0) {
      Get.snackbar(
        'notice'.tr,
        'no_unread_articles'.tr,
        snackPosition: SnackPosition.TOP,
        icon: SvgPicture.asset('assets/feeds/right.svg'),
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(context).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      return;
    }

    ConfirmationDialog.show(
      context: context,
      title: 'mark_all_as_read'.tr,
      message: 'are_you_sure_to_mark_all_as_read'.tr,
      confirmText: 'confirm',
      cancelText: 'cancel',
      confirmColor: Theme.of(context).colorScheme.primary,
      onConfirm: () async {
        await controller.updateUserFeedsIsReadStatus(
          [int.tryParse(feed['feedsId'].toString()) ?? -1],
        );
      },
    );
  }

  /// 显示取消订阅对话框
  static void showUnfollowFeedDialog(BuildContext context,
      Map<String, dynamic> feed, FeedsController controller) {
    ConfirmationDialog.show(
      context: context,
      title: 'unfollow'.tr,
      message: 'are_you_sure_to_unfollow'.tr,
      confirmText: 'unfollow',
      cancelText: 'cancel',
      confirmColor: const Color(0xFFd0452f),
      iconPath: 'assets/feeds/feeds_error.svg',
      onConfirm: () async {
        await controller.deleteRss(
          int.tryParse(feed['feedsId'].toString()) ?? -1,
        );
      },
    );
  }

  /// 显示删除文件夹对话框
  static void showUnfollowFolderDialog(BuildContext context,
      Map<String, dynamic> folder, FeedsController controller) {
    ConfirmationDialog.show(
      context: context,
      title: 'delete'.tr,
      message: 'are_you_sure_to_delete_folder'.tr,
      confirmText: 'delete',
      cancelText: 'cancel',
      confirmColor: const Color(0xFFd0452f),
      iconPath: 'assets/feeds/feeds_error.svg',
      onConfirm: () async {
        // 获取文件夹ID
        final folderId = int.tryParse(folder['id'].toString()) ?? -1;

        if (folderId != -1) {
          await controller.deleteGroupFeeds([folderId]);
        }
      },
    );
  }
}
