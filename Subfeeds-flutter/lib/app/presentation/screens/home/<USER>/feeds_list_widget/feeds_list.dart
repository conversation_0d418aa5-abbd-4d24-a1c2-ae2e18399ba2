import 'package:flutter/material.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/feed_item.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/load_more_indicator.dart';

/// Feed列表组件，显示未分组的订阅源列表
class FeedsList extends StatelessWidget {
  /// 控制器
  final FeedsController controller;

  /// 显示选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowOptions;

  const FeedsList({
    Key? key,
    required this.controller,
    required this.onShowOptions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(0),
      ),
      elevation: 0,
      margin: EdgeInsets.zero,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 未分组的订阅源列表
          ...controller.feedsList
              .map((feed) => KeyedSubtree(
                    key: ValueKey(controller.getItemKey(feed)),
                    child: FeedItem(
                      feed: feed,
                      onShowOptions: onShowOptions,
                    ),
                  ))
              .toList(),

          // 加载更多提示
          LoadMoreIndicator(
            hasMoreData: controller.hasMoreData.value,
            isLoading: controller.isLoading.value,
            hasContent: controller.feedsList.isNotEmpty ||
                controller.foldersList.isNotEmpty,
          ),
        ],
      ),
    );
  }
}

/// 可滚动的Feed列表组件
class ScrollableFeedsList extends StatelessWidget {
  /// 控制器
  final FeedsController controller;

  /// 显示选项菜单的回调
  final Function(BuildContext, Map<String, dynamic>) onShowOptions;

  const ScrollableFeedsList({
    Key? key,
    required this.controller,
    required this.onShowOptions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: controller.feedsListScrollController,
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(vertical: 0),
      itemCount: controller.feedsList.length + 1,
      itemBuilder: (context, index) {
        if (index == controller.feedsList.length) {
          // 加载更多指示器
          return LoadMoreIndicator(
            hasMoreData: controller.hasMoreData.value,
            isLoading: controller.isLoading.value,
            hasContent: controller.feedsList.isNotEmpty ||
                controller.foldersList.isNotEmpty,
          );
        }

        // 显示未分类的订阅源
        final feed = controller.feedsList[index];
        return KeyedSubtree(
          key: ValueKey(controller.getItemKey(feed)),
          child: FeedItem(
            feed: feed,
            onShowOptions: onShowOptions,
          ),
        );
      },
    );
  }
}
