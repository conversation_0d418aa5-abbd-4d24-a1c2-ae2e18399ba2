import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/new_widget/article_item.dart';

/// 新闻骨架屏组件
class NewsSkeleton extends StatelessWidget {
  final int itemCount;
  final bool isLoadingMore;

  const NewsSkeleton({
    super.key,
    this.itemCount = 8,
    this.isLoadingMore = false,
  });

  /// 获取骨架屏颜色
  static Color? getSkeletonColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color.fromRGBO(97, 97, 97, 1)
        : const Color.fromRGBO(224, 224, 224, 1);
  }

  /// 生成骨架屏数据
  List<Map<String, dynamic>> _generateSkeletonItems(int count) {
    return List.generate(
        count,
        (index) => {
              'id': 'skeleton_$index',
              'title': 'Loading Article Title ${index + 1}',
              'description':
                  'This is a loading description for the article that will be replaced with real content when the data loads from the server.',
              'feedsName': 'Loading Feed Name',
              'feedsImg': '',
              'img': '',
              'pubDate': DateTime.now().millisecondsSinceEpoch,
              'createTime': DateTime.now().millisecondsSinceEpoch,
              'creator': 'Loading Author',
              'link': '',
              'isCollect': 0,
              'isLaterRead': 0,
              'isRead': 0,
              'feedsId': 'skeleton_feed_$index',
              'suffixTable': '',
              'isSkeleton': true, // 标记为骨架屏数据
            });
  }

  @override
  Widget build(BuildContext context) {
    final skeletonData = _generateSkeletonItems(itemCount);
    final baseColor = getSkeletonColor(context);

    return Skeletonizer(
      enabled: true,
      effect: ShimmerEffect(
        baseColor: baseColor ?? Colors.grey[300]!,
        highlightColor: const Color.fromRGBO(245, 245, 245, 1),
        duration: const Duration(seconds: 1),
      ),
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 10.spx),
        itemCount: itemCount,
        itemBuilder: (context, index) {
          final article = skeletonData[index];
          final feed = {
            'id': article['feedsId'],
            'feedsName': article['feedsName'],
            'img': article['feedsImg'] ?? '',
            'link': article['link'],
          };

          return ArticleItem(
            article: article,
            feed: feed,
            isDarkMode: Theme.of(context).brightness == Brightness.dark,
            onTap: () {},
            isShowLaterRead: false,
            isShowBookmark: false,
            onReadLater: () {},
            onBookmark: () {},
            onShare: () {},
          );
        },
      ),
    );
  }
}
