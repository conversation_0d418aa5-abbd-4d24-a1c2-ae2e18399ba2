import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/new_widget/news_skeleton.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/new_widget/news_error_state.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/new_widget/news_empty_state.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/new_widget/news_list.dart';

/// 新闻选项卡
class NewsTab extends StatelessWidget {
  final FeedsController controller;

  const NewsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final backgroundColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF1e2020)
        : const Color(0xffeef2f9);

    return SafeArea(
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: backgroundColor,
        child: Column(
          children: [
            Expanded(
              child: Obx(() => _buildContent(context)),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent(BuildContext context) {
    // 首次加载状态
    if (controller.newsLoading.value && controller.newsList.isEmpty) {
      return RefreshIndicator(
        onRefresh: () => controller.getNewsList(refresh: true),
        child: SingleChildScrollView(
          physics: AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height, // 给一个足够的高度以支持下拉刷新
            child: NewsSkeleton(),
          ),
        ),
      );
    }

    // 空状态
    if (controller.newsList.isEmpty) {
      return RefreshIndicator(
        onRefresh: () => controller.getNewsList(refresh: true),
        child: const SingleChildScrollView(
          physics: AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: 600, // 给一个足够的高度以支持下拉刷新
            child: NewsEmptyState(),
          ),
        ),
      );
    }

    // 正常列表状态
    return NewsList(controller: controller);
  }
}
