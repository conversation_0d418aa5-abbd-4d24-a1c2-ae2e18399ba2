import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/new_widget/article_item.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/new_widget/news_skeleton.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/share_bottom_sheet.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

/// 新闻列表组件
class NewsList extends StatelessWidget {
  final FeedsController controller;

  const NewsList({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final articles = controller.newsList;
      final isLoadingMore = controller.newsLoadingMore.value;

      // 计算总项目数
      final totalItemCount =
          articles.length + (isLoadingMore ? 10 : 0); // 加载更多时显示10个骨架屏

      return RefreshIndicator(
        onRefresh: () => controller.getNewsList(refresh: true),
        child: ListView.builder(
          padding: EdgeInsets.symmetric(horizontal: 10.spx),
          controller: controller.scrollController,
          itemCount: totalItemCount,
          itemBuilder: (context, index) {
            return _buildListItem(context, index, articles, isLoadingMore);
          },
        ),
      );
    });
  }

  Widget _buildListItem(
    BuildContext context,
    int index,
    RxList<Map<String, dynamic>> articles,
    bool isLoadingMore,
  ) {
    // 显示实际文章
    if (index < articles.length) {
      final article = articles[index];
      return _buildArticleItem(context, article);
    }

    // 显示加载更多的骨架屏
    if (isLoadingMore &&
        index >= articles.length &&
        index < articles.length + 10) {
      return _buildLoadMoreSkeletonItem(context);
    }

    return const SizedBox.shrink();
  }

  Widget _buildArticleItem(BuildContext context, Map<String, dynamic> article) {
    final feed = {
      'id': article['feedsId'],
      'feedsName': article['feedsName'],
      'img': article['feedsImg'] ?? '',
      'link': article['link'],
    };

    return ArticleItem(
      article: article,
      feed: feed,
      isDarkMode: Theme.of(context).brightness == Brightness.dark,
      onTap: () => _handleArticleTap(article),
      isShowLaterRead: false,
      isShowBookmark: false,
      onReadLater: () => controller.toggleReadLater(article, isShowToast: true),
      onBookmark: () => controller.toggleBookmark(article, isShowToast: true),
      onShare: () => _handleShare(context, article),
    );
  }

  Widget _buildLoadMoreSkeletonItem(BuildContext context) {
    final skeletonArticle = {
      'id': 'skeleton_loading_${DateTime.now().millisecondsSinceEpoch}',
      'title': 'Loading Article Title',
      'description':
          'This is a loading description for the article that will be replaced with real content.',
      'feedsName': 'Loading Feed Name',
      'feedsImg': '',
      'img': '',
      'pubDate': DateTime.now().millisecondsSinceEpoch,
      'createTime': DateTime.now().millisecondsSinceEpoch,
      'creator': 'Loading Author',
      'link': '',
      'isCollect': 0,
      'isLaterRead': 0,
      'isRead': 0,
      'feedsId': 'skeleton_feed',
      'suffixTable': '',
      'isSkeleton': true,
    };

    final feed = {
      'id': skeletonArticle['feedsId'],
      'feedsName': skeletonArticle['feedsName'],
      'img': skeletonArticle['feedsImg'] ?? '',
      'link': skeletonArticle['link'],
    };

    final baseColor = NewsSkeleton.getSkeletonColor(context);

    return Container(
      margin: EdgeInsets.only(bottom: 2.spx),
      child: Skeletonizer(
        enabled: true,
        effect: ShimmerEffect(
          baseColor: baseColor ?? Colors.grey[300]!,
          highlightColor: const Color.fromRGBO(245, 245, 245, 1),
          duration: const Duration(seconds: 1),
        ),
        child: ArticleItem(
          article: skeletonArticle,
          feed: feed,
          isDarkMode: Theme.of(context).brightness == Brightness.dark,
          onTap: () {},
          isShowLaterRead: false,
          isShowBookmark: false,
          onReadLater: () {},
          onBookmark: () {},
          onShare: () {},
        ),
      ),
    );
  }

  void _handleArticleTap(Map<String, dynamic> article) {
    controller.markArticleAsRead(article);
    Get.toNamed(
      Routes.ARTICLE,
      arguments: {'article': article},
    );
  }

  void _handleShare(BuildContext context, Map<String, dynamic> article) {
    final shareData = {
      'name': article['title'] ?? 'Article',
      'link': article['link'] ?? '',
      'description': article['description'] ?? '',
    };
    ShareBottomSheet.show(context, shareData);
  }
}
