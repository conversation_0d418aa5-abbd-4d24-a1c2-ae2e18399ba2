import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/controller/discover_controller.dart';
import 'package:subfeeds/app/presentation/widgets/custom_app_bar.dart';
import 'package:subfeeds/app/presentation/screens/add_feeds/widgets/add_feeds_search_results.dart';
import 'package:subfeeds/app/presentation/screens/add_feeds/add_feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/add_feeds/widgets/search_results_skeleton.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_tab.dart';

/// 发现选项卡
class DiscoverTab extends GetView<DiscoverController> {
  const DiscoverTab({super.key});

  @override
  Widget build(BuildContext context) {
    final appBarBackgroundColor =
        Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1E1F1F)
            : const Color(0xFFeef2f9);
    return Scaffold(
      backgroundColor: appBarBackgroundColor,
      // 使用 CustomAppBar 替换原有自定义顶部栏，确保标题居中
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(50.0),
        child: Obx(() => CustomAppBar(
              defaultIconColor: const Color(0xFF7F8EA7),
              title: 'nav_discover'.tr,
              leftIconPath: 'assets/home/<USER>/feeds.svg',
              onLeftIconPressed: () => _showFeedsBottomSheet(context),
              rightActions: [
                ActionItem(
                    iconPath: 'assets/icons/reload.svg',
                    onPressed: () => controller.refreshData(),
                    isLoading: controller.isLoading.value,
                    iconSize: 16.spx,
                    iconColor: const Color(0xFF7F8EA7)),
              ],
              backgroundColor: appBarBackgroundColor,
              titleStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontSize: 16.spx,
                    fontWeight: FontWeight.w600,
                  ),
              height: 50.0,
              iconSize: 20.spx,
              extendToTopSafeArea: true,
            )),
      ),
      body: Container(
        color: appBarBackgroundColor,
        child: SafeArea(
          top: false, // 因为AppBar已经处理了顶部安全区域
          child: RefreshIndicator(
            onRefresh: () => controller.refreshData(),
            child: CustomScrollView(
              slivers: [
                // 搜索类型选项卡和搜索框
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(16.spx, 16.spx, 16.spx, 0.spx),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 添加Tab栏用于切换搜索类型
                        Obx(() => Row(
                              children: [
                                _buildSearchTypeTab(
                                  context,
                                  'SubFeeds Feeds',
                                  controller.searchType.value == 1,
                                  () => controller.setSearchType(1),
                                ),
                              ],
                            )),
                        SizedBox(height: 16.spx),
                        _buildSearchBar(context),
                        SizedBox(height: 16.spx),
                        _buildCategoryWidget(level: 1),
                        _buildCategoryWidget(level: 2),
                      ],
                    ),
                  ),
                ),
                // 搜索结果
                Obx(() {
                  // 如果正在加载且没有搜索结果，显示骨架屏
                  if (controller.isLoading.value &&
                      controller.searchResults.isEmpty) {
                    return SliverFillRemaining(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 0.spx),
                        child: const SearchResultsSkeleton(),
                      ),
                    );
                  }

                  // 如果有搜索结果或不在加载状态，显示正常的搜索结果
                  return SliverMainAxisGroup(
                    slivers: AddFeedsSearchResults(
                            controller: _DiscoverControllerAdapter(controller))
                        .buildSlivers(context),
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建搜索类型Tab
  Widget _buildSearchTypeTab(
      BuildContext context, String title, bool isActive, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: isActive
                  ? Theme.of(context).brightness == Brightness.light
                      ? Theme.of(context).colorScheme.primary
                      : Colors.white
                  : Theme.of(context).brightness == Brightness.dark
                      ? Colors.white70
                      : Colors.black54,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
              fontFamily: 'Montserrat',
              fontSize: 15,
            ),
          ),
          const SizedBox(height: 4),
          // 底部下划线
          Container(
            height: 2.spx,
            width: 100.spx,
            decoration: BoxDecoration(
              color: isActive
                  ? Theme.of(context).brightness == Brightness.light
                      ? Theme.of(context).colorScheme.primary
                      : Colors.white
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建搜索框
  Widget _buildSearchBar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(28.spx),
      ),
      child: TextField(
        controller: controller.searchController,
        decoration: InputDecoration(
          isDense: true,
          contentPadding:
              EdgeInsets.symmetric(vertical: 12.spx, horizontal: 12.spx),
          filled: true,
          fillColor: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF5B5B5B)
              : const Color(0xFFE5E9F1),
          hintStyle: TextStyle(
            fontSize: 14.spx,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).brightness == Brightness.dark
                ? Color(0XFF909090)
                : Color(0XFFBCC2CC),
          ),
          suffixIcon: Padding(
            padding: EdgeInsets.symmetric(horizontal: 0.spx),
            child: SvgPicture.asset(
              'assets/feeds/feeds_search_weight.svg',
              width: 16.spx,
              height: 16.spx,
              colorFilter: ColorFilter.mode(
                Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF909090)
                    : const Color(0xFFBCC2CC),
                BlendMode.srcIn,
              ),
            ),
          ),
          suffixIconConstraints: BoxConstraints(
            minWidth: 36.spx,
            maxHeight: 36.spx,
          ),
          hintText: 'search_hint'.tr,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(40.spx),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(40.spx),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(40.spx),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.primary,
              width: 1,
            ),
          ),
        ),
        onSubmitted: (value) {
          if (value.isNotEmpty) {
            // 触发搜索
            controller.search(value);
          }
        },
      ),
    );
  }

  // 顶部一级分类
  Widget _buildCategoryWidget({
    required int level,
  }) {
    return Obx(() {
      final categoryList =
          level == 1 ? controller.categories : controller.subCategories;
      final currentCategoryId =
          level == 1 ? controller.categoryId : controller.subCategoryId;
      if (categoryList.isNotEmpty) {
        return Container(
          margin: EdgeInsets.only(bottom: 16.spx),
          height: 36,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: categoryList.length,
            itemBuilder: (context, index) {
              final category = categoryList[index];

              return Obx(() {
                final isSelected = currentCategoryId.value == category['id'];

                return GestureDetector(
                  onTap: () {
                    if (level == 1) {
                      controller.switchCategory(category['id']);
                    } else {
                      controller.switchSubCategory(category['id']);
                    }
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 100),
                    curve: Curves.easeInOut,
                    padding: EdgeInsets.symmetric(
                        horizontal: 9.spx, vertical: 8.spx),
                    margin: EdgeInsets.only(right: 8.spx),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFF3A3A3A)
                              : Theme.of(context).colorScheme.primary
                          : Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFF555555)
                              : const Color(0xFFE6E6E6),
                      borderRadius: BorderRadius.circular(10.spx),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: Theme.of(context)
                                    .colorScheme
                                    .primary
                                    .withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              )
                            ]
                          : null,
                    ),
                    child: AnimatedDefaultTextStyle(
                      duration: const Duration(milliseconds: 300),
                      style: TextStyle(
                        color: isSelected
                            ? Colors.white
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFFD1D1D1)
                                : const Color(0xFF999999),
                        fontWeight: FontWeight.normal,
                        fontFamily: 'Montserrat',
                        fontSize: 12.spx,
                      ),
                      child: Text(
                        // 显示中文名称，如果没有则显示英文名称
                        controller.isEnglish.value
                            ? (category['name'] ?? category['nameCn'] ?? '')
                            : (category['nameCn'] ?? category['name'] ?? ''),
                      ),
                    ),
                  ),
                );
              });
            },
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  /// 显示FeedsListTab作为底部弹窗
  void _showFeedsBottomSheet(BuildContext context) {
    // 确保FeedsController可用
    FeedsController feedsController;
    if (!Get.isRegistered<FeedsController>()) {
      // 如果FeedsController还没有被注册，则创建一个新的实例
      feedsController = Get.put(FeedsController());
    } else {
      // 如果已经注册，则获取现有实例
      feedsController = Get.find<FeedsController>();
    }

    // 刷新数据
    feedsController.refreshFeeds();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF444444)
          : const Color(0xFFf7faff),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.9, // 增加高度为屏幕高度的90%
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部拖动条
              Center(
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40.spx,
                  height: 4.spx,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // FeedsListTab内容
              Expanded(
                child: FeedsListTab(controller: feedsController),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// 适配器类，将DiscoverController适配为AddFeedsController接口
class _DiscoverControllerAdapter extends AddFeedsController {
  final DiscoverController _discoverController;

  _DiscoverControllerAdapter(this._discoverController);

  @override
  RxList<Map<String, dynamic>> get searchResults =>
      _discoverController.searchResults;

  @override
  RxInt get totalResults => _discoverController.totalResults;

  @override
  RxBool get isLoadingMore => _discoverController.isLoadingMore;

  @override
  RxBool get hasLoadedAll => _discoverController.hasLoadedAll;

  @override
  Future<void> loadMore() => _discoverController.loadMore();

  @override
  Future<void> subscribeFeed(Map<String, dynamic> feed) async {
    await _discoverController.subscribeFeed(feed);
  }

  @override
  Future<void> unsubscribeFeed(Map<String, dynamic> feed) async {
    await _discoverController.unsubscribeFeed(feed);
  }

  // 其他不需要的方法返回默认值或空实现
  @override
  Future<void> refresh() async {}
}
