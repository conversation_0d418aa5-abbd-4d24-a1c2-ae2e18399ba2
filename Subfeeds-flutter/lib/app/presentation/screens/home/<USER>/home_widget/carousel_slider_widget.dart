import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/home_utils.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/carousel_skeleton.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';

class CarouselSliderWidget extends StatefulWidget {
  final RxList<Map<String, dynamic>> articles;
  final bool isLoading;

  const CarouselSliderWidget({
    Key? key,
    required this.articles,
    required this.isLoading,
  }) : super(key: key);

  @override
  State<CarouselSliderWidget> createState() => _CarouselSliderWidgetState();
}

class _CarouselSliderWidgetState extends State<CarouselSliderWidget> {
  // 当前轮播索引
  final RxInt _currentCarouselIndex = 0.obs;
  // 记录图片加载失败的状态
  final Map<String, bool> _imageLoadFailedMap = {};

  @override
  Widget build(BuildContext context) {
    // 使用Obx监听articles列表变化
    return Obx(() {
      // 如果没有轮播图数据或者正在加载，显示骨架屏
      if (widget.articles.isEmpty) {
        if (widget.isLoading) {
          return const CarouselSkeleton();
        }
        // 如果没有数据且不在加载中，则不显示轮播图区域
        return const SizedBox.shrink();
      }

      return Container(
        padding: EdgeInsets.symmetric(vertical: 11.spx),
        color: Colors.transparent,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 轮播图
            CarouselSlider(
              options: CarouselOptions(
                height: 180.spx,
                aspectRatio: 1 / 1,
                viewportFraction: 1,
                initialPage: 0,
                enableInfiniteScroll: widget.articles.length > 1,
                reverse: false,
                autoPlay: widget.articles.length > 1,
                autoPlayInterval: const Duration(seconds: 5),
                autoPlayAnimationDuration: const Duration(milliseconds: 100),
                autoPlayCurve: Curves.fastOutSlowIn,
                enlargeCenterPage: true,
                enlargeFactor: 0.15,
                onPageChanged: (index, reason) {
                  _currentCarouselIndex.value = index;
                },
                scrollDirection: Axis.horizontal,
              ),
              items: widget.articles.map((article) {
                return Builder(
                  builder: (BuildContext context) {
                    return _buildSlideItem(context, article);
                  },
                );
              }).toList(),
            ),
            // 轮播指示器
            if (widget.articles.length > 1)
              _buildCarouselIndicator(
                  context, widget.articles, _currentCarouselIndex),
          ],
        ),
      );
    });
  }

  /// 构建轮播图项
  Widget _buildSlideItem(BuildContext context, Map<String, dynamic> article) {
    final articleId = article['id']?.toString() ?? 'slide_${article.hashCode}';
    final hasImage = article['img'] != null &&
        article['img'].toString().isNotEmpty &&
        !(_imageLoadFailedMap[articleId] ?? false);
    final hasFeedsImage = article['feedsImg'] != null &&
        article['feedsImg'].toString().isNotEmpty;
    final imageUrl = hasImage ? article['img'] : null;
    final feedsImageUrl = hasFeedsImage ? article['feedsImg'] : null;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 14.spx),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.spx),
        color: Colors.transparent,
      ),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            height: 121.spx,
            child: InkWell(
              onTap: () {
                // 点击轮播图项跳转到文章详情页
                Get.toNamed(
                  Routes.ARTICLE,
                  arguments: {
                    'article': {...article, 'img': imageUrl},
                    'isHome': true,
                  },
                );
              },
              borderRadius: BorderRadius.circular(12.spx),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 左侧图片 - 只有在有有效图片时才显示
                  if (hasImage) ...[
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12.spx),
                      child: SizedBox(
                        width: 121.spx,
                        height: 121.spx,
                        child: CachedNetworkImage(
                          imageUrl: imageUrl!,
                          fit: BoxFit.cover,
                          cacheKey: imageUrl, // 缓存key 用于清除缓存
                          placeholder: (context, url) => Container(
                            width: 121.spx,
                            height: 121.spx,
                            decoration: BoxDecoration(
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFF2A2A2A)
                                  : const Color(0xFFF5F5F5),
                              borderRadius: BorderRadius.circular(12.spx),
                            ),
                            child: Center(
                              child: LoadingIndicator(size: 90.spx),
                            ),
                          ),
                          errorWidget: (context, url, error) {
                            debugPrint('error IMG LOADER: $error');
                            // 图片加载失败时设置状态并重新构建
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (mounted) {
                                setState(() {
                                  _imageLoadFailedMap[articleId] = true;
                                });
                              }
                            });
                            // 返回完全折叠的空部件
                            return const SizedBox.shrink();
                          },
                        ),
                      ),
                    ),
                    SizedBox(width: 12.spx),
                  ],
                  // 右侧内容
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 标题
                        Text(
                          article['title'] ?? 'No Title',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 13.spx,
                            fontWeight: FontWeight.w600,
                            color: HomeColors.getTitleTextColor(context),
                          ),
                        ),
                        SizedBox(height: 6.spx),
                        // 描述
                        Text(
                          HomeUtils.stripHtmlTags(
                              article['description'] ?? 'No description'),
                          maxLines: 4,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 12.spx,
                            fontWeight: FontWeight.w500,
                            color: HomeColors.getDescriptionTextColor(context),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 22.spx),
          // 底部布局：左侧为feedsImg + feedsName，右侧为pubDate
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 左侧：feedsImg + feedsName
              Expanded(
                child: Row(
                  children: [
                    // feedsImg
                    ClipOval(
                      child: Container(
                        padding: EdgeInsets.all(1.spx),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.spx),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        width: 16.spx,
                        height: 16.spx,
                        child: hasFeedsImage
                            ? CachedNetworkImage(
                                imageUrl: feedsImageUrl,
                                fit: BoxFit.cover,
                                errorWidget: (context, url, error) =>
                                    SvgPicture.asset(
                                  'assets/feeds/feeds_logo.svg',
                                  width: 16.spx,
                                  height: 16.spx,
                                ),
                              )
                            : SvgPicture.asset(
                                'assets/feeds/feeds_logo.svg',
                                width: 16.spx,
                                height: 16.spx,
                              ),
                      ),
                    ),
                    SizedBox(width: 6.spx),
                    // feedsName
                    Expanded(
                      child: Text(
                        article['feedsName'] ?? 'Unknown',
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 12.spx,
                          color: HomeColors.getDescriptionTextColor(context),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 右侧：pubDate
              Text(
                HomeUtils.formatTime(article['pubDate']),
                style: TextStyle(
                  fontSize: 10.spx,
                  color: HomeColors.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建轮播指示器
  Widget _buildCarouselIndicator(BuildContext context,
      List<Map<String, dynamic>> articles, RxInt currentIndex) {
    return Padding(
      padding: EdgeInsets.only(top: 8.spx),
      child: Obx(() => Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: articles.asMap().entries.map((entry) {
              return Container(
                width: currentIndex.value == entry.key ? 16.spx : 8.spx,
                height: 4.spx,
                margin: EdgeInsets.symmetric(horizontal: 2.spx),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(2.spx)),
                  color: currentIndex.value == entry.key
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey.withOpacity(0.5),
                ),
              );
            }).toList(),
          )),
    );
  }
}
