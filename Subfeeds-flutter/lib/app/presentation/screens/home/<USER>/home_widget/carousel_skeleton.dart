import 'package:flutter/material.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/shimmer_painter.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/home_utils.dart';

/// 轮播图骨架屏组件
class CarouselSkeleton extends StatefulWidget {
  const CarouselSkeleton({Key? key}) : super(key: key);

  @override
  State<CarouselSkeleton> createState() => _CarouselSkeletonState();
}

class _CarouselSkeletonState extends State<CarouselSkeleton>
    with SingleTickerProviderStateMixin {
  // 动画控制器
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    );
    // 启动并循环播放动画
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final skeletonColor = HomeColors.getSkeletonColor(context);

    return Container(
      padding: EdgeInsets.symmetric(vertical: 11.spx),
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 轮播图骨架
          Container(
            margin: EdgeInsets.symmetric(horizontal: 14.spx),
            height: 180.spx,
            child: Column(
              children: [
                // 主内容区域
                Container(
                  width: double.infinity,
                  height: 121.spx,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 左侧图片骨架
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12.spx),
                        child: Container(
                          width: 121.spx,
                          height: 121.spx,
                          decoration: BoxDecoration(
                            color: skeletonColor,
                            borderRadius: BorderRadius.circular(12.spx),
                          ),
                          child: CustomPaint(
                            painter: ShimmerPainter(
                              value: _animationController.value,
                              color: skeletonColor!,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12.spx),
                      // 右侧内容骨架
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // 标题骨架
                            Container(
                              height: 13.spx,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: skeletonColor,
                                borderRadius: BorderRadius.circular(4.spx),
                              ),
                            ),
                            SizedBox(height: 8.spx),
                            Container(
                              height: 13.spx,
                              width: MediaQuery.of(context).size.width * 0.5,
                              decoration: BoxDecoration(
                                color: skeletonColor,
                                borderRadius: BorderRadius.circular(4.spx),
                              ),
                            ),
                            SizedBox(height: 10.spx),
                            // 描述骨架
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(
                                3,
                                (index) => Container(
                                  margin: EdgeInsets.only(bottom: 6.spx),
                                  height: 12.spx,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: skeletonColor,
                                    borderRadius: BorderRadius.circular(4.spx),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 22.spx),
                // 底部信息骨架
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 左侧：feedsImg + feedsName
                    Expanded(
                      child: Row(
                        children: [
                          // feedsImg骨架
                          ClipOval(
                            child: Container(
                              width: 16.spx,
                              height: 16.spx,
                              decoration: BoxDecoration(
                                color: skeletonColor,
                                borderRadius: BorderRadius.circular(16.spx),
                              ),
                            ),
                          ),
                          SizedBox(width: 6.spx),
                          // feedsName骨架
                          Container(
                            height: 12.spx,
                            width: 80.spx,
                            decoration: BoxDecoration(
                              color: skeletonColor,
                              borderRadius: BorderRadius.circular(4.spx),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 右侧：pubDate骨架
                    Container(
                      height: 10.spx,
                      width: 50.spx,
                      decoration: BoxDecoration(
                        color: skeletonColor,
                        borderRadius: BorderRadius.circular(4.spx),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 轮播指示器骨架
          Padding(
            padding: EdgeInsets.only(top: 8.spx),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                3,
                (index) => Container(
                  width: index == 0 ? 16.spx : 8.spx,
                  height: 4.spx,
                  margin: EdgeInsets.symmetric(horizontal: 2.spx),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(2.spx)),
                    color: index == 0
                        ? Theme.of(context).colorScheme.primary
                        : Colors.grey.withOpacity(0.5),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
