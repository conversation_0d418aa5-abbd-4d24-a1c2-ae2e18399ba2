import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/home_utils.dart';

/// 空状态组件
class EmptyState extends StatelessWidget {
  final String searchText;
  final Function() onRetry;
  const EmptyState({
    Key? key,
    required this.searchText,
    required this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(32.spx),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 空状态图片
          Image.asset(
            'assets/images/search-empty.png',
            width: 200.spx,
            height: 200.spx,
          ),
          SizedBox(height: 16.spx),
          // 空状态文本
          Text(
            searchText.isEmpty
                ? 'No articles found'.tr
                : 'No matching articles found'.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16.spx,
              fontWeight: FontWeight.w500,
              color: HomeColors.getDescriptionTextColor(context),
            ),
          ),
          SizedBox(height: 8.spx),
          // 提示文本
          Text(
            searchText.isEmpty
                ? 'Try refreshing or check back later'.tr
                : 'Try with different keywords'.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14.spx,
              color: HomeColors.getSecondaryTextColor(context),
            ),
          ),
          // 重试按钮
          SizedBox(height: 16.spx),
          ElevatedButton(
            onPressed: () {
              onRetry();
            },
            child: Text('Retry'.tr),
          ),
        ],
      ),
    );
  }
}
