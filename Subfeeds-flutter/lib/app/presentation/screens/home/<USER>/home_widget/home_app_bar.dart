import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/controllers/theme_controller.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/home_utils.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_tab.dart';

/// 首页自定义AppBar组件
class HomeAppBar extends StatelessWidget {
  final ThemeController themeController;
  final UserController userController;
  final HomeController homeController;

  const HomeAppBar({
    Key? key,
    required this.themeController,
    required this.userController,
    required this.homeController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取顶部安全区域高度
    final topPadding = MediaQuery.of(context).padding.top;
    // 获取图标颜色
    final iconColor = HomeColors.getIconColor(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: topPadding),
      decoration: BoxDecoration(
        color: HomeColors.getBackgroundColor(context),
      ),
      child: Padding(
        padding: EdgeInsets.only(
            left: 12.spx, right: 12.spx, top: 8.spx, bottom: 14.spx),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 左侧布局：标题和操作按钮
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题 "Hey, User"
                Text(
                  "Hey, ${_getUserDisplayName()}",
                  style: TextStyle(
                    fontSize: 24.spx,
                    fontWeight: FontWeight.w600,
                    color: HomeColors.getTitleTextColor(context),
                  ),
                ),
                SizedBox(height: 7.spx),
                // 操作按钮行
                Row(
                  children: [
                    // 主题切换按钮
                    Obx(() => _buildThemeToggleButton(
                          context,
                          themeController.isDarkMode.value
                              ? 'assets/home/<USER>/light.svg'
                              : 'assets/home/<USER>/dark.svg',
                          iconColor,
                        )),
                    SizedBox(width: 16.spx),
                    // Feeds按钮
                    Obx(() => _buildActionButton(
                          context,
                          'assets/home/<USER>/feeds.svg',
                          () => _showFeedsBottomSheet(context),
                          iconColor,
                          isLoading: homeController.isRefreshing.value,
                        )),
                    SizedBox(width: 16.spx),
                  ],
                ),
              ],
            ),
            // 右侧：用户头像
            _buildUserAvatar(context),
          ],
        ),
      ),
    );
  }

  /// 显示FeedsListTab作为底部弹窗
  void _showFeedsBottomSheet(BuildContext context) {
    // 确保FeedsController可用
    FeedsController feedsController;
    if (!Get.isRegistered<FeedsController>()) {
      // 如果FeedsController还没有被注册，则创建一个新的实例
      feedsController = Get.put(FeedsController());
    } else {
      // 如果已经注册，则获取现有实例
      feedsController = Get.find<FeedsController>();
    }

    // 刷新数据
    feedsController.refreshFeeds();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF444444)
          : const Color(0xFFf7faff),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.9, // 增加高度为屏幕高度的90%
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部拖动条
              Center(
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40.spx,
                  height: 4.spx,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // FeedsListTab内容
              Expanded(
                child: FeedsListTab(controller: feedsController),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建主题切换按钮（带位置感知的过渡效果和触觉反馈）
  Widget _buildThemeToggleButton(
    BuildContext context,
    String iconPath,
    Color iconColor,
  ) {
    return Builder(
      builder: (buttonContext) {
        return InkWell(
          onTap: () => _handleThemeToggle(buttonContext),
          borderRadius: BorderRadius.circular(8.spx),
          child: Container(
            padding: EdgeInsets.all(9.5.spx),
            width: 42.spx,
            height: 42.spx,
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF575757)
                  : const Color(0xFFFFFFFF),
              borderRadius: BorderRadius.circular(42.spx),
            ),
            child: SvgPicture.asset(
              iconPath,
              colorFilter: ColorFilter.mode(
                iconColor,
                BlendMode.srcIn,
              ),
            ),
          ),
        );
      },
    );
  }

  /// 处理主题切换 - 带位置感知的涟漪动画和触觉反馈
  void _handleThemeToggle(BuildContext buttonContext) {
    try {
      debugPrint('开始切换主题');

      // 添加重度冲击触觉反馈
      HapticFeedback.heavyImpact();

      // 获取按钮的渲染盒子，用于计算动画起始位置
      final RenderBox? buttonRenderBox =
          buttonContext.findRenderObject() as RenderBox?;

      if (buttonRenderBox != null) {
        // 计算按钮中心位置作为动画起始点
        final buttonSize = buttonRenderBox.size;
        final buttonCenter =
            Offset(buttonSize.width / 2, buttonSize.height / 2);
        final globalPosition = buttonRenderBox.localToGlobal(buttonCenter);
        // 使用位置感知的主题切换
        themeController.toggleThemeModeFromGlobalPosition(globalPosition);
      } else {
        // 如果无法获取位置，使用普通切换
        themeController.toggleThemeMode();
      }

      debugPrint('主题切换完成');
    } catch (e) {
      debugPrint('主题切换失败: $e');
      // 如果出错，使用普通切换作为后备
      themeController.toggleThemeMode();
    }
  }

  /// 构建操作按钮
  Widget _buildActionButton(
    BuildContext context,
    String iconPath,
    VoidCallback onPressed,
    Color iconColor, {
    bool isLoading = false,
  }) {
    return InkWell(
      onTap: isLoading ? null : onPressed,
      borderRadius: BorderRadius.circular(8.spx),
      child: Container(
        padding: EdgeInsets.all(9.5.spx),
        width: 42.spx,
        height: 42.spx,
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF575757)
              : const Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(42.spx),
        ),
        child: isLoading
            ? SizedBox(
                width: 20.spx,
                height: 20.spx,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
              )
            : SvgPicture.asset(
                iconPath,
                colorFilter: ColorFilter.mode(
                  iconColor,
                  BlendMode.srcIn,
                ),
              ),
      ),
    );
  }

  /// 构建用户头像
  Widget _buildUserAvatar(BuildContext context) {
    return Obx(() {
      final user = userController.user.value;
      final hasAvatar = user?.avatar != null && user!.avatar!.isNotEmpty;

      return GestureDetector(
        onTap: () => homeController.changePage(3),
        child: Container(
          width: 66.spx,
          height: 66.spx,
          child: ClipOval(
            child: hasAvatar
                ? CachedNetworkImage(
                    imageUrl: user!.avatar!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Image.asset(
                      'assets/images/default_avatar.png',
                      fit: BoxFit.cover,
                    ),
                    errorWidget: (context, error, stackTrace) => Image.asset(
                      'assets/images/default_avatar.png',
                      fit: BoxFit.cover,
                    ),
                  )
                : Image.asset(
                    'assets/images/default_avatar.png',
                    fit: BoxFit.cover,
                  ),
          ),
        ),
      );
    });
  }

  /// 获取用户显示名称
  String _getUserDisplayName() {
    final user = userController.user.value;
    if (user == null) {
      return 'please_login'.tr;
    }
    return user.name ?? user.email ?? 'User';
  }
}
