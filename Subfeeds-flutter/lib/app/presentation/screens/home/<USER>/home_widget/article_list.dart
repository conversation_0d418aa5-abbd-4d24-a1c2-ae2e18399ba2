import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/home_utils.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/empty_state.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/article_skeleton.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';

/// 文章列表组件
class ArticleList extends StatefulWidget {
  final RxList<Map<String, dynamic>> articles;
  final bool isLoading;
  final HomeController homeController;
  final String searchText;

  const ArticleList({
    Key? key,
    required this.articles,
    required this.isLoading,
    required this.homeController,
    required this.searchText,
  }) : super(key: key);

  @override
  State<ArticleList> createState() => _ArticleListState();
}

class _ArticleListState extends State<ArticleList> {
  // 记录图片加载失败的状态
  final Map<String, bool> _imageLoadFailedMap = {};

  @override
  Widget build(BuildContext context) {
    // 使用Obx监听articles列表变化
    return Obx(() {
      // 判断是否为加载状态
      if (widget.isLoading && widget.articles.isEmpty) {
        return const ArticleSkeleton();
      }

      // 构建列表容器
      return Container(
        width: double.infinity,
        // 添加一些底部内边距，确保内容不会被底部导航栏遮挡

        child: widget.articles.isEmpty
            ? EmptyState(
                searchText: widget.searchText,
                onRetry: () {
                  widget.homeController.refreshAllData();
                },
              )
            : _buildArticlesList(context),
      );
    });
  }

  /// 构建普通文章列表
  Widget _buildArticlesList(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: widget.articles.length,
      itemBuilder: (context, index) => _buildArticleListItem(context, index),
      padding: EdgeInsets.zero,
    );
  }

  /// 构建文章列表项
  Widget _buildArticleListItem(BuildContext context, int index) {
    final article = widget.articles[index];
    final articleId = article['id']?.toString() ?? 'article_$index';
    final hasImage = article['img'] != null &&
        article['img'].toString().isNotEmpty &&
        !(_imageLoadFailedMap[articleId] ?? false);
    final hasFeedsImage = article['feedsImg'] != null &&
        article['feedsImg'].toString().isNotEmpty;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 6.spx, vertical: 4.spx),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF565656)
            : Colors.white,
        borderRadius: BorderRadius.circular(12.spx),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF2A2D3E)
              : const Color(0xFFd5d5d5),
          width: 0.5.spx,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          widget.homeController
              .setRead(index, article['feedsId'], article['suffixTable']);
          Get.toNamed(
            Routes.ARTICLE,
            arguments: {
              'article': {
                ...article,
                'img': article['feedsImg'] ?? article['img']
              },
              'isHome': true,
            },
          );
        },
        borderRadius: BorderRadius.circular(16.spx),
        child: Padding(
          padding: EdgeInsets.all(10.spx),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部布局：左侧为feedsImg + feedsName，右侧为pubDate
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 左侧：feedsImg + feedsName
                  Expanded(
                    child: Row(
                      children: [
                        // feedsImg
                        Container(
                          width: 16.spx,
                          height: 16.spx,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.spx),
                            border: Border.all(
                                color: Theme.of(context).colorScheme.primary),
                          ),
                          child: ClipOval(
                            child: SizedBox(
                              width: 16.spx,
                              height: 16.spx,
                              child: hasFeedsImage
                                  ? CachedNetworkImage(
                                      imageUrl: article['feedsImg'],
                                      fit: BoxFit.cover,
                                      errorWidget: (context, url, error) =>
                                          SvgPicture.asset(
                                        'assets/feeds/feeds_logo.svg',
                                        width: 16.spx,
                                        height: 16.spx,
                                      ),
                                    )
                                  : SvgPicture.asset(
                                      'assets/feeds/feeds_logo.svg',
                                      width: 16.spx,
                                      height: 16.spx,
                                    ),
                            ),
                          ),
                        ),
                        SizedBox(width: 6.spx),
                        // feedsName
                        Expanded(
                          child: Text(
                            article['feedsName'] ?? 'Unknown',
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 12.spx,
                              color:
                                  HomeColors.getDescriptionTextColor(context),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 右侧：pubDate
                  Text(
                    HomeUtils.formatTime(article['pubDate']),
                    style: TextStyle(
                      fontSize: 10.spx,
                      color: HomeColors.getSecondaryTextColor(context),
                    ),
                  ),
                ],
              ),
              // 分隔线

              SizedBox(height: 6.spx),
              // 内容区域
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 左侧图片 - 只有在有有效图片时才显示
                  if (hasImage) ...[
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.spx),
                      child: SizedBox(
                        width: 57.spx,
                        height: 57.spx,
                        child: CachedNetworkImage(
                          imageUrl: article['img'],
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            width: 57.spx,
                            height: 57.spx,
                            decoration: BoxDecoration(
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFF2A2A2A)
                                  : const Color(0xFFF5F5F5),
                              borderRadius: BorderRadius.circular(8.spx),
                            ),
                            child: Center(
                              child: LoadingIndicator(size: 50.spx),
                            ),
                          ),
                          errorWidget: (context, url, error) {
                            // 图片加载失败时设置状态并重新构建
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (mounted) {
                                setState(() {
                                  _imageLoadFailedMap[articleId] = true;
                                });
                              }
                            });
                            // 返回完全折叠的空部件
                            return const SizedBox.shrink();
                          },
                        ),
                      ),
                    ),
                    SizedBox(width: 6.spx),
                  ],
                  // 右侧内容
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 标题
                        Text(
                          article['title'] ?? 'No Title',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 13.spx,
                            fontWeight: FontWeight.w600,
                            color: HomeColors.getTitleTextColor(context),
                          ),
                        ),
                        SizedBox(height: 6.spx),
                        // 描述
                        Text(
                          HomeUtils.stripHtmlTags(
                              article['description'] ?? 'No description'),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 12.spx,
                            color: HomeColors.getDescriptionTextColor(context),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
