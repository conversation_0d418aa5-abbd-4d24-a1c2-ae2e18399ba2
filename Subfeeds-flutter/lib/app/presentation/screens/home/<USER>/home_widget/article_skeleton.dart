import 'package:flutter/material.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/shimmer_painter.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/home_widget/home_utils.dart';

/// 文章列表骨架屏组件
class ArticleSkeleton extends StatefulWidget {
  const ArticleSkeleton({Key? key}) : super(key: key);

  @override
  State<ArticleSkeleton> createState() => _ArticleSkeletonState();
}

class _ArticleSkeletonState extends State<ArticleSkeleton>
    with SingleTickerProviderStateMixin {
  // 动画控制器
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    );
    // 启动并循环播放动画
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final skeletonColor = HomeColors.getSkeletonColor(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(bottom: 80.spx),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题骨架
          Padding(
            padding: EdgeInsets.fromLTRB(16.spx, 16.spx, 16.spx, 8.spx),
            child: Container(
              width: 150.spx,
              height: 24.spx,
              decoration: BoxDecoration(
                color: skeletonColor,
                borderRadius: BorderRadius.circular(4.spx),
              ),
            ),
          ),
          // 骨架列表项
          ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: 6, // 显示6个骨架项
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) => _buildSkeletonListItem(context),
          ),
        ],
      ),
    );
  }

  /// 构建骨架列表项
  Widget _buildSkeletonListItem(BuildContext context) {
    final skeletonColor = HomeColors.getSkeletonColor(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.spx),
      padding: EdgeInsets.all(12.spx),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF444444) : const Color(0xFFf7faff),
        borderRadius: BorderRadius.circular(12.spx),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 图片骨架
          Container(
            width: 100.spx,
            height: 80.spx,
            decoration: BoxDecoration(
              color: skeletonColor,
              borderRadius: BorderRadius.circular(8.spx),
            ),
            child: CustomPaint(
              painter: ShimmerPainter(
                value: _animationController.value,
                color: skeletonColor!,
              ),
            ),
          ),
          SizedBox(width: 12.spx),
          // 内容骨架
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题骨架
                Container(
                  height: 16.spx,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: skeletonColor,
                    borderRadius: BorderRadius.circular(4.spx),
                  ),
                ),
                SizedBox(height: 8.spx),
                Container(
                  height: 16.spx,
                  width: MediaQuery.of(context).size.width * 0.6,
                  decoration: BoxDecoration(
                    color: skeletonColor,
                    borderRadius: BorderRadius.circular(4.spx),
                  ),
                ),
                SizedBox(height: 12.spx),
                // 底部信息骨架
                Row(
                  children: [
                    Container(
                      height: 12.spx,
                      width: 80.spx,
                      decoration: BoxDecoration(
                        color: skeletonColor,
                        borderRadius: BorderRadius.circular(4.spx),
                      ),
                    ),
                    Spacer(),
                    Container(
                      height: 12.spx,
                      width: 50.spx,
                      decoration: BoxDecoration(
                        color: skeletonColor,
                        borderRadius: BorderRadius.circular(4.spx),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
