import 'package:flutter/material.dart';

/// 首页工具类和常量
class HomeUtils {
  /// 移除HTML标签
  static String stripHtmlTags(String htmlText) {
    RegExp exp = RegExp(r"<[^>]*>", multiLine: true, caseSensitive: true);
    String strippedText = htmlText.replaceAll(exp, '');
    return strippedText.replaceAll('&nbsp;', ' ');
  }

  /// 格式化时间
  static String formatTime(dynamic pubDate) {
    if (pubDate == null) return 'Unknown';
    try {
      final DateTime date = DateTime.parse(pubDate.toString());
      final DateTime now = DateTime.now();
      final Duration difference = now.difference(date);

      if (difference.inDays > 0) {
        return '${difference.inDays}d ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}h ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}m ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return 'Unknown';
    }
  }
}

/// 首页主题颜色
class HomeColors {
  /// 获取背景颜色
  static Color getBackgroundColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF1e2020)
        : const Color(0xffeef2f9);
  }

  /// 获取卡片颜色
  static Color getCardColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF3b3b3b)
        : const Color(0xffffffff);
  }

  /// 获取图标颜色
  static Color getIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFFe2edfe)
        : const Color(0xFF808ea7);
  }

  /// 获取骨架屏颜色
  static Color? getSkeletonColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[700]
        : Colors.grey[300];
  }

  /// 获取标题文本颜色
  static Color getTitleTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.white
        : Colors.black87;
  }

  /// 获取描述文本颜色
  static Color? getDescriptionTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[400]
        : Colors.grey[600];
  }

  /// 获取次要文本颜色
  static Color? getSecondaryTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[500]
        : Colors.grey[700];
  }
}
