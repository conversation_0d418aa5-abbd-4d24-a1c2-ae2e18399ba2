import 'dart:math';
import 'package:flutter/material.dart';

/// 骨架屏绘制器
class ShimmerPainter extends CustomPainter {
  final double value;
  final Color color;

  ShimmerPainter({required this.value, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    // 创建一个从左上角到右下角的渐变
    final Paint paint = Paint();

    // 计算对角线长度
    final double diagonal =
        sqrt(size.width * size.width + size.height * size.height);

    // 计算高亮位置 (-0.5 到 1.5的范围，这样高亮可以完全移出视图)
    final double shimmerPos = value * 2.0 - 0.5;

    // 高亮宽度占对角线的比例
    const double shimmerWidth = 0.4;

    // 创建线性渐变
    final Gradient gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        color,
        color.withOpacity(0.5),
        Colors.white.withOpacity(0.5),
        color.withOpacity(0.5),
        color,
      ],
      stops: [
        max(0.0, shimmerPos - shimmerWidth),
        max(0.0, shimmerPos - shimmerWidth / 2),
        shimmerPos,
        min(1.0, shimmerPos + shimmerWidth / 2),
        min(1.0, shimmerPos + shimmerWidth),
      ],
    );

    paint.shader = gradient.createShader(Offset.zero & size);

    // 绘制整个区域
    canvas.drawRect(Offset.zero & size, paint);
  }

  @override
  bool shouldRepaint(ShimmerPainter oldDelegate) {
    return oldDelegate.value != value || oldDelegate.color != color;
  }
}
