import 'package:get/get.dart';
import 'package:subfeeds/app/data/repositories/feeds_repository.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class DiscoverController extends GetxController {
  final ArticleRepository _feedsRepository = ArticleRepository();

  // 所有分类数据
  final RxList<Map<String, dynamic>> allCategories =
      <Map<String, dynamic>>[].obs;

  // 一级分类列表
  final RxList<Map<String, dynamic>> categories = <Map<String, dynamic>>[].obs;
  // 当前一级分类id
  final RxString currentCategoryId = '0'.obs;
  // 获取语言设置
  final RxBool isEnglish = false.obs;

  // 搜索类型 (1: SubFeeds Feeds, 100: Feeds Article)
  final RxInt searchType = 1.obs;

  // 推荐源列表
  final RxList<Map<String, dynamic>> recommendFeeds =
      <Map<String, dynamic>>[].obs;

  // 加载状态
  final RxBool isLoading = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;

  // 订阅状态
  final RxMap<String, bool> subscribedFeeds = <String, bool>{}.obs;

  // 二级分类列表
  final RxList<Map<String, dynamic>> subCategories =
      <Map<String, dynamic>>[].obs;
  // 当前一级分类id
  final RxString categoryId = '1'.obs;
  // 当前二级分类id
  final RxString subCategoryId = '1'.obs;

  final searchHistory = <String>[].obs;

  // 搜索框控制器
  late TextEditingController searchController;

  // 搜索结果相关属性（兼容AddFeedsSearchResults组件）
  final RxList<Map<String, dynamic>> searchResults =
      <Map<String, dynamic>>[].obs;
  final RxInt totalResults = 0.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool hasLoadedAll = false.obs;
  final RxBool hasMoreData = true.obs;
  final RxString currentSearchQuery = ''.obs;

  // 分页信息
  int _currentPage = 1;
  final int _pageSize = 10;

  @override
  void onInit() {
    super.onInit();
    searchController = TextEditingController();
    // 监听搜索框文本变化
    searchController.addListener(_onSearchTextChanged);
    loadSearchHistory();
    _initializeData();
  }

  /// 搜索框文本变化监听
  void _onSearchTextChanged() {
    final text = searchController.text.trim();
    // 如果搜索框为空，清空当前搜索关键词
    if (text.isEmpty) {
      currentSearchQuery.value = '';
    }
  }

  /// 初始化数据
  Future<void> _initializeData() async {
    await loadData();
    // 数据加载完成后，初始化分类和执行搜索
    categoryId.value = 'all';
    await loadSubCategories();
    subCategoryId.value = 'all';
    await performCategorySearch();
  }

  @override
  void onClose() {
    searchController.removeListener(_onSearchTextChanged);
    searchController.dispose();
    super.onClose();
  }

  // 切换一级分类
  void switchCategory(String categoryIds) {
    if (categoryId.value != categoryIds) {
      categoryId.value = categoryIds;
      loadSubCategories();
      // 切换一级分类时，重置二级分类为"All"并执行搜索
      subCategoryId.value = 'all';
      performCategorySearch();
    }
  }

  // 切换二级分类
  void switchSubCategory(String subCategoryIds) {
    if (subCategoryId.value != subCategoryIds) {
      subCategoryId.value = subCategoryIds;
      // 切换二级分类时执行搜索
      performCategorySearch();
    }
  }

  // 执行分类搜索
  Future<void> performCategorySearch() async {
    _currentPage = 1;
    hasLoadedAll.value = false;
    hasMoreData.value = true;
    searchResults.clear();
    totalResults.value = 0;

    await _searchByCategory();
  }

  // 根据分类搜索
  Future<void> _searchByCategory() async {
    if (_currentPage == 1) {
      isLoading.value = true;
    } else {
      isLoadingMore.value = true;
    }

    try {
      // 确定搜索的分类ID
      String searchCategoryId;
      if (subCategoryId.value == 'all') {
        if (categoryId.value == 'all') {
          // 如果一级和二级分类都是"全部"，则搜索所有分类
          searchCategoryId = '';
        } else {
          // 如果只有二级分类是"全部"，则搜索当前一级分类
          searchCategoryId = categoryId.value;
        }
      } else {
        // 搜索具体的二级分类
        searchCategoryId = subCategoryId.value;
      }

      final response = await _feedsRepository.searchCategoryFeeds({
        'search': currentSearchQuery.value,
        'pageNum': _currentPage,
        'pageSize': _pageSize,
        'categoryList': searchCategoryId,
        'popular': 1,
        'articleList': 3,
      });

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final List<dynamic> pageList = data['pageList'] as List<dynamic>? ?? [];
        final results =
            pageList.map((item) => item as Map<String, dynamic>).toList();

        if (_currentPage == 1) {
          searchResults.assignAll(results);
        } else {
          searchResults.addAll(results);
        }

        // 初始化订阅状态映射
        _initializeSubscriptionStatus(results);

        totalResults.value = (data['total'] as num?)?.toInt() ?? 0;
        hasMoreData.value = searchResults.length < totalResults.value;
        hasLoadedAll.value = searchResults.length >= totalResults.value;

        if (results.isNotEmpty) {
          _currentPage++;
        }
      } else {
        if (_currentPage == 1) {
          searchResults.clear();
          totalResults.value = 0;
        }
        hasMoreData.value = false;
        hasLoadedAll.value = true;
      }
    } catch (e) {
      debugPrint('分类搜索失败: $e');
      if (_currentPage == 1) {
        searchResults.clear();
        totalResults.value = 0;
      }
      hasMoreData.value = false;
      hasLoadedAll.value = true;
    } finally {
      isLoading.value = false;
      isLoadingMore.value = false;
    }
  }

  // 加载更多搜索结果
  Future<void> loadMore() async {
    if (isLoadingMore.value || !hasMoreData.value || hasLoadedAll.value) {
      return;
    }
    await _searchByCategory();
  }

  // 搜索方法（用于搜索框触发）
  Future<void> search(String query) async {
    final trimmedQuery = query.trim();
    currentSearchQuery.value = trimmedQuery;

    // 更新搜索框文本（如果不一致的话）
    if (searchController.text != trimmedQuery) {
      searchController.text = trimmedQuery;
    }

    _currentPage = 1;
    hasLoadedAll.value = false;
    hasMoreData.value = true;
    searchResults.clear();
    totalResults.value = 0;

    // 添加到搜索历史
    if (currentSearchQuery.value.isNotEmpty) {
      addSearchHistory(currentSearchQuery.value);
    }

    await _searchByCategory();
  }

  // 设置搜索类型
  void setSearchType(int type) {
    searchType.value = type;
  }

  // 从本地获取语言 判断是否是英语
  Future<void> _isEnglish() async {
    final prefs = await SharedPreferences.getInstance();
    final language = prefs.getString('locale') == 'en_US' ? 'English' : '简体中文';
    isEnglish.value = language == 'English';
  }

  /// 加载数据
  Future<void> loadData() async {
    if (isLoading.value) return;

    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';

    try {
      await _isEnglish();
      // 获取分类数据
      final categoryResponse = await _feedsRepository.getFeedsCategory();
      if (categoryResponse.isSuccess && categoryResponse.data != null) {
        allCategories.value = categoryResponse.data ?? [];
        // 获取一级分类（parentId为0的）
        final primaryCats =
            allCategories.where((cat) => cat['parentId'] == '0').toList();

        // 添加"All"选项作为第一个选项
        final allOption = {
          'id': 'all',
          'name': 'All',
          'nameCn': '全部',
          'parentId': '0',
        };

        categories.value = [allOption, ...primaryCats];
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  /// 刷新数据
  Future<void> refreshData() async {
    await loadData();
  }

  /// 订阅源
  Future<bool> subscribeFeed(Map<String, dynamic> feed) async {
    try {
      final String feedId = feed['id']?.toString() ?? '';
      if (feedId.isEmpty) {
        Get.closeAllSnackbars();
        Get.snackbar(
          'error'.tr,
          'invalid_feed_id'.tr,
          duration: const Duration(seconds: 2),
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return false;
      }

      // 检查当前订阅状态
      final currentStatus = feed['isSub'] as int? ?? 0;
      if (currentStatus == 1) {
        Get.closeAllSnackbars();
        Get.snackbar(
          'notice'.tr,
          'already_subscribed'.tr,
          duration: const Duration(seconds: 2),
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      }

      // 构建请求参数
      final params = {
        "searchValue": feedId,
        "type": feed['type'] ?? 1,
      };

      // 调用订阅API
      final response = await _feedsRepository.subscribeRss(params);
      debugPrint('subscribeFeed response: ${response.toJson()}');
      if (response.isSuccess && response.code == 200) {
        // 更新订阅状态
        subscribedFeeds[feedId] = true;
        // 更新搜索结果中的订阅状态
        final index = searchResults
            .indexWhere((item) => item['id']?.toString() == feedId);
        if (index != -1) {
          final updatedFeed = Map<String, dynamic>.from(searchResults[index]);
          updatedFeed['isSub'] = 1;
          searchResults[index] = updatedFeed;
        }

        Get.closeAllSnackbars();
        Get.snackbar(
          'success'.tr,
          'subscribe_success'.tr,
          duration: const Duration(seconds: 2),
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      } else {
        Get.closeAllSnackbars();
        Get.snackbar(
          'error'.tr,
          response.msg,
          duration: const Duration(seconds: 2),
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        throw Exception(response.msg);
      }
    } catch (e) {
      debugPrint('订阅失败: $e');
      Get.closeAllSnackbars();
      Get.snackbar(
        'error'.tr,
        e.toString(),
        duration: const Duration(seconds: 2),
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      return false;
    }
  }

  /// 取消订阅源
  Future<bool> unsubscribeFeed(Map<String, dynamic> feed) async {
    try {
      final String feedId = feed['id']?.toString() ?? '';
      if (feedId.isEmpty) {
        Get.closeAllSnackbars();
        Get.snackbar(
          'error'.tr,
          'invalid_feed_id'.tr,
          duration: const Duration(seconds: 2),
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return false;
      }

      // 检查当前订阅状态
      final currentStatus = feed['isSub'] as int? ?? 0;
      if (currentStatus == 0) {
        Get.closeAllSnackbars();
        Get.snackbar(
          'notice'.tr,
          'not_subscribed'.tr,
          duration: const Duration(seconds: 2),
          icon: const Icon(Icons.info, color: Colors.orange),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      }

      // 调用取消订阅API
      final response = await _feedsRepository.deleteRss([int.parse(feedId)]);

      if (response.isSuccess) {
        // 更新订阅状态
        subscribedFeeds[feedId] = false;
        // 更新搜索结果中的订阅状态
        final index = searchResults
            .indexWhere((item) => item['id']?.toString() == feedId);
        if (index != -1) {
          final updatedFeed = Map<String, dynamic>.from(searchResults[index]);
          updatedFeed['isSub'] = 0;
          searchResults[index] = updatedFeed;
        }

        Get.closeAllSnackbars();
        Get.snackbar(
          'success'.tr,
          'unsubscribe_success'.tr,
          duration: const Duration(seconds: 2),
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return true;
      } else {
        Get.closeAllSnackbars();
        Get.snackbar(
          'error'.tr,
          response.msg,
          duration: const Duration(seconds: 2),
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        throw Exception(response.msg);
      }
    } catch (e) {
      debugPrint('取消订阅失败: $e');
      Get.closeAllSnackbars();
      Get.snackbar(
        'error'.tr,
        e.toString(),
        duration: const Duration(seconds: 2),
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      return false;
    }
  }

  /// 加载二级分类
  Future<void> loadSubCategories() async {
    try {
      List<Map<String, dynamic>> subCats = [];

      if (categoryId.value == 'all') {
        // 如果一级分类是"全部"，则显示所有一级分类作为二级分类
        subCats = allCategories.where((cat) => cat['parentId'] == '0').toList();
      } else {
        // 获取当前一级分类下的二级分类
        subCats = allCategories
            .where((cat) => cat['parentId'] == categoryId.value)
            .toList();
      }

      // 添加"All"选项作为第一个选项
      final allOption = {
        'id': 'all',
        'name': 'All',
        'nameCn': '全部',
        'parentId': categoryId.value,
      };

      subCategories.value = [allOption, ...subCats];
      debugPrint("二级分类加载完成: ${subCategories.length}个");
    } catch (e) {
      debugPrint('加载二级分类失败: $e');
    }
  }

  void addSearchHistory(String query) {
    if (!searchHistory.contains(query)) {
      searchHistory.insert(0, query);
      if (searchHistory.length > 10) {
        searchHistory.removeLast();
      }
      saveSearchHistory();
    }
  }

  void clearSearchHistory() {
    searchHistory.clear();
    saveSearchHistory();
  }

  Future<void> loadSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final history = prefs.getStringList('discover_search_history') ?? [];
    searchHistory.assignAll(history);
  }

  Future<void> saveSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('discover_search_history', searchHistory);
  }

  /// 初始化订阅状态映射
  void _initializeSubscriptionStatus(List<Map<String, dynamic>> results) {
    for (final result in results) {
      final feedId = result['id']?.toString();
      if (feedId != null) {
        final isSub = result['isSub'] as int? ?? 0;
        subscribedFeeds[feedId] = isSub == 1;
      }
    }
  }
}
