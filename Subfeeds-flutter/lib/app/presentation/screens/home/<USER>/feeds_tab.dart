import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_controller.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_tab.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/new_widget/news_tab.dart';
import 'package:subfeeds/app/presentation/widgets/search_bottom_sheet.dart';
import 'package:subfeeds/app/presentation/widgets/custom_app_bar.dart';

/// 订阅选项卡
class FeedsTab extends GetView<FeedsController> {
  FeedsTab({super.key});
  final _currentTab = 0.obs; // 0: News, 1: Feeds

  /// 构建右侧操作按钮列表
  List<ActionItem> _buildActionItems(BuildContext context) {
    final List<ActionItem> actions = [];

    // 过滤按钮 - 只在News标签页显示
    if (_currentTab.value == 0) {
      actions.add(
        ActionItem(
          iconPath: 'assets/icons/filter.svg',
          iconSize: 20.spx,
          iconColor: controller.newsFilterStatus.value == 0
              ? Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Theme.of(context).colorScheme.primary
              : const Color(0xFF7F8EA7),
          onPressed: () {
            controller.toggleNewsFilter();
          },
        ),
      );
    }

    // 添加按钮
    actions.add(
      ActionItem(
        iconPath: 'assets/icons/add.svg',
        iconSize: 16.spx,
        iconColor: const Color(0xFF7F8EA7),
        onPressed: () {
          _showSearchBottomSheet(context);
        },
      ),
    );

    return actions;
  }

  @override
  Widget build(BuildContext context) {
    // 创建背景色，基于当前主题

    final backgroundColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF1e2020)
        : const Color(0xffeef2f9);
    return Scaffold(
      backgroundColor: backgroundColor,
      extendBodyBehindAppBar: true, // 确保内容延伸到AppBar之下
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(50.0),
        child: Obx(() => CustomAppBar(
              backgroundColor: backgroundColor,
              title: 'new_feeds'.tr,
              leftIconPath: 'assets/home/<USER>/feeds.svg',
              onLeftIconPressed: () => _showFeedsBottomSheet(context),
              defaultIconColor: const Color(0xFF7F8EA7),
              rightActions: _buildActionItems(context),
              titleStyle: TextStyle(
                color: Theme.of(context).textTheme.titleLarge?.color,
                fontSize: 16.spx,
                fontWeight: FontWeight.w600,
              ),
              height: kToolbarHeight,
              extendToTopSafeArea: true,
            )),
      ),
      body: NewsTab(controller: controller),
    );
  }

  /// 显示搜索底部弹窗
  void _showSearchBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xff3b3b3b)
          : const Color(0xFFffffff),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height - 100,
      ),
      builder: (context) {
        return SearchBottomSheet(
          title: 'add_feeds'.tr,
          hintText: 'discover_search_hint'.tr,
          onSearch: (query) {
            Get.toNamed(Routes.SEARCH, arguments: {
              'type': 1,
              'query': query,
            });
          },
          searchHistory: controller.searchHistory,
          addSearchHistory: controller.addSearchHistory,
          clearSearchHistory: controller.clearSearchHistory,
          searchType: 1,
        );
      },
    );
  }

  /// 显示FeedsListTab作为底部弹窗
  void _showFeedsBottomSheet(BuildContext context) {
    // 确保FeedsController可用
    FeedsController feedsController;
    if (!Get.isRegistered<FeedsController>()) {
      // 如果FeedsController还没有被注册，则创建一个新的实例
      feedsController = Get.put(FeedsController());
    } else {
      // 如果已经注册，则获取现有实例
      feedsController = Get.find<FeedsController>();
    }

    // 刷新数据
    feedsController.refreshFeeds();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF444444)
          : const Color(0xFFf7faff),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.9, // 增加高度为屏幕高度的90%
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部拖动条
              Center(
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40.spx,
                  height: 4.spx,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // FeedsListTab内容
              Expanded(
                child: FeedsListTab(controller: feedsController),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// 自动保持活动状态的 Widget 包装器
class AutomaticKeepAliveWidget extends StatefulWidget {
  final Widget child;

  const AutomaticKeepAliveWidget({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  AutomaticKeepAliveWidgetState createState() =>
      AutomaticKeepAliveWidgetState();
}

class AutomaticKeepAliveWidgetState extends State<AutomaticKeepAliveWidget>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
