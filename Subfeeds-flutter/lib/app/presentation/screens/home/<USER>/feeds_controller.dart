import 'dart:async';
import 'package:get/get.dart';
import 'package:subfeeds/app/data/repositories/feeds_repository.dart';
import 'package:subfeeds/app/data/repositories/article_repository.dart'
    as article_repo;
import 'package:subfeeds/app/data/models/api_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/presentation/screens/article/article_controller.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/dialogs/create_folder_dialog.dart';

class FeedsController extends GetxController {
  final article_repo.ArticleRepository _articleRepository =
      article_repo.ArticleRepository();
  // 推荐源相关
  final ArticleRepository _feedsRepository = ArticleRepository();
  final ScrollController scrollController = ScrollController();
  // 单独为FeedsList创建一个滚动控制器
  final ScrollController feedsListScrollController = ScrollController();

  // 订阅源列表
  final RxList<Map<String, dynamic>> feedsList = <Map<String, dynamic>>[].obs;
  // 文件夹列表
  final RxList<Map<String, dynamic>> foldersList = <Map<String, dynamic>>[].obs;
  // 展开的文件夹ID集合
  final RxSet<String> expandedFolders = <String>{}.obs;

  // 文件夹加载状态 - 用于跟踪每个文件夹的内容是否正在加载
  final RxMap<String, bool> folderLoadingStatus = <String, bool>{}.obs;

  // 加载状态
  final RxBool isLoading = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool isFoldersLoading = false.obs;
  final RxBool isFeedsLoading = false.obs;

  // 分页信息
  int _page = 1;
  final int _pageSize = 25;
  final RxBool hasMoreData = true.obs;

  // 缓存总数
  int? _totalCount;

  final newsList = <Map<String, dynamic>>[].obs;
  final newsLoading = false.obs;
  final newsLoadingMore = false.obs;
  final newsError = false.obs;
  final newsErrorMessage = ''.obs;
  final newsHasMore = true.obs;
  final newsPageNum = 1.obs;
  final newsPageSize = 20.obs;

  // 搜索历史记录
  final searchHistory = <String>[].obs;

  // 筛选状态：0-未读，2-全部
  final newsFilterStatus = 2.obs;

  // 文章状态变更流的订阅
  StreamSubscription? _articleStatusSubscription;

  /// 是否固定文件夹列表
  final RxBool isFolderFixed = false.obs;

  /// 显示创建文件夹对话框
  void showCreateFolderDialog(BuildContext context) {
    // 使用FeedsListUtils.showCreateFolderDialog方法显示创建文件夹对话框
    // 由于循环引用问题，这里不直接调用FeedsListUtils方法
    // 而是使用底部弹窗的方式实现
    CreateFolderDialogUtils.show(context, this);
  }

  /// 切换文件夹固定状态
  void toggleFolderFixed() {
    isFolderFixed.value = !isFolderFixed.value;
  }

  @override
  void onInit() {
    super.onInit();
    loadAllData();
    _setupScrollController();
    _setupFeedsListScrollController();

    // 监听文章状态变更事件
    _articleStatusSubscription = ArticleController.articleStatusChanged.stream
        .listen(_handleArticleStatusChanged);
  }

  @override
  void onClose() {
    scrollController.dispose();
    feedsListScrollController.dispose();
    _articleStatusSubscription?.cancel();
    super.onClose();
  }

  void _setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 200) {
        if (!newsLoading.value && newsHasMore.value) {
          getNewsList();
        }
      }
    });
  }

  void _setupFeedsListScrollController() {
    feedsListScrollController.addListener(() {
      if (feedsListScrollController.position.pixels >=
              feedsListScrollController.position.maxScrollExtent - 200 &&
          !isLoading.value &&
          hasMoreData.value) {
        print(
            "触发加载更多，当前页码：$_page，滚动位置：${feedsListScrollController.position.pixels}，最大位置：${feedsListScrollController.position.maxScrollExtent}");
        loadFeeds();
      }
    });
  }

  // 加载所有数据（文件夹和订阅源）
  Future<void> loadAllData() async {
    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';

    try {
      await Future.wait(
          [loadFolders(), loadFeeds(), getNewsList(), getUserCount()]);
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = isFoldersLoading.value || isFeedsLoading.value;
    }
  }

  // 加载文件夹列表
  Future<void> loadFolders() async {
    isFoldersLoading.value = true;
    try {
      final response = await _feedsRepository.getFeedsGroupList();
      if (response.isSuccess && response.data != null) {
        final data = response.data;
        final List<Map<String, dynamic>> folders =
            (data?['pageList'] as List?)?.cast<Map<String, dynamic>>() ?? [];
        foldersList.value = folders;
      } else {
        hasError.value = true;
        errorMessage.value = response.msg ?? '加载文件夹失败';
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      print('加载文件夹失败: $e');
    } finally {
      isFoldersLoading.value = false;
      isLoading.value = isFoldersLoading.value || isFeedsLoading.value;
    }
  }

  /// 加载订阅源列表
  Future<void> loadFeeds() async {
    if (isFeedsLoading.value) return;

    isFeedsLoading.value = true;
    isLoading.value = true; // 确保加载状态正确设置
    try {
      final response = await _feedsRepository.getMyRssSubList({
        'pageNum': _page,
        'pageSize': _pageSize,
        'search': '',
      });

      if (response.isSuccess && response.data != null) {
        final data = response.data;
        final pageList =
            (data?['pageList'] as List?)?.cast<Map<String, dynamic>>() ?? [];

        _totalCount = (data?['total'] as num?)?.toInt() ?? 0;

        if (_page == 1) {
          feedsList.value = pageList;
        } else {
          final newList = List<Map<String, dynamic>>.from(feedsList);
          newList.addAll(pageList);
          feedsList.assignAll(newList);
        }

        hasMoreData.value = feedsList.length < (_totalCount ?? 0);
        _page++;

        // 输出调试信息
        print(
            '加载Feeds成功: 页码=$_page, 总数=$_totalCount, 当前列表大小=${feedsList.length}, 是否有更多=${hasMoreData.value}');
      } else {
        hasError.value = true;
        errorMessage.value = response.msg ?? '加载订阅源失败';
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      print('加载订阅源出错: $e');
    } finally {
      isFeedsLoading.value = false;
      isLoading.value = false; // 确保加载状态正确重置
      // 强制刷新列表视图
      feedsList.refresh();
    }
  }

  /// 刷新数据
  Future<void> refreshFeeds() async {
    _page = 1;
    _totalCount = null;
    expandedFolders.clear();
    hasMoreData.value = true; // 重置分页标志
    await loadAllData();
    return Future.value();
  }

  /// 切换文件夹展开状态
  Future<void> toggleFolder(String folderId) async {
    if (expandedFolders.contains(folderId)) {
      expandedFolders.remove(folderId);
    } else {
      // 一次只展开一个文件夹，关闭其他文件夹
      expandedFolders.clear();
      expandedFolders.add(folderId);

      // 加载文件夹内容
      try {
        final folderIndex =
            foldersList.indexWhere((f) => f['id'].toString() == folderId);
        if (folderIndex != -1) {
          // 检查是否已经有内容
          final folder = foldersList[folderIndex];
          if (folder['userFeedsPos'] == null ||
              (folder['userFeedsPos'] as List?)?.isEmpty == true) {
            // 设置加载状态为true
            folderLoadingStatus.value[folderId] = true;
            folderLoadingStatus.refresh();

            // 从API获取文件夹内容
            final response = await _feedsRepository.getGroupFeeds(folderId);
            if (response.isSuccess && response.data != null) {
              final data = response.data;
              final List<Map<String, dynamic>> folderFeeds =
                  (data?['pageList'] as List?)?.cast<Map<String, dynamic>>() ??
                      [];

              // 更新文件夹数据
              folder['userFeedsPos'] = folderFeeds;
              foldersList[folderIndex] = folder;
              foldersList.refresh();
            }

            // 设置加载状态为false，表示加载完成
            folderLoadingStatus.value[folderId] = false;
            folderLoadingStatus.refresh();
          }
        }
      } catch (e) {
        print('加载文件夹内容失败: $e');
        // 设置加载状态为false，表示加载完成（即使失败）
        folderLoadingStatus.value[folderId] = false;
        folderLoadingStatus.refresh();
      }
    }
  }

  /// 获取列表项的唯一键
  String getItemKey(dynamic item) {
    if (item is Map<String, dynamic> && item.containsKey('groupName')) {
      return 'folder_${item['id']}';
    }
    return 'feed_${item['id']}';
  }

  /// 创建文件夹
  Future<ApiResponse<Map<String, dynamic>>> createFolder(
      String folderName, List<int> feedsList) async {
    try {
      final response = await _feedsRepository.addFeedsGroup({
        'groupName': folderName,
        'feedsList': feedsList,
      });
      return response;
    } catch (e) {
      return ApiResponse.failure(
        code: -1,
        msg: e.toString(),
      );
    }
  }

  /// 搜索订阅源
  Future<List<Map<String, dynamic>>> searchFeeds(String keyword) async {
    try {
      final response = await _feedsRepository.getMyRssSubList({
        'pageNum': 1,
        'pageSize': 10,
        'search': keyword,
      });

      if (response.isSuccess && response.data != null) {
        final data = response.data;
        return (data?['pageList'] as List?)?.cast<Map<String, dynamic>>() ?? [];
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  /// 移动订阅源到指定文件夹（乐观更新）
  Future<void> moveFeedToFolder(
      int feedId, String? fromGroupId, String toGroupId) async {
    // 保存原始状态以便回退
    final originalFeedsList = List<Map<String, dynamic>>.from(feedsList);
    final originalFoldersList = List<Map<String, dynamic>>.from(foldersList);

    try {
      // 立即更新UI
      _updateLocalFeedPosition(feedId, fromGroupId, toGroupId);

      // 静默发送请求
      Map<String, dynamic> params = {
        'feedId': feedId,
      };

      if (fromGroupId != null && toGroupId != "0") {
        params['formGroupId'] = fromGroupId;
        params['toGroupId'] = toGroupId;
      } else if (fromGroupId == null && toGroupId != "0") {
        params['toGroupId'] = toGroupId;
      } else if (fromGroupId != null && toGroupId == "0") {
        params['formGroupId'] = fromGroupId;
      }

      final response = await _feedsRepository.addUserFeedsGroup(params);

      if (!response.isSuccess) {
        // 如果请求失败，回退更改
        feedsList.value = originalFeedsList;
        foldersList.value = originalFoldersList;
        Get.snackbar(
          'error'.tr,
          'move_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // 发生错误时回退更改
      feedsList.value = originalFeedsList;
      foldersList.value = originalFoldersList;
      Get.snackbar(
        'error'.tr,
        'move_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 本地更新订阅源位置
  void _updateLocalFeedPosition(
      int feedId, String? fromGroupId, String toGroupId) {
    Map<String, dynamic>? movedFeed;
    bool shouldDeleteFolder = false;
    int? folderToDeleteId;

    // 从源位置移除
    if (fromGroupId != null) {
      // 从文件夹中移除
      final folderIndex =
          foldersList.indexWhere((f) => f['id'].toString() == fromGroupId);
      if (folderIndex != -1) {
        final folder = foldersList[folderIndex];
        final feedsList = (folder['userFeedsPos'] as List?) ?? [];
        final feedIndex = feedsList
            .indexWhere((f) => f['id'].toString() == feedId.toString());
        if (feedIndex != -1) {
          movedFeed = Map<String, dynamic>.from(feedsList[feedIndex]);
          feedsList.removeAt(feedIndex);
          folder['userFeedsPos'] = feedsList;
          foldersList[folderIndex] = folder;

          // 检查文件夹是否为空，如果为空则标记删除
          if (feedsList.isEmpty) {
            shouldDeleteFolder = true;
            folderToDeleteId = int.tryParse(folder['id'].toString());
          }
        }
      }
    } else {
      // 从未分组列表中移除
      final feedIndex =
          feedsList.indexWhere((f) => f['id'].toString() == feedId.toString());
      if (feedIndex != -1) {
        movedFeed = Map<String, dynamic>.from(feedsList[feedIndex]);
        feedsList.removeAt(feedIndex);
      }
    }

    // 添加到目标位置
    if (movedFeed != null) {
      if (toGroupId == "0") {
        // 移动到未分组列表
        feedsList.insert(0, movedFeed);
      } else {
        // 移动到目标文件夹
        final folderIndex =
            foldersList.indexWhere((f) => f['id'].toString() == toGroupId);
        if (folderIndex != -1) {
          final folder = foldersList[folderIndex];
          // 如果文件夹中没有userFeedsPos属性，先创建一个空列表
          if (folder['userFeedsPos'] == null) {
            folder['userFeedsPos'] = <Map<String, dynamic>>[];
          }
          final folderFeeds = (folder['userFeedsPos'] as List?) ?? [];
          folderFeeds.insert(0, movedFeed);
          folder['userFeedsPos'] = folderFeeds;
          foldersList[folderIndex] = folder;
        }
      }
    }

    // 如果需要删除空文件夹，先从本地列表中移除
    if (shouldDeleteFolder && folderToDeleteId != null) {
      final folderIndex = foldersList
          .indexWhere((f) => f['id'].toString() == folderToDeleteId.toString());
      if (folderIndex != -1) {
        foldersList.removeAt(folderIndex);
        // 静默删除文件夹，不显示提示
        _deleteEmptyFolderSilently(folderToDeleteId);
      }
    }

    // 触发更新
    foldersList.refresh();
    feedsList.refresh();
  }

  /// 判断是否可以接收拖拽
  bool canAcceptDrag(String? currentFolderId, String? dragSourceFolderId) {
    if (currentFolderId == dragSourceFolderId) {
      return false;
    }
    return true;
  }

  /// 更新订阅源名称
  Future<void> updateRssSub(int feedId, String newName) async {
    try {
      final response = await _feedsRepository.updateRssSub({
        'id': feedId,
        'feedsName': newName,
      });

      // Get.back();
      if (response.isSuccess) {
        refreshFeeds();
        Get.snackbar(
          'success'.tr,
          'rename_success'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        Get.snackbar(
          'error'.tr,
          response.msg ?? 'rename_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // Get.back();
      Get.snackbar(
        'error'.tr,
        'operation_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 标记订阅源所有文章为已读
  Future<void> updateUserFeedsIsReadStatus(List<int> feedIds) async {
    try {
      final response = await _feedsRepository.updateUserFeedsIsReadStatus({
        'arrayIds': feedIds,
      });

      // Get.back();
      if (response.isSuccess) {
        refreshFeeds();
        Get.snackbar(
          'success'.tr,
          'mark_read_success'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        Get.snackbar(
          'error'.tr,
          response.msg ?? 'operation_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // Get.back();
      Get.snackbar(
        'error'.tr,
        'operation_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 取消订阅源
  Future<void> deleteRss(int feedId) async {
    if (feedId == -1) return;

    try {
      final response = await _feedsRepository.deleteRss([feedId]);
      // Get.back();
      if (response.isSuccess) {
        refreshFeeds();
        Get.snackbar(
          'success'.tr,
          'unfollow_success'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        Get.snackbar(
          'error'.tr,
          response.msg ?? 'operation_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // Get.back();
      Get.snackbar(
        'error'.tr,
        'operation_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 取消订阅源
  Future<void> deleteRssBatch(List<int> feedIds) async {
    if (feedIds.isEmpty) return;

    try {
      final response = await _feedsRepository.deleteRss(feedIds);
      // Get.back();
      if (response.isSuccess) {
        refreshFeeds();
        Get.snackbar(
          'success'.tr,
          'unfollow_success'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        Get.snackbar(
          'error'.tr,
          response.msg ?? 'operation_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // Get.back();
      Get.snackbar(
        'error'.tr,
        'operation_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 更新文件夹名称
  Future<void> updateFeedsGroup(int folderId, String newName) async {
    try {
      final response = await _feedsRepository.updateFeedsGroup({
        'id': folderId,
        'groupName': newName,
      });

      // Get.back();
      if (response.isSuccess) {
        refreshFeeds();
        Get.snackbar(
          'success'.tr,
          'rename_success'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        Get.snackbar(
          'error'.tr,
          response.msg ?? 'rename_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // Get.back();
      Get.snackbar(
        'error'.tr,
        'operation_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  Future<void> deleteGroupFeedsNoTips(List<int> groupId) async {
    if (groupId.isEmpty) return;

    try {
      final response = await _feedsRepository.deleteGroupFeeds({
        'arrayId': groupId.join(','),
      });

      if (response.isSuccess) {
        refreshFeeds();
      } else {
        // 删除失败
      }
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        'operation_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 删除文件夹中的订阅源
  Future<void> deleteGroupFeeds(List<int> groupId) async {
    if (groupId.isEmpty) return;

    try {
      final response = await _feedsRepository.deleteGroupFeeds({
        'arrayId': groupId.join(','),
      });

      if (response.isSuccess) {
        refreshFeeds();
        // Get.back();
        Get.snackbar(
          'success'.tr,
          'delete_folder_success'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        Get.snackbar(
          'error'.tr,
          response.msg ?? 'delete_folder_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        'operation_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  Future<void> getNewsList({bool refresh = false}) async {
    if (refresh) {
      newsPageNum.value = 1;
      newsList.clear();
      newsLoadingMore.value = false;
    }

    if (newsLoading.value) return;

    try {
      // 区分首次加载和加载更多
      final isLoadingMore = newsList.isNotEmpty;
      if (isLoadingMore) {
        newsLoadingMore.value = true;
      } else {
        newsLoading.value = true;
      }

      newsError.value = false;

      final response = await _articleRepository.getRssNewArticle(
          newsPageSize.value, newsPageNum.value, newsFilterStatus.value);

      if (response.isSuccess && response.data != null) {
        final responseData = response.data;
        final data = responseData?['data'];
        final pageList = data?['pageList'] as List? ?? [];
        final total = data?['total'] as int? ?? 0;

        newsList.addAll(pageList.cast<Map<String, dynamic>>());
        newsHasMore.value = newsList.length < total;

        if (newsHasMore.value) {
          newsPageNum.value++;
        }
      } else {
        newsError.value = true;
        newsErrorMessage.value = response.msg;
        debugPrint('response.msg401: ${response.msg}');
        Get.closeAllSnackbars();
        Get.snackbar(
          'error'.tr,
          response.msg ?? 'operation_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      newsError.value = true;
      newsErrorMessage.value = e.toString();
    } finally {
      newsLoading.value = false;
      newsLoadingMore.value = false;
    }
  }

  /// 标记文章为已读
  Future<void> markArticleAsRead(Map<String, dynamic> article) async {
    try {
      final response = await _articleRepository.insertHistory(
        int.tryParse(article['id'].toString()) ?? -1,
        int.tryParse(article['feedsId'].toString()) ?? -1,
        article['suffixTable'] ?? '',
      );
      if (response.isSuccess) {
        // 更新本地列表中的文章状态
        final index = newsList.indexWhere((a) => a['id'] == article['id']);
        if (index != -1) {
          newsList[index]['isRead'] = 1;
          newsList.refresh();
        }
      } else {
        Get.snackbar(
          'error'.tr,
          response.msg ?? '标记已读失败',
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 切换稍后阅读状态
  Future<void> toggleReadLater(Map<String, dynamic> article,
      {bool isShowToast = false}) async {
    try {
      final isCurrentlyLaterRead = article['isLaterRead'] == 1;

      if (isCurrentlyLaterRead) {
        final response = await _articleRepository.deleteLaterRead([
          int.tryParse(article['id'].toString()) ?? -1,
        ]);
        if (response.isSuccess) {
          article['isLaterRead'] = 0;
          if (isShowToast) {
            Get.snackbar(
              'success'.tr,
              response.msg.tr,
              icon: SvgPicture.asset('assets/feeds/right.svg'),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
          }
          newsList.refresh();
        } else {
          Get.snackbar(
            'error'.tr,
            response.msg ?? '取消稍后阅读失败',
            snackPosition: SnackPosition.TOP,
            icon: const Icon(Icons.error, color: Colors.red),
            backgroundColor:
                Theme.of(Get.context!).brightness == Brightness.dark
                    ? const Color(0xFF161617)
                    : const Color(0xFF161617),
            colorText: Theme.of(Get.context!).brightness == Brightness.dark
                ? Colors.white
                : Colors.white,
          );
        }
      } else {
        final response = await _articleRepository.setReadLater(
          int.tryParse(article['id'].toString()) ?? -1,
          int.tryParse(article['feedsId'].toString()) ?? -1,
          article['suffixTable'] ?? '',
        );
        if (response.isSuccess) {
          article['isLaterRead'] = 1;
          newsList.refresh();
          if (isShowToast) {
            Get.snackbar(
              'success'.tr,
              response.msg.tr,
              icon: SvgPicture.asset('assets/feeds/right.svg'),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
          }
        } else {
          Get.snackbar(
            'error'.tr,
            response.msg ?? '设置稍后阅读失败',
            snackPosition: SnackPosition.TOP,
            icon: const Icon(Icons.error, color: Colors.red),
            backgroundColor:
                Theme.of(Get.context!).brightness == Brightness.dark
                    ? const Color(0xFF161617)
                    : const Color(0xFF161617),
            colorText: Theme.of(Get.context!).brightness == Brightness.dark
                ? Colors.white
                : Colors.white,
          );
        }
      }
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 切换收藏状态
  Future<void> toggleBookmark(Map<String, dynamic> article,
      {bool isShowToast = false}) async {
    try {
      final isCurrentlyCollect = article['isCollect'] == 1;

      if (isCurrentlyCollect) {
        final response = await _articleRepository.deleteCollect([
          int.tryParse(article['id'].toString()) ?? -1,
        ]);
        if (response.isSuccess) {
          article['isCollect'] = 0;
          newsList.refresh();

          if (isShowToast) {
            Get.snackbar(
              'success'.tr,
              response.msg.tr,
              icon: SvgPicture.asset('assets/feeds/right.svg'),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
          }
        } else {
          Get.snackbar(
            'error'.tr,
            response.msg ?? '取消收藏失败',
            snackPosition: SnackPosition.TOP,
            icon: const Icon(Icons.error, color: Colors.red),
            backgroundColor:
                Theme.of(Get.context!).brightness == Brightness.dark
                    ? const Color(0xFF161617)
                    : const Color(0xFF161617),
            colorText: Theme.of(Get.context!).brightness == Brightness.dark
                ? Colors.white
                : Colors.white,
          );
        }
      } else {
        final response = await _articleRepository.insertCollect(
          int.tryParse(article['id'].toString()) ?? -1,
          int.tryParse(article['feedsId'].toString()) ?? -1,
          article['suffixTable'] ?? '',
        );
        if (response.isSuccess) {
          if (isShowToast) {
            Get.snackbar(
              'success'.tr,
              response.msg.tr,
              icon: SvgPicture.asset('assets/feeds/right.svg'),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
          }
          article['isCollect'] = 1;

          newsList.refresh();
        } else {
          Get.snackbar(
            'error'.tr,
            response.msg ?? '收藏失败',
            snackPosition: SnackPosition.TOP,
            icon: const Icon(Icons.error, color: Colors.red),
            backgroundColor:
                Theme.of(Get.context!).brightness == Brightness.dark
                    ? const Color(0xFF161617)
                    : const Color(0xFF161617),
            colorText: Theme.of(Get.context!).brightness == Brightness.dark
                ? Colors.white
                : Colors.white,
          );
        }
      }
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        e.toString(),
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  // 添加搜索历史
  void addSearchHistory(String query) {
    if (!searchHistory.contains(query)) {
      searchHistory.insert(0, query);
      if (searchHistory.length > 10) {
        searchHistory.removeLast();
      }
    }
  }

  // 清除搜索历史
  void clearSearchHistory() {
    searchHistory.clear();
  }

  // 删除单个搜索历史
  void removeSearchHistory(String query) {
    searchHistory.remove(query);
  }

  // 切换筛选状态
  void toggleNewsFilter() {
    newsFilterStatus.value = newsFilterStatus.value == 2 ? 0 : 2;
    newsPageNum.value = 1;
    newsList.clear();
    getNewsList(refresh: true);
  }

  /// 获取用户计数数据
  Future<ApiResponse<Map<String, dynamic>>> getUserCount() async {
    try {
      final response = await _feedsRepository.getUserCount();
      return response;
    } catch (e) {
      return ApiResponse.failure(
        code: -1,
        msg: e.toString(),
      );
    }
  }

  // 处理文章状态变更事件
  void _handleArticleStatusChanged(Map<String, dynamic> data) {
    if (data.containsKey('id')) {
      final String articleId = data['id'];

      // 更新新闻列表中的文章状态
      final index = newsList
          .indexWhere((article) => article['id'].toString() == articleId);

      if (index != -1) {
        // 创建文章的副本
        final updatedArticle = Map<String, dynamic>.from(newsList[index]);

        // 更新收藏状态
        if (data.containsKey('isCollect')) {
          updatedArticle['isCollect'] = data['isCollect'];
        }

        // 更新稍后阅读状态
        if (data.containsKey('isLaterRead')) {
          updatedArticle['isLaterRead'] = data['isLaterRead'];
        }

        // 更新新闻列表
        newsList[index] = updatedArticle;

        // 刷新新闻列表
        newsList.refresh();
      }
    }
  }

  /// 静默删除空文件夹（不显示提示）
  Future<void> _deleteEmptyFolderSilently(int folderId) async {
    try {
      await _feedsRepository.deleteGroupFeeds({
        'arrayId': folderId.toString(),
      });

      // 不管成功失败都不显示提示，因为这是自动删除空文件夹的操作
      // 如果删除失败，下次刷新时会重新加载正确的数据
    } catch (e) {
      // 静默处理错误，不显示给用户
      // 如果删除失败，下次刷新时会重新加载正确的数据
    }
  }
}
