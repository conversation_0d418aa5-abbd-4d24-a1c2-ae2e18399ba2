import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/splash/splash_controller.dart';

/// 启动页面
class SplashScreen extends GetView<SplashController> {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // 预缓存splash图片以提升性能
    precacheImage(const AssetImage('assets/images/splash.png'), context);
    // 预计算主题相关的颜色，避免重复计算
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDark ? const Color(0xFF3c3c3c) : const Color(0xFFf7faff);
    final textColor =
        isDark ? const Color(0xFFFFFFFF) : const Color(0xFF333333);

    return Scaffold(
      backgroundColor: backgroundColor,
      body: SafeArea(
          child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            // 主图 - 使用优化的图片加载
            Image.asset(
              'assets/images/splash.png',
              width: 92.spx,
              height: 92.spx,
              fit: BoxFit.contain,
              // 添加错误处理
            ),
            const Spacer(),
            // 底部文字 - 简化样式计算
            Padding(
              padding: const EdgeInsets.only(bottom: 20, left: 20, right: 20),
              child: Text(
                'SubFeeds',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18.spx,
                  color: textColor,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Rubik',
                  fontStyle: FontStyle.italic,
                  letterSpacing: 1,
                ),
              ),
            ),
          ],
        ),
      )),
    );
  }
}
