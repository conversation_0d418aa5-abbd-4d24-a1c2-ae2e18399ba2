import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/controllers/theme_controller.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 闪屏页控制器
class SplashController extends GetxController {
  // 是否为暗黑模式
  final RxBool isDarkMode = false.obs;

  // 获取主题控制器
  final ThemeController themeController = Get.find<ThemeController>();
  // 存储实例
  final GetStorage _storage = GetStorage();

  // 导航计时器
  Timer? _navigationTimer;

  // 是否已导航
  bool _hasNavigated = false;

  ///自动缩放
  final DimensionsAutoScaleResolver _autoScaleResolver =
      DimensionsAutoScaleResolver();

  @override
  void onInit() {
    super.onInit();
    try {
      // 检测当前主题模式
      isDarkMode.value = themeController.isDarkMode.value;
      // 监听主题变化
      ever(themeController.isDarkMode, (bool darkMode) {
        isDarkMode.value = darkMode;
      });
      // 设置安全计时器，确保即使出现问题也能在1.5秒后导航
      _navigationTimer = Timer(const Duration(milliseconds: 1500), () {
        _navigateToHome();
      });

      /// 设置尺寸单位自动缩放
      if (!_autoScaleResolver.hasSetup) {
        _autoScaleResolver.trySetup(Get.context!, designWidth: 375.0);
      }

      // 立即开始快速初始化
      _quickInitialize();
    } catch (e) {
      debugPrint('SplashController初始化错误: $e');
      _navigateToHome();
    }
  }

  /// 快速初始化
  Future<void> _quickInitialize() async {
    if (_hasNavigated) return;

    try {
      // 检查是否首次启动
      final isFirstLaunch = _storage.read('first_launch') ?? true;

      if (isFirstLaunch) {
        // 异步写入，不等待完成
        unawaited(_storage.write('first_launch', false));
        // 最小延迟后跳转到引导页
        await Future.delayed(const Duration(milliseconds: 100));
        _navigateTo(Routes.ONBOARDING);
        return;
      }
      // 快速检查token（不进行网络验证，暂时不使用结果）
      await _quickCheckToken();
      // 最小延迟后导航到首页
      await Future.delayed(const Duration(milliseconds: 100));
      _navigateToHome();
    } catch (e) {
      debugPrint('快速初始化失败: $e');
      _navigateToHome();
    }
  }

  /// 快速检查token（仅本地检查，不进行网络验证）
  Future<bool> _quickCheckToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      return token != null && token.isNotEmpty;
    } catch (e) {
      debugPrint('快速检查token失败: $e');
      return false;
    }
  }

  /// 导航到首页
  void _navigateToHome() {
    _navigateTo(Routes.HOME);
  }

  @override
  void onClose() {
    _navigationTimer?.cancel();
    super.onClose();
  }

  /// 导航到指定路由
  void _navigateTo(String route) {
    if (_hasNavigated) return;

    try {
      debugPrint('导航到: $route');
      _hasNavigated = true;
      _navigationTimer?.cancel();
      Get.offAllNamed(route);
    } catch (e) {
      debugPrint('导航失败: $e');
      // 简单重试一次
      try {
        Get.offAllNamed(Routes.HOME);
      } catch (e2) {
        debugPrint('导航重试失败: $e2');
      }
    }
  }
}
