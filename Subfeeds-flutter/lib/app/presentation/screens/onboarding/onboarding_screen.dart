import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/onboarding/onboarding_controller.dart';

/// 引导页面
class OnboardingScreen extends GetView<OnboardingController> {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF3c3c3c)
          : const Color(0xFFf7faff),
      body: Stack(
        children: [
          PageView.builder(
            controller: controller.pageController,
            itemCount: controller.onboardingItems.length,
            onPageChanged: controller.onPageChanged,
            itemBuilder: (context, index) {
              return _buildPageByIndex(context, index);
            },
          ),
          // 页面指示器 - 只在非最后一页显示
          Obx(() => Visibility(
                visible: controller.currentPage.value <
                    controller.onboardingItems.length - 1,
                child: Positioned(
                  left: 0,
                  right: 0,
                  bottom: 60,
                  child: _buildPageIndicator(context),
                ),
              )),
          // 跳过按钮
          Positioned(
            top: 60,
            right: 20,
            child: TextButton(
                onPressed: controller.goToLoginSelection,
                child: Text(
                  'Skip',
                  style: TextStyle(
                    fontSize: 14.spx,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Inter',
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFFD1D1D1)
                        : const Color(0xFF999999),
                  ),
                )),
          ),
        ],
      ),
    );
  }

  /// 根据页面索引构建不同的页面布局
  Widget _buildPageByIndex(BuildContext context, int index) {
    switch (index) {
      case 0:
        return _buildPage1(context);
      case 1:
        return _buildPage2(context);
      case 2:
        return _buildPage3(context);
      default:
        return _buildPage1(context);
    }
  }

  /// 构建第一页 - Stay Informed, Your Way
  Widget _buildPage1(BuildContext context) {
    final item = controller.onboardingItems[0];
    return SafeArea(
      child: Column(
        children: [
          // 图片
          Expanded(
            flex: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Image.asset(
                item.image,
                fit: BoxFit.contain,
                width: double.infinity,
                height: double.infinity,
              ),
            ),
          ),

          // 间距 28px
          const SizedBox(height: 28),

          // 标题 - 第一页使用更大的字体和特殊样式
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 30.0),
            child: Text(
              item.title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 28.spx,
                fontWeight: FontWeight.bold,
                fontFamily: 'Inter',
              ),
            ),
          ),

          // 间距 15px
          SizedBox(height: 15.spx),

          // 描述 - 第一页使用稍大的字体
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 35.0),
            child: Text(
              item.description,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                fontFamily: 'Inter',
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFFD1D1D1)
                    : const Color(0xFF999999),
              ),
            ),
          ),

          const Spacer(),
        ],
      ),
    );
  }

  /// 构建第二页 - Seamless Sync Across Devices
  Widget _buildPage2(BuildContext context) {
    final item = controller.onboardingItems[1];
    return SafeArea(
      child: Column(
        children: [
          // 图片 - 第二页使用不同的布局
          Expanded(
            flex: 8,
            child: Container(
              padding:
                  const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10.0),
              child: Image.asset(
                item.image,
                fit: BoxFit.contain,
                width: double.infinity,
                height: double.infinity,
              ),
            ),
          ),

          // 间距 28px
          const SizedBox(height: 28),

          // 标题 - 第一页使用更大的字体和特殊样式
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 30.0),
            child: Text(
              item.title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 28.spx,
                fontWeight: FontWeight.bold,
                fontFamily: 'Inter',
              ),
            ),
          ),

          // 间距 15px
          SizedBox(height: 15.spx),

          // 描述 - 第一页使用稍大的字体
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 35.0),
            child: Text(
              item.description,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                fontFamily: 'Inter',
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFFD1D1D1)
                    : const Color(0xFF999999),
              ),
            ),
          ),

          const Spacer(),
        ],
      ),
    );
  }

  /// 构建第三页 - Instant AI Insights (带Get Started按钮)
  Widget _buildPage3(BuildContext context) {
    final item = controller.onboardingItems[2];
    return SafeArea(
      bottom: false,
      child: Column(
        children: [
          // 图片 - 第三页使用更紧凑的布局
          Expanded(
            flex: 8,
            child: Container(
              padding:
                  const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10.0),
              child: Image.asset(
                item.image,
                fit: BoxFit.contain,
                width: double.infinity,
                height: double.infinity,
              ),
            ),
          ),

          // 间距 28px
          const SizedBox(height: 28),

          // 标题 - 第一页使用更大的字体和特殊样式
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 30.0),
            child: Text(
              item.title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 28.spx,
                fontWeight: FontWeight.bold,
                fontFamily: 'Inter',
              ),
            ),
          ),

          // 间距 15px
          SizedBox(height: 15.spx),

          // 描述 - 第一页使用稍大的字体
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 35.0),
            child: Text(
              item.description,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                fontFamily: 'Inter',
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFFD1D1D1)
                    : const Color(0xFF999999),
              ),
            ),
          ),
          SizedBox(height: 30.spx),
          // Get Started 按钮 - 只在第三页显示
          Padding(
            padding:
                const EdgeInsets.only(bottom: 32.0, left: 20.0, right: 20.0),
            child: TextButton(
                style: TextButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  padding: const EdgeInsets.symmetric(
                      horizontal: 32.0, vertical: 16.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0.spx),
                  ),
                  minimumSize:
                      Size(MediaQuery.of(context).size.width * 0.9, 50),
                ),
                onPressed: controller.goToLoginSelection,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Let\'s Go',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14.spx,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Icon(
                      Icons.arrow_forward,
                      color: Colors.white,
                      size: 16,
                    ),
                  ],
                )),
          ),
        ],
      ),
    );
  }

  /// 构建页面指示器
  Widget _buildPageIndicator(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        controller.onboardingItems.length, // 生成所有页面的指示器点
        (index) => Obx(() => _buildDotIndicator(context, index)),
      ),
    );
  }

  /// 构建页面指示器点
  Widget _buildDotIndicator(BuildContext context, int index) {
    final isActive = controller.currentPage.value == index;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      width: isActive ? 40 : 8,
      height: 8,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: isActive
            ? Theme.of(context).primaryColor // 主题色
            : Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF45529e)
                : const Color(0xFFa2b0ff), // 非激活状态颜色
      ),
    );
  }
}
