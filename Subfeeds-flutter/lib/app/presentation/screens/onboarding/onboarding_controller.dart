import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

/// 引导页控制器
class OnboardingController extends GetxController {
  // 页面控制器
  late PageController pageController;
  // 当前页面索引
  final currentPage = 0.obs;
  final isDarkMode = false.obs;
  // 引导页数据
  final List<OnboardingItem> onboardingItems = [];

  @override
  void onInit() {
    super.onInit();
    pageController = PageController();
    getSystemTheme();
    if (isDarkMode.value) {
      onboardingItems.add(OnboardingItem(
        image: 'assets/images/leading1-dark.png',
        title: 'Focus on What Matters',
        description: 'Customize your feed and stay updated on what matters.',
      ));
      onboardingItems.add(OnboardingItem(
        image: 'assets/images/leading2-dark.png',
        title: 'Discover & Share Great Content',
        description: 'Sync your subscriptions across web Android and iOS.',
      ));
      onboardingItems.add(OnboardingItem(
        image: 'assets/images/leading3-dark.png',
        title: 'Immersive Reading Experience',
        description: 'Get instant insights and recommendations.',
      ));
    } else {
      onboardingItems.add(OnboardingItem(
        image: 'assets/images/leading1.png',
        title: 'Focus on What Matters',
        description: 'Customize your feed and stay updated on what matters.',
      ));
      onboardingItems.add(OnboardingItem(
        image: 'assets/images/leading2.png',
        title: 'Discover & Share Great Content',
        description: 'Sync your subscriptions across web Android and iOS.',
      ));
      onboardingItems.add(OnboardingItem(
        image: 'assets/images/leading3.png',
        title: 'Immersive Reading Experience',
        description: 'Get instant insights and recommendations.',
      ));
    }
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }

  /// 页面变化回调
  void onPageChanged(int index) {
    currentPage.value = index;
  }

  /// 获取系统主题
  void getSystemTheme() {
    isDarkMode.value = Theme.of(Get.context!).brightness == Brightness.dark;
  }

  /// 跳转到下一页
  void nextPage() {
    if (currentPage.value < onboardingItems.length - 1) {
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 跳过引导，直接进入主页
  void skipToHome() {
    Get.offAllNamed(Routes.HOME);
  }

  /// 进入登陆选择页面
  void goToLoginSelection() {
    Get.offAllNamed(Routes.LOGIN_SELECTION);
  }
}

/// 引导页项目
class OnboardingItem {
  final String image;
  final String title;
  final String description;

  OnboardingItem({
    required this.image,
    required this.title,
    required this.description,
  });
}
