import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/theme_controller.dart';
import 'package:subfeeds/app/core/theme/app_theme.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();

    return Scaffold(
      appBar: AppBar(
        title: Text('关于 SubFeeds'.tr),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 40),
            // Logo
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(15.0),
                child: Obx(() {
                  return themeController.isDarkMode.value
                      ? Image.asset(
                          'assets/images/logo-dark.png',
                        )
                      : Image.asset(
                          'assets/images/logo-light.png',
                        );
                }),
              ),
            ),
            const SizedBox(height: 20),
            // App名称
            Text(
              'SubFeeds',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 5),
            // 版本号
            const Text(
              'v1.0.0',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 20),
            // 描述
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                '你的订阅内容管理助手，帮助你整合和管理各种订阅内容，提供更好的阅读体验。'.tr,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.5,
                ),
              ),
            ),
            const SizedBox(height: 40),
            // 分隔线
            const Divider(),
            // 联系我们
            ListTile(
              leading: const Icon(Icons.email),
              title: Text('联系我们'.tr),
              subtitle: const Text('<EMAIL>'),
              onTap: () => _launchEmail('<EMAIL>'),
            ),
            // 官方网站
            ListTile(
              leading: const Icon(Icons.language),
              title: Text('官方网站'.tr),
              subtitle: const Text('https://subfeeds.com'),
              onTap: () => _launchUrl('https://subfeeds.com'),
            ),
            // GitHub
            ListTile(
              leading: const Icon(Icons.code),
              title: const Text('GitHub'),
              subtitle: const Text('https://github.com/subfeeds'),
              onTap: () => _launchUrl('https://github.com/subfeeds'),
            ),
            const Divider(),
            // 版权信息
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                '© ${DateTime.now().year} SubFeeds. ${'版权所有'.tr}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 启动邮件客户端
  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      queryParameters: {
        'subject': 'SubFeeds 反馈',
      },
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      Get.snackbar(
        'error'.tr,
        'launch_email_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  // 启动URL
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      Get.snackbar(
        'error'.tr,
        'launch_url_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }
}
