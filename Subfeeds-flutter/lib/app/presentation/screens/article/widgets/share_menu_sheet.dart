import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/article/article_controller.dart';

class ShareMenuSheet {
  final BuildContext context;
  final ArticleController controller;
  final String selectedText;
  const ShareMenuSheet(
      {Key? key,
      required this.context,
      required this.controller,
      this.selectedText = ''});

  /// 显示分享菜单
  void show() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).brightness == Brightness.light
          ? const Color(0xFFF7FAFF)
          : const Color(0xFF444444),
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.light
              ? const Color(0xFFF7FAFF)
              : const Color(0xFF444444),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Text(
                selectedText.isEmpty ? 'share_to'.tr : 'share_selected_text'.tr,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: controller.getTextColor(),
                    ),
              ),
            ),
            if (selectedText.isNotEmpty)
              // 显示选中的文本预览（截断过长的文本）
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  selectedText.length > 100
                      ? '${selectedText.substring(0, 100)}...'
                      : selectedText,
                  style: TextStyle(
                    fontSize: 14,
                    color: controller.getTextColor().withOpacity(0.7),
                    fontStyle: FontStyle.italic,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            if (selectedText.isNotEmpty) const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              crossAxisCount: 4,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: [
                _buildShareItem(
                  context,
                  'assets/share/facebook.svg',
                  'platform_facebook'.tr,
                  () => controller.shareOtherApp(
                      appType: 'Facebook', text: selectedText),
                ),
                _buildShareItem(
                  context,
                  'assets/share/twitter.svg',
                  'platform_twitter'.tr,
                  () => controller.shareOtherApp(
                      appType: 'Twitter', text: selectedText),
                ),
                _buildShareItem(
                  context,
                  'assets/share/instagram.svg',
                  'platform_instagram'.tr,
                  () => controller.shareOtherApp(
                      appType: 'Instagram', text: selectedText),
                ),
                _buildShareItem(
                  context,
                  'assets/share/linkedin.svg',
                  'platform_linkedin'.tr,
                  () => controller.shareOtherApp(
                      appType: 'LinkedIn', text: selectedText),
                ),
                _buildShareItem(
                  context,
                  'assets/share/pinterest.svg',
                  'platform_pinterest'.tr,
                  () => controller.shareOtherApp(
                      appType: "Pinterest", text: selectedText),
                ),
                _buildShareItem(
                  context,
                  'assets/share/reddit.svg',
                  'platform_reddit'.tr,
                  () => controller.shareOtherApp(
                      appType: "Reddit", text: selectedText),
                ),
                _buildShareItem(
                  context,
                  'assets/share/telegram.svg',
                  'platform_telegram'.tr,
                  () => controller.shareOtherApp(
                      appType: "Telegram", text: selectedText),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// 构建分享项目
  Widget _buildShareItem(
    BuildContext context,
    String iconPath,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: () {
        Navigator.pop(context); // 关闭分享菜单
        onTap();
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 48.spx,
            height: 48.spx,
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF212534)
                  : const Color(0xFFeceff2),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.5),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: SvgPicture.asset(
                iconPath,
                width: 24.spx,
                height: 24.spx,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
