import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 聊天消息气泡组件
class ChatMessageBubble extends StatefulWidget {
  final String content;
  final bool isUser;
  final bool isLoading;
  final bool isError;
  final bool isComplete; // AI消息是否已完成生成
  final VoidCallback? onCopy;
  final VoidCallback? onRegenerate;
  final bool isDark;
  final String? messageId; // 用于标识消息
  final bool isLastMessage; // 是否是对话中的最后一条消息

  const ChatMessageBubble({
    super.key,
    required this.content,
    required this.isUser,
    this.isLoading = false,
    this.isError = false,
    this.isComplete = false,
    this.onCopy,
    this.onRegenerate,
    this.isDark = false,
    this.messageId,
    this.isLastMessage = false,
  });

  @override
  State<ChatMessageBubble> createState() => _ChatMessageBubbleState();
}

class _ChatMessageBubbleState extends State<ChatMessageBubble>
    with TickerProviderStateMixin {
  // 本地状态管理
  bool _isLiked = false;
  bool _isDisliked = false;

  // 动画控制器
  late AnimationController _likeAnimationController;
  late AnimationController _dislikeAnimationController;
  late AnimationController _regenerateAnimationController;

  // 动画
  late Animation<double> _likeScaleAnimation;
  late Animation<double> _dislikeScaleAnimation;
  late Animation<double> _regenerateScaleAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _dislikeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _regenerateAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    // 初始化动画
    _likeScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _likeAnimationController,
      curve: Curves.easeInOut,
    ));

    _dislikeScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _dislikeAnimationController,
      curve: Curves.easeInOut,
    ));

    _regenerateScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _regenerateAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _likeAnimationController.dispose();
    _dislikeAnimationController.dispose();
    _regenerateAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 10.spx),
      child: Row(
        mainAxisAlignment:
            widget.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 消息内容
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.85,
              ),
              child: widget.isUser
                  ? _buildUserMessage(context)
                  : _buildAIMessage(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建用户消息
  Widget _buildUserMessage(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: widget.isDark
            ? const Color(0xFF2b2b2b)
            : Theme.of(context).primaryColor,
        borderRadius: BorderRadius.circular(12.spx).copyWith(
          bottomRight: const Radius.circular(0),
        ),
      ),
      child: Text(
        widget.content,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 13,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建AI消息
  Widget _buildAIMessage(BuildContext context) {
    return Container(
      padding:
          EdgeInsets.only(left: 16, right: 16, top: 15.spx, bottom: 10.spx),
      decoration: BoxDecoration(
        color:
            widget.isDark ? const Color(0xFF565656) : const Color(0xFFffffff),
        borderRadius: BorderRadius.circular(12.spx).copyWith(
          bottomLeft: const Radius.circular(0),
        ),
        border: widget.isDark
            ? Border.all(color: const Color(0xFF565656), width: 1)
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.isLoading)
            _buildLoadingIndicator()
          else if (widget.isError)
            _buildErrorMessage(context)
          else
            _buildMarkdownContent(context),

          // 操作按钮 - 只有在AI完全响应完成后才显示
          if (_shouldShowActionButtons())
            AnimatedOpacity(
              opacity: _shouldShowActionButtons() ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Padding(
                  padding: EdgeInsets.only(top: 10.spx),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 左侧：点赞和点踩按钮
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildLikeButton(context),
                          SizedBox(width: 10.spx),
                          _buildDislikeButton(context),
                        ],
                      ),
                      // 右侧：复制和重新生成按钮
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildCopyButton(context),
                          SizedBox(width: 10.spx), // 复制和重新生成之间的间距
                          _buildRegenerateButton(context),
                        ],
                      ),
                    ],
                  )),
            )
        ],
      ),
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator() {
    return _ColorfulDotsLoadingIndicator();
  }

  Widget _buildLikeButton(BuildContext context) {
    return AnimatedBuilder(
      animation: _likeScaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _likeScaleAnimation.value,
          child: IconButton(
            style: IconButton.styleFrom(
              backgroundColor: _isLiked
                  ? Theme.of(context).primaryColor
                  : (widget.isDark
                      ? const Color(0xFF3e3e3e)
                      : const Color(0xFFdcdcdc)),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(50)),
              ),
              padding: EdgeInsets.zero,
              maximumSize: Size(28.spx, 28.spx),
              minimumSize: Size(28.spx, 28.spx),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            onPressed: _handleLikePressed,
            icon: SvgPicture.asset(
              'assets/icons/like.svg',
              width: 12.spx,
              height: 12.spx,
              colorFilter: ColorFilter.mode(
                _isLiked ? Colors.white : const Color(0xFF7e8ea7),
                BlendMode.srcIn,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDislikeButton(BuildContext context) {
    return AnimatedBuilder(
      animation: _dislikeScaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _dislikeScaleAnimation.value,
          child: IconButton(
            style: IconButton.styleFrom(
              backgroundColor: _isDisliked
                  ? Colors.red
                  : (widget.isDark
                      ? const Color(0xFF3e3e3e)
                      : const Color(0xFFdcdcdc)),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(50)),
              ),
              padding: EdgeInsets.zero,
              maximumSize: Size(28.spx, 28.spx),
              minimumSize: Size(28.spx, 28.spx),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            onPressed: _handleDislikePressed,
            icon: SvgPicture.asset(
              'assets/icons/dislike.svg',
              width: 12.spx,
              height: 12.spx,
              colorFilter: ColorFilter.mode(
                _isDisliked ? Colors.white : const Color(0xFF7e8ea7),
                BlendMode.srcIn,
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建错误消息
  Widget _buildErrorMessage(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.error_outline,
          color: Theme.of(context).colorScheme.error,
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            widget.content,
            style: TextStyle(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建Markdown内容
  Widget _buildMarkdownContent(BuildContext context) {
    return MarkdownBody(
      key: ValueKey(
          'markdown_${widget.content.length}_${widget.content.hashCode}'), // 确保内容变化时重建
      data: widget.content,
      selectable: true,
      styleSheet: MarkdownStyleSheet(
        p: TextStyle(
          color: widget.isDark ? Colors.white : const Color(0xFF333333),
          fontSize: 13.spx,
          height: 1.5,
        ),
        horizontalRuleDecoration: BoxDecoration(
          color:
              widget.isDark ? const Color(0xFF747474) : const Color(0xFFD5D5D5),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: widget.isDark
                ? const Color(0xFF747474)
                : const Color(0xFFD5D5D5),
          ),
        ),
        blockquoteDecoration: BoxDecoration(
          color:
              widget.isDark ? const Color(0xFF565656) : const Color(0xFFffffff),
          border: Border(
            left: BorderSide(
              color: widget.isDark
                  ? const Color(0xFF747474)
                  : const Color(0xFFD5D5D5),
              width: 1,
            ),
          ),
        ),
        del: TextStyle(
          color: widget.isDark ? Colors.white : const Color(0xFF333333),
          fontSize: 13.spx,
          height: 1.5,
        ),
        listIndent: 4.spx,
        h1: TextStyle(
          color: widget.isDark ? Colors.white : const Color(0xFF333333),
          fontSize: 18.spx,
          fontWeight: FontWeight.bold,
        ),
        h2: TextStyle(
          color: widget.isDark ? Colors.white : const Color(0xFF333333),
          fontSize: 16.spx,
          fontWeight: FontWeight.bold,
        ),
        h3: TextStyle(
          color: widget.isDark ? Colors.white : const Color(0xFF333333),
          fontSize: 14.spx,
          fontWeight: FontWeight.bold,
        ),
        h4: TextStyle(
          color: widget.isDark ? Colors.white : const Color(0xFF333333),
          fontSize: 12.spx,
          fontWeight: FontWeight.bold,
        ),
        h5: TextStyle(
          color: widget.isDark ? Colors.white : const Color(0xFF333333),
          fontSize: 10.spx,
          fontWeight: FontWeight.bold,
        ),
        h6: TextStyle(
          color: widget.isDark ? Colors.white : const Color(0xFF333333),
          fontSize: 8.spx,
          fontWeight: FontWeight.bold,
        ),
        code: TextStyle(
          backgroundColor:
              widget.isDark ? const Color(0xFF1e1e1e) : const Color(0xFFf6f8fa),
          color:
              widget.isDark ? const Color(0xFFe1e4e8) : const Color(0xFF24292e),
          fontSize: 12.spx,
        ),
        codeblockDecoration: BoxDecoration(
          color:
              widget.isDark ? const Color(0xFF1e1e1e) : const Color(0xFFf6f8fa),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: widget.isDark
                ? const Color(0xFF30363d)
                : const Color(0xFFd1d9e0),
          ),
        ),
        tableHead: TextStyle(
          color: widget.isDark ? Colors.white : const Color(0xFF333333),
          fontSize: 12.spx, // 比正文稍小
          fontWeight: FontWeight.w600,
        ),
        tableBody: TextStyle(
          color: widget.isDark ? Colors.white70 : Colors.black87,
          fontSize: 11.spx, // 紧凑尺寸
          height: 1.3,
        ),
        tableBorder: TableBorder.all(
          color:
              widget.isDark ? const Color(0xFF3A3F5C) : const Color(0xFFE2E8F0),
          width: 0.5, // 细边框
        ),
        tableCellsPadding: EdgeInsets.symmetric(
          horizontal: 6.spx, // 水平紧凑
          vertical: 4.spx, // 垂直紧凑
        ),
        tableCellsDecoration: BoxDecoration(
          color: Colors.transparent, // 透明背景更紧凑
        ),
        tableColumnWidth: const FlexColumnWidth(1.0), // 等宽列
        tableHeadAlign: TextAlign.center, // 表头居中
        tableVerticalAlignment: TableCellVerticalAlignment.top,
        blockquote: TextStyle(
          color:
              widget.isDark ? const Color(0xFF8b949e) : const Color(0xFF656d76),
          fontStyle: FontStyle.italic,
        ),

        listBullet: TextStyle(
          color: widget.isDark ? Colors.white : const Color(0xFF333333),
        ),
      ),
      // Remove custom code builder for now to avoid complexity
    );
  }

  /// 构建复制按钮
  Widget _buildCopyButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _handleCopy(context),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.spx, vertical: 6.spx),
        decoration: BoxDecoration(
          color:
              widget.isDark ? const Color(0xFF3e3e3e) : const Color(0xFFdcdcdc),
          borderRadius: BorderRadius.circular(50),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              'assets/icons/note_copy.svg',
              width: 12.spx,
              height: 12.spx,
              colorFilter: ColorFilter.mode(
                Color(0xFF7e8ea7),
                BlendMode.srcIn,
              ),
            ),
            SizedBox(width: 2.spx),
            Text(
              'copy'.tr,
              style: TextStyle(
                color: Color(0xFF7e8ea7),
                fontSize: 10.spx,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建重新生成按钮
  Widget _buildRegenerateButton(BuildContext context) {
    return AnimatedBuilder(
      animation: _regenerateScaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _regenerateScaleAnimation.value,
          child: GestureDetector(
            onTap: _handleRegeneratePressed,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.spx, vertical: 6.spx),
              decoration: BoxDecoration(
                color: widget.isDark
                    ? const Color(0xFF3e3e3e)
                    : const Color(0xFFdcdcdc),
                borderRadius: BorderRadius.circular(50),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    'assets/icons/refresh.svg',
                    width: 12.spx,
                    height: 12.spx,
                    colorFilter: const ColorFilter.mode(
                      Color(0xFF7e8ea7),
                      BlendMode.srcIn,
                    ),
                  ),
                  SizedBox(width: 2.spx),
                  Text(
                    'regenerate'.tr,
                    style: TextStyle(
                      color: const Color(0xFF7e8ea7),
                      fontSize: 10.spx,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 处理复制操作
  Future<void> _handleCopy(BuildContext context) async {
    try {
      await Clipboard.setData(ClipboardData(text: widget.content));
      HapticFeedback.lightImpact();

      Get.snackbar(
        'success'.tr,
        'text_copied_to_clipboard'.tr,
        snackPosition: SnackPosition.TOP,
        icon: SvgPicture.asset('assets/feeds/right.svg'),
        backgroundColor: const Color(0xFF161617),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      widget.onCopy?.call();
    } catch (e) {
      debugPrint('复制文本失败: $e');
      Get.snackbar(
        'error'.tr,
        'copy_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: const Color(0xFF161617),
        colorText: Colors.white,
      );
    }
  }

  /// 处理点赞按钮点击
  void _handleLikePressed() {
    // 播放动画
    _likeAnimationController.forward().then((_) {
      _likeAnimationController.reverse();
    });

    // 震动反馈
    HapticFeedback.lightImpact();

    setState(() {
      if (_isLiked) {
        // 如果已经点赞，取消点赞
        _isLiked = false;
      } else {
        // 如果没有点赞，设置点赞并取消点踩
        _isLiked = true;
        _isDisliked = false; // 互斥
      }
    });

    debugPrint('点赞状态: $_isLiked');
  }

  /// 处理点踩按钮点击
  void _handleDislikePressed() {
    // 播放动画
    _dislikeAnimationController.forward().then((_) {
      _dislikeAnimationController.reverse();
    });

    // 震动反馈
    HapticFeedback.lightImpact();

    setState(() {
      if (_isDisliked) {
        // 如果已经点踩，取消点踩
        _isDisliked = false;
      } else {
        // 如果没有点踩，设置点踩并取消点赞
        _isDisliked = true;
        _isLiked = false; // 互斥
      }
    });

    debugPrint('点踩状态: $_isDisliked');
  }

  /// 处理重新生成按钮点击
  void _handleRegeneratePressed() {
    // 播放动画
    _regenerateAnimationController.forward().then((_) {
      _regenerateAnimationController.reverse();
    });

    // 震动反馈
    HapticFeedback.mediumImpact();

    // 重置点赞/点踩状态
    _resetFeedbackState();

    // 调用重新生成回调
    if (widget.onRegenerate != null) {
      widget.onRegenerate!();
      debugPrint('重新生成消息: ${widget.messageId}，已重置反馈状态');
    } else {
      debugPrint('重新生成回调未设置');
    }
  }

  /// 重置反馈状态
  void _resetFeedbackState() {
    setState(() {
      _isLiked = false;
      _isDisliked = false;
    });
    debugPrint('反馈状态已重置');
  }

  /// 判断是否应该显示操作按钮
  bool _shouldShowActionButtons() {
    // 只有AI消息才显示操作按钮
    if (widget.isUser) {
      return false;
    }

    // 必须满足以下所有条件：
    // 1. 不在加载状态
    // 2. 没有错误
    // 3. 内容不为空
    // 4. 内容不只是空白字符
    // 5. 是对话中的最后一条消息
    // 6. AI消息已完成生成
    return !widget.isLoading &&
        !widget.isError &&
        widget.content.trim().isNotEmpty &&
        widget.isLastMessage &&
        widget.isComplete;
  }
}

/// 多彩圆点加载指示器
class _ColorfulDotsLoadingIndicator extends StatefulWidget {
  @override
  State<_ColorfulDotsLoadingIndicator> createState() =>
      _ColorfulDotsLoadingIndicatorState();
}

class _ColorfulDotsLoadingIndicatorState
    extends State<_ColorfulDotsLoadingIndicator> with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  // 5种颜色的圆点
  final List<Color> _colors = [
    const Color(0xFF639ce5), // 蓝色
    const Color(0xFF958ee8), // 紫色
    const Color(0xFFce81c1), // 粉紫色
    const Color(0xFFdf929f), // 粉红色
    const Color(0xFFf4bf95), // 橙色
  ];

  @override
  void initState() {
    super.initState();

    _controllers = List.generate(
      _colors.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: -8.0, // 向上移动8像素
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));
    }).toList();

    _startAnimation();
  }

  void _startAnimation() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(_colors.length, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.only(
                right: index < _colors.length - 1 ? 4.spx : 0,
              ),
              transform:
                  Matrix4.translationValues(0, _animations[index].value, 0),
              child: Container(
                width: 8.spx,
                height: 8.spx,
                decoration: BoxDecoration(
                  color: _colors[index],
                  shape: BoxShape.circle,
                ),
              ),
            );
          },
        );
      }),
    );
  }
}
