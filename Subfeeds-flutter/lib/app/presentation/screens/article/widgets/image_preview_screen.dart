import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:share_plus/share_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:http/http.dart' as http;
import 'dart:io';

import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';

/// 图片预览页面
class ImagePreviewScreen extends StatefulWidget {
  final List<String> imageUrls;
  final int initialIndex;
  final bool isDark;

  const ImagePreviewScreen({
    super.key,
    required this.imageUrls,
    this.initialIndex = 0,
    this.isDark = false,
  });

  @override
  State<ImagePreviewScreen> createState() => _ImagePreviewScreenState();
}

class _ImagePreviewScreenState extends State<ImagePreviewScreen> {
  late PageController _pageController;
  late int _currentIndex;
  final TransformationController _transformationController =
      TransformationController();

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 图片预览区域
          PageView.builder(
            controller: _pageController,
            itemCount: widget.imageUrls.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
              // 重置缩放
              _transformationController.value = Matrix4.identity();
            },
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                onLongPress: () => _shareImage(widget.imageUrls[index]),
                child: InteractiveViewer(
                  transformationController: _transformationController,
                  minScale: 0.5, // 最小缩放比例
                  maxScale: 3.0, // 最大缩放比例
                  child: Center(
                    child: CachedNetworkImage(
                      imageUrl: widget.imageUrls[index],
                      fit: BoxFit.contain,
                      placeholder: (context, url) => Container(
                        color: Colors.black,
                        child: Center(
                          child: LoadingIndicator(
                            size: 75.spx,
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[900],
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.broken_image,
                                size: 64.spx,
                                color: Colors.grey[600],
                              ),
                              SizedBox(height: 16.spx),
                              Text(
                                'image_load_failed'.tr,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 16.spx,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),

          // 顶部状态栏和关闭按钮
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: MediaQuery.of(context).padding.top + 56.spx,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.7),
                    Colors.transparent,
                  ],
                ),
              ),
              child: SafeArea(
                child: Row(
                  children: [
                    // 关闭按钮
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 24.spx,
                      ),
                    ),
                    const Spacer(),
                    // 图片计数
                    if (widget.imageUrls.length > 1)
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(16.spx),
                        ),
                        child: Text(
                          '${_currentIndex + 1}/${widget.imageUrls.length}',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.spx,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    SizedBox(width: 16.spx),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示图片操作面板
  void _showImageActionSheet(BuildContext context, String imageUrl) {
    // 添加触觉反馈
    HapticFeedback.mediumImpact();

    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        actions: <CupertinoActionSheetAction>[
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              _saveImageToGallery(imageUrl);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  CupertinoIcons.photo_on_rectangle,
                  size: 20.spx,
                  color: CupertinoColors.systemBlue,
                ),
                SizedBox(width: 8.spx),
                Text(
                  'save_to_gallery'.tr,
                  style: TextStyle(
                    fontSize: 16.spx,
                    color: CupertinoColors.systemBlue,
                  ),
                ),
              ],
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              _shareImage(imageUrl);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  CupertinoIcons.share,
                  size: 20.spx,
                  color: CupertinoColors.systemBlue,
                ),
                SizedBox(width: 8.spx),
                Text(
                  'share_image'.tr,
                  style: TextStyle(
                    fontSize: 16.spx,
                    color: CupertinoColors.systemBlue,
                  ),
                ),
              ],
            ),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () {
            Navigator.pop(context);
          },
          isDefaultAction: true,
          child: Text(
            'cancel'.tr,
            style: TextStyle(
              fontSize: 16.spx,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  /// 保存图片到相册
  Future<void> _saveImageToGallery(String imageUrl) async {
    try {
      // 显示加载提示
      _showToast('saving_image'.tr, isLoading: true);

      // 检查权限
      bool hasPermission = await _checkStoragePermission();
      if (!hasPermission) {
        _showToast('storage_permission_denied'.tr, isError: true);
        return;
      }

      // 下载图片
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        final Uint8List bytes = response.bodyBytes;

        // 保存到相册
        final result = await ImageGallerySaver.saveImage(
          bytes,
          quality: 100,
          name: "subfeeds_image_${DateTime.now().millisecondsSinceEpoch}",
        );

        if (result['isSuccess'] == true) {
          _showToast('image_saved_successfully'.tr, isSuccess: true);
          // 添加成功反馈
          HapticFeedback.lightImpact();
        } else {
          _showToast('failed_to_save_image'.tr, isError: true);
        }
      } else {
        _showToast('failed_to_download_image'.tr, isError: true);
      }
    } catch (e) {
      debugPrint('保存图片失败: $e');
      _showToast('failed_to_save_image'.tr, isError: true);
      HapticFeedback.heavyImpact();
    }
  }

  /// 分享图片
  Future<void> _shareImage(String imageUrl) async {
    try {
      // 下载图片
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        final Uint8List bytes = response.bodyBytes;

        // 创建临时文件
        final tempDir = Directory.systemTemp;
        final file = await File(
                '${tempDir.path}/shared_image_${DateTime.now().millisecondsSinceEpoch}.jpg')
            .create();
        await file.writeAsBytes(bytes);

        // 分享文件
        await Share.shareXFiles(
          [XFile(file.path)],
          text: 'shared_from_subfeeds'.tr,
        );

        HapticFeedback.lightImpact();
      } else {
        _showToast('failed_to_download_image'.tr, isError: true);
      }
    } catch (e) {
      debugPrint('分享图片失败: $e');
      _showToast('failed_to_share_image'.tr, isError: true);
      HapticFeedback.heavyImpact();
    }
  }

  /// 检查存储权限
  Future<bool> _checkStoragePermission() async {
    if (Platform.isAndroid) {
      // Android 13+ 使用新的权限模型
      if (await Permission.photos.isGranted) {
        return true;
      }

      final status = await Permission.photos.request();
      if (status.isGranted) {
        return true;
      }

      // 如果新权限被拒绝，尝试旧的存储权限
      if (await Permission.storage.isGranted) {
        return true;
      }

      final storageStatus = await Permission.storage.request();
      return storageStatus.isGranted;
    } else if (Platform.isIOS) {
      // iOS 使用照片权限
      final status = await Permission.photos.request();
      return status.isGranted;
    }

    return true; // 其他平台默认允许
  }

  /// 显示Toast提示
  void _showToast(String message,
      {bool isError = false, bool isSuccess = false, bool isLoading = false}) {
    Color backgroundColor;
    Color textColor = Colors.white;

    if (isError) {
      backgroundColor = CupertinoColors.systemRed;
    } else if (isSuccess) {
      backgroundColor = CupertinoColors.systemGreen;
    } else if (isLoading) {
      backgroundColor = CupertinoColors.systemGrey;
    } else {
      backgroundColor = CupertinoColors.systemBlue;
    }

    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
      timeInSecForIosWeb: 2,
      backgroundColor: backgroundColor,
      textColor: textColor,
      fontSize: 16.0,
    );
  }
}
