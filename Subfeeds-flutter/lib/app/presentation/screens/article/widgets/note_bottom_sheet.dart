import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/article/article_controller.dart';

class NoteBottomSheet {
  final BuildContext context;

  /// 文章控制器
  final ArticleController controller;

  /// 选择文本
  final String selectedText;

  /// 是否为编辑模式
  final bool isEdit;
  const NoteBottomSheet(
      {Key? key,
      required this.context,
      required this.controller,
      required this.selectedText,
      this.isEdit = false});

  /// 从mark标签中获取笔记内容
  String? _getMarkContent(String text) {
    final RegExp markRegex = RegExp(r'<mark[^>]*data-content="([^"]*)"[^>]*>');
    final match = markRegex.firstMatch(text);
    return match?.group(1);
  }

  /// 从mark标签中获取高亮颜色
  String? _getMarkColor(String text) {
    final RegExp markRegex = RegExp(r'<mark[^>]*data-color="([^"]*)"[^>]*>');
    final match = markRegex.firstMatch(text);
    return match?.group(1);
  }

  /// 从带mark标签的文本中获取纯文本
  String _getPlainText(String text) {
    return text.replaceAll(RegExp(r'<mark[^>]*>|</mark>'), '');
  }

  /// 显示笔记底部弹窗
  void show() {
    if (selectedText.isEmpty) return;

    final TextEditingController noteController = TextEditingController();
    final FocusNode noteFocusNode = FocusNode();
    bool canSave = false;

    // 如果是编辑模式，从mark标签中获取笔记内容和颜色
    if (isEdit) {
      final content = _getMarkContent(selectedText);
      final color = _getMarkColor(selectedText);

      if (content != null) {
        noteController.text = content;
        canSave = true;
      }

      if (color != null) {
        controller.setNoteHighlightColor(color);
      }
    }

    showDialog(
      context: Get.context!,
      barrierDismissible: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // 计算dialog的位置
            final screenHeight = MediaQuery.of(context).size.height;
            final screenWidth = MediaQuery.of(context).size.width;
            final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

            // 计算dialog的实际中心位置（考虑键盘高度）
            final availableHeight = screenHeight - keyboardHeight;
            final dialogCenterY = availableHeight * 0.5;

            // 颜色选择器位置：dialog上方4.spx
            final colorPickerY =
                dialogCenterY - 140 - 4 - 48; // 300是dialog大概高度的一半

            return Stack(
              children: [
                // 颜色选择器 - 悬浮在dialog上方
                Positioned(
                  top: colorPickerY,
                  left: screenWidth * 0.05,
                  right: screenWidth * 0.05,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 8),
                      decoration: BoxDecoration(
                        color: controller.backgroundColor.value,
                        borderRadius: BorderRadius.circular(12.spx),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: controller.availableNoteColors
                            .map((color) => GestureDetector(
                                  onTap: () {
                                    controller.setNoteHighlightColor(color);
                                    setState(() {});
                                  },
                                  child: Container(
                                    width: 20.spx,
                                    height: 20.spx,
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 4.spx),
                                    decoration: BoxDecoration(
                                      color: HexColor.fromHex(color),
                                      shape: BoxShape.circle,
                                    ),
                                    child: controller.selectedNoteColor.value ==
                                            color
                                        ? Center(
                                            child: Container(
                                              width: 10.spx,
                                              height: 10.spx,
                                              decoration: BoxDecoration(
                                                color: controller
                                                        .isDarkMode.value
                                                    ? const Color(0xFF3b3b3b)
                                                    : const Color(0xFFffffff),
                                                shape: BoxShape.circle,
                                              ),
                                            ),
                                          )
                                        : null,
                                  ),
                                ))
                            .toList(),
                      ),
                    ),
                  ),
                ),
                // 主dialog
                Center(
                  child: Dialog(
                      insetPadding: EdgeInsets.symmetric(horizontal: 20.spx),
                      backgroundColor: controller.backgroundColor.value,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Container(
                        child: Padding(
                          padding: EdgeInsets.all(12.spx),
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 所选文本展示区域
                                Text(
                                  _getPlainText(selectedText),
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        fontSize: 14.spx,
                                        fontWeight: FontWeight.w500,
                                        color: controller.getTextColor(),
                                      ),
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: 8.spx),

                                // 笔记输入框
                                TextField(
                                  controller: noteController,
                                  focusNode: noteFocusNode,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: controller.getTextColor(),
                                        fontSize: 13.spx,
                                        fontWeight: FontWeight.w400,
                                      ),
                                  decoration: InputDecoration(
                                    hintText: '在此输入笔记内容...',
                                    border: OutlineInputBorder(
                                      borderRadius:
                                          BorderRadius.circular(8.spx),
                                    ),
                                    fillColor: controller.backgroundColor.value
                                        .withOpacity(0.5),
                                    contentPadding: EdgeInsets.all(12.spx),
                                  ),
                                  maxLines: 3,

                                  autofocus: !isEdit, // 编辑模式下不自动获取焦点
                                  onChanged: (value) {
                                    setState(() {
                                      canSave = value.trim().isNotEmpty;
                                    });
                                  },
                                ),
                                SizedBox(height: 8.spx),

                                // 操作栏
                                Row(
                                  children: [
                                    // 编辑/保存按钮
                                    ElevatedButton(
                                      onPressed: canSave
                                          ? () async {
                                              final success = await controller
                                                  .addOrUpdateNote(
                                                selectedText,
                                                noteController.text,
                                                isEdit ? 2 : 0, // 2表示修改，0表示新增
                                              );
                                              if (success) {
                                                Navigator.pop(context);
                                                // _clearSelectionSafely(context);
                                              }
                                            }
                                          : null,
                                      style: ElevatedButton.styleFrom(
                                        padding: EdgeInsets.zero,
                                        backgroundColor: Theme.of(context)
                                            .colorScheme
                                            .primary,
                                        disabledBackgroundColor:
                                            const Color(0xFF959ed5),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(21.spx),
                                        ),
                                        minimumSize: Size(100.spx, 36.spx),
                                        maximumSize: Size(100.spx, 36.spx),
                                      ),
                                      child: Text(
                                        isEdit ? 'Edit' : 'save'.tr,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium
                                            ?.copyWith(
                                              color: Colors.white,
                                              fontSize: 14.spx,
                                              fontWeight: FontWeight.w600,
                                            ),
                                      ),
                                    ),
                                    const Spacer(),

                                    // 复制按钮
                                    IconButton(
                                      onPressed: () {
                                        Clipboard.setData(ClipboardData(
                                            text: _getPlainText(selectedText)));
                                        Get.snackbar(
                                          'success'.tr,
                                          'text_copied'.tr,
                                          snackPosition: SnackPosition.BOTTOM,
                                          duration: const Duration(seconds: 1),
                                        );
                                      },
                                      icon: SvgPicture.asset(
                                        'assets/icons/note_copy.svg',
                                        width: 20.spx,
                                        height: 20.spx,
                                        colorFilter: const ColorFilter.mode(
                                          Color(0xFF73839d),
                                          BlendMode.srcIn,
                                        ),
                                      ),
                                    ),

                                    if (isEdit) ...[
                                      // 删除按钮（仅在编辑模式显示）
                                      IconButton(
                                        onPressed: () async {
                                          final confirmed = await Get.dialog(
                                            Dialog(
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(16),
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    SvgPicture.asset(
                                                      'assets/feeds/feeds_error.svg',
                                                      width: 50,
                                                      height: 50,
                                                    ),
                                                    const SizedBox(height: 16),
                                                    Text(
                                                      'confirm_delete_note'.tr,
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .bodySmall
                                                          ?.copyWith(
                                                            fontSize: 14,
                                                          ),
                                                    ),
                                                    const SizedBox(height: 16),
                                                    Row(
                                                      children: [
                                                        Expanded(
                                                          child: TextButton(
                                                            onPressed: () =>
                                                                Get.back(
                                                                    result:
                                                                        false),
                                                            style: TextButton
                                                                .styleFrom(
                                                              backgroundColor:
                                                                  const Color(
                                                                      0xFFb9c0eb),
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            8),
                                                              ),
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                vertical: 12,
                                                              ),
                                                            ),
                                                            child: Text(
                                                              'cancel'.tr,
                                                              style: Theme.of(
                                                                      context)
                                                                  .textTheme
                                                                  .bodyMedium
                                                                  ?.copyWith(
                                                                    color: Colors
                                                                        .white,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  ),
                                                            ),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            width: 16),
                                                        Expanded(
                                                          child: TextButton(
                                                            onPressed: () =>
                                                                Get.back(
                                                                    result:
                                                                        true),
                                                            style: TextButton
                                                                .styleFrom(
                                                              backgroundColor:
                                                                  const Color(
                                                                      0xFFd0452f),
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            8),
                                                              ),
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                vertical: 12,
                                                              ),
                                                            ),
                                                            child: Text(
                                                              'delete'.tr,
                                                              style: Theme.of(
                                                                      context)
                                                                  .textTheme
                                                                  .bodyMedium
                                                                  ?.copyWith(
                                                                    color: Colors
                                                                        .white,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          );

                                          if (confirmed == true) {
                                            final success = await controller
                                                .addOrUpdateNote(
                                              selectedText,
                                              '',
                                              1, // 1表示删除
                                            );
                                            if (success) {
                                              Navigator.pop(context);
                                              // _clearSelectionSafely(context);
                                            }
                                          }
                                        },
                                        icon: SvgPicture.asset(
                                          'assets/icons/note_del.svg',
                                          width: 20.spx,
                                          height: 20.spx,
                                          colorFilter: const ColorFilter.mode(
                                            Color(0xFFd0452f),
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      )),
                ),
              ],
            );
          },
        );
      },
    ).then((_) {
      noteController.dispose();
      noteFocusNode.dispose();
    });
  }
}

/// 扩展类用于处理十六进制颜色转换
extension HexColor on Color {
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 7) {
      buffer.write('ff');
      buffer.write(hexString.replaceFirst('#', ''));
    } else {
      buffer.write(hexString.replaceFirst('#', ''));
    }
    return Color(int.parse(buffer.toString(), radix: 16));
  }
}
