import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 聊天输入框组件
class ChatInputField extends StatefulWidget {
  final Function(String) onSendMessage;
  final bool isLoading;
  final String hintText;
  final bool isDark;
  const ChatInputField({
    super.key,
    required this.onSendMessage,
    this.isLoading = false,
    this.hintText = '',
    this.isDark = false,
  });

  @override
  State<ChatInputField> createState() => _ChatInputFieldState();
}

class _ChatInputFieldState extends State<ChatInputField> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 发送消息
  void _sendMessage() {
    final text = _controller.text.trim();
    if (text.isNotEmpty && !widget.isLoading) {
      widget.onSendMessage(text);
      _controller.clear();
    } else if (text.isEmpty) {
      // _showEmptyMessageWarning();
    }
  }

  /// 显示空消息警告
  void _showEmptyMessageWarning() {
    Get.snackbar(
      'warning'.tr,
      'empty_message'.tr,
      snackPosition: SnackPosition.TOP,
      backgroundColor: const Color(0xFF161617),
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 16.spx,
        right: 16.spx,
        bottom: 4.spx,
        top: 8.spx,
      ),
      decoration: BoxDecoration(
        color:
            widget.isDark ? const Color(0xFF444444) : const Color(0xFFf7faff),
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: widget.isDark
                      ? const Color(0xFF444444)
                      : const Color(0xFFf7faff),
                  borderRadius: BorderRadius.circular(50.spx),
                ),
                child: TextField(
                  style: TextStyle(
                    fontSize: 14.spx,
                    fontFamily: 'PingFang SC',
                    fontWeight: FontWeight.w500,
                    color: widget.isDark ? Colors.white : Colors.black,
                  ),
                  controller: _controller,
                  focusNode: _focusNode,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: widget.isDark
                        ? const Color(0xFF565656)
                        : const Color(0xFFFFFFFF),
                    hintText: widget.hintText.isNotEmpty
                        ? widget.hintText
                        : 'article_ask_ai_hint'.tr,
                    hintStyle: TextStyle(
                      fontSize: 14.spx,
                      color: widget.isDark
                          ? const Color(0xFFD1D1D1)
                          : const Color(0xFF999999),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(40.spx),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(40.spx),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.only(
                      left: 20,
                      right: 20,
                      top: 12,
                      bottom: 12,
                    ),
                    suffixIcon: widget.isLoading
                        ? null
                        : IconButton(
                            padding: EdgeInsets.only(
                              left: 20.spx,
                              right: 20.spx,
                            ),
                            onPressed: () {
                              widget.isLoading ? null : _sendMessage();
                            },
                            icon: SvgPicture.asset(
                              'assets/feeds/send.svg',
                              width: 22.spx,
                              height: 22.spx,
                              colorFilter: ColorFilter.mode(
                                widget.isDark
                                    ? Colors.white
                                    : Theme.of(context).primaryColor,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                  ),
                  textInputAction: TextInputAction.send,
                  minLines: 1,
                  maxLines: 4,
                  onSubmitted: (_) => _sendMessage(),
                  enabled: !widget.isLoading,
                ),
              ),
            ),
            const SizedBox(width: 12),
            _buildSendButton(),
          ],
        ),
      ),
    );
  }

  /// 构建发送按钮
  Widget _buildSendButton() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: widget.isDark ? const Color(0xFF565656) : Colors.white,
        borderRadius: BorderRadius.circular(24),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(24),
          onTap: widget.isLoading ? null : _sendMessage,
          child: Center(
            child: widget.isLoading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        widget.isDark
                            ? Colors.white
                            : Theme.of(context).primaryColor,
                      ),
                    ),
                  )
                : SvgPicture.asset(
                    'assets/icons/ai_circle.svg',
                    width: 20.spx,
                    height: 20.spx,
                    colorFilter: ColorFilter.mode(
                      widget.isDark
                          ? Colors.white
                          : Theme.of(context).primaryColor,
                      BlendMode.srcIn,
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}

/// 简化版聊天输入框（用于快速集成）
class SimpleChatInput extends StatelessWidget {
  final Function(String) onSendMessage;
  final bool isLoading;
  final String? hintText;
  final TextEditingController? controller;

  const SimpleChatInput({
    super.key,
    required this.onSendMessage,
    this.isLoading = false,
    this.hintText,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: hintText ?? 'Type a message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF2A2D3E)
                    : const Color(0xFFEEEEEE),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              textInputAction: TextInputAction.send,
              minLines: 1,
              maxLines: 3,
              onSubmitted: (text) {
                if (text.trim().isNotEmpty && !isLoading) {
                  onSendMessage(text.trim());
                  controller?.clear();
                }
              },
              enabled: !isLoading,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: isLoading
                ? null
                : () {
                    final text = controller?.text.trim() ?? '';
                    if (text.isNotEmpty) {
                      onSendMessage(text);
                      controller?.clear();
                    }
                  },
            icon: isLoading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Icon(
                    Icons.send,
                    color: Theme.of(context).primaryColor,
                  ),
          ),
        ],
      ),
    );
  }
}
