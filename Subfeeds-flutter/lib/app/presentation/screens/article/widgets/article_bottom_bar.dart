import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/article/article_controller.dart';
import 'package:subfeeds/app/presentation/screens/article/widgets/note_bottom_sheet.dart';
import 'package:subfeeds/app/presentation/screens/article/widgets/share_menu_sheet.dart';

class ArticleBottomBar extends GetView<ArticleController> {
  ArticleBottomBar({super.key});
  // 添加OverlayEntry成员变量来管理菜单
  OverlayEntry? _selectionMenuOverlay;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Gradient border
        Container(
          height: 1,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: Theme.of(Get.context!).brightness == Brightness.dark
                  ? [
                      const Color(0x00EAEAEA), // #EAEAEA00
                      const Color(0xFFEAEAEA), // #EAEAEA
                      const Color(0x00EAEAEA), // #EAEAEA00
                    ]
                  : [
                      const Color(0x50EAEAEA), // #EAEAEA50
                      const Color(0xFFEAEAEA), // #EAEAEA
                      const Color(0x50EAEAEA), // #EAEAEA50
                    ],
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(
              left: 0.spx, right: 16.spx, top: 10.spx, bottom: 20.spx),
          child: Row(
            children: [
              _buildBottomBarButton(
                icon: Obx(() => controller.isBookmarked.value
                    ? SvgPicture.asset(
                        'assets/icons/star-fill.svg',
                        width: 22.spx,
                        height: 22.spx,
                      )
                    : SvgPicture.asset(
                        'assets/feeds/menu_star.svg',
                        width: 22.spx,
                        height: 22.spx,
                        colorFilter: ColorFilter.mode(
                          controller.getTextColor(),
                          BlendMode.srcIn,
                        ),
                      )),
                onTap: controller.toggleBookmark,
              ),
              _buildBottomBarButton(
                icon: SvgPicture.asset(
                  'assets/feeds/note.svg',
                  width: 22.spx,
                  height: 22.spx,
                  colorFilter: ColorFilter.mode(
                    controller.getTextColor(),
                    BlendMode.srcIn,
                  ),
                ),
                onTap: () {
                  // 显示笔记列表
                  _showNoteListBottomSheet();
                },
              ),
              _buildBottomBarButton(
                icon: Obx(() => controller.isReadLater.value
                    ? SvgPicture.asset(
                        'assets/icons/readlater-fill.svg',
                        width: 20.spx,
                        height: 20.spx,
                      )
                    : SvgPicture.asset(
                        'assets/icons/readlater-border.svg',
                        width: 22.spx,
                        height: 22.spx,
                        colorFilter: ColorFilter.mode(
                          controller.getTextColor(),
                          BlendMode.srcIn,
                        ),
                      )),
                onTap: controller.toggleReadLater,
              ),

              _buildBottomBarButton(
                icon: SvgPicture.asset(
                  'assets/icons/theme.svg',
                  width: 22.spx,
                  height: 22.spx,
                  colorFilter: ColorFilter.mode(
                    controller.getTextColor(),
                    BlendMode.srcIn,
                  ),
                ),
                onTap: () {
                  // 显示主题设置菜单
                  _showThemeSettingsMenu(context);
                },
              ),
              // 添加订阅按钮，只在isHome为true时显示
              if (controller.article.value['isHome'] == true)
                _buildSubscriptionButton(),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建底部栏按钮
  Widget _buildBottomBarButton({
    required Widget icon,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 6.spx),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconTheme(
                data: IconThemeData(
                  color: controller.getTextColor(),
                ),
                child: icon,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建订阅按钮（文字按钮样式）
  Widget _buildSubscriptionButton() {
    return Expanded(
      child: Obx(
        () => ElevatedButton(
          onPressed: () => controller.toggleSubscription(),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(Get.context!).primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24.spx),
            ),
            elevation: 1,
            padding: EdgeInsets.symmetric(horizontal: 8.spx, vertical: 8.spx),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (controller.isSubscribed.value)
                Icon(
                  Icons.remove,
                  size: 16.spx,
                  color: controller.getTextColor(),
                ),
              if (!controller.isSubscribed.value)
                Icon(
                  Icons.add,
                  color: Colors.white,
                  size: 16.spx,
                ),
              SizedBox(width: 2.spx),
              Text(
                controller.isSubscribed.value ? 'cancel'.tr : 'add'.tr,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 11.spx,
                  fontFamily: 'Roboto',
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 移除选择菜单
  void _removeSelectionMenu() {
    _selectionMenuOverlay?.remove();
    _selectionMenuOverlay = null;
  }

  // 安全地清除选择，避免异常
  void _clearSelectionSafely(BuildContext context) {
    // 移除菜单
    _removeSelectionMenu();

    // 避免在用户正在调整选择时清除选择
    if (controller.selectedText.value.isEmpty) {
      return; // 如果没有选择内容，直接返回
    }

    // 确保清除选择状态
    controller.isTextSelected.value = false;
    controller.selectedText.value = '';
    controller.lastTapPosition.value = Offset.zero;

    try {
      // 通过解除焦点尝试清除选择
      FocusManager.instance.primaryFocus?.unfocus();
    } catch (e) {
      print('安全清除选中文本时发生错误: $e');
    }

    // 更新UI状态
    controller.update();

    // 延迟一点时间再次尝试清除选择
    Future.delayed(const Duration(milliseconds: 200), () {
      try {
        // 再次尝试获取当前焦点并清除
        FocusManager.instance.primaryFocus?.unfocus();

        // 强制触发界面刷新
        controller.isTextSelected.value = false;
        controller.update();
      } catch (e) {
        print('延迟清除文本选择时出错: $e');
      }
    });
  }

  /// 显示主题设置菜单
  void _showThemeSettingsMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: controller.backgroundColor.value,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 字体大小设置
                  Text(
                    'article_font_size'.tr,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodySmall?.color,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),

                  // 字体大小滑块
                  Obx(
                    () => Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              'Aa',
                              style: TextStyle(
                                fontSize: 14,
                                color: controller.getTextColor(),
                              ),
                            ),
                            Expanded(
                              child: SliderTheme(
                                data: SliderTheme.of(context).copyWith(
                                  activeTrackColor:
                                      Theme.of(context).primaryColor,
                                  inactiveTrackColor: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.2),
                                  thumbColor: Theme.of(context).primaryColor,
                                  overlayColor: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.1),
                                  trackHeight: 2.0,
                                ),
                                child: Slider(
                                  value: controller.fontSize.value,
                                  min: 12,
                                  max: 24,
                                  divisions: 4,
                                  onChanged: controller.setFontSize,
                                ),
                              ),
                            ),
                            Text(
                              'Aa',
                              style: TextStyle(
                                fontSize: 24,
                                color: controller.getTextColor(),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),
                  // 字体选择
                  Text(
                    'article_font_family'.tr,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodySmall?.color,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(height: 10),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildFontOption(context, 'Roboto'),
                        _buildFontOption(context, 'SourceSerif4'),
                        _buildFontOption(context, 'Montserrat'),
                        _buildFontOption(context, 'Poppins'),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 行高设置

                  Text(
                    'article_line_height'.tr,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodySmall?.color,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildLineHeightOption(
                          context, 1.2, 'article_compact'.tr),
                      _buildLineHeightOption(
                          context, 1.5, 'article_standard'.tr),
                      _buildLineHeightOption(
                          context, 1.8, 'article_comfortable'.tr),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // 主题样式设置
                  Text(
                    'article_theme'.tr,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodySmall?.color,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: controller.themeColors
                          .map(
                              (color) => _buildThemeColorOption(context, color))
                          .toList(),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// 构建主题样式选项
  Widget _buildThemeColorOption(BuildContext context, Color color) {
    return Obx(
      () => GestureDetector(
        onTap: () => controller.setBackgroundColor(color),
        child: Container(
          width: 40.spx,
          height: 40.spx,
          margin: const EdgeInsets.only(right: 12),
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(
              color: controller.backgroundColor.value == color
                  ? Theme.of(context).primaryColor
                  : Colors.transparent,
              width: 2,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建行高选项
  Widget _buildLineHeightOption(
      BuildContext context, double height, String label) {
    return Obx(
      () => Padding(
        padding: const EdgeInsets.only(right: 12.0),
        child: InkWell(
          onTap: () => controller.setLineHeight(height),
          child: Container(
            width: 90.spx,
            height: 40.spx,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: controller.lineHeight.value == height
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : const Color(0xFFeff1f4),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: controller.lineHeight.value == height
                    ? Theme.of(context).primaryColor
                    : Colors.transparent,
                width: 1.5,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 根据不同模式显示不同数量的横线
                ...List.generate(
                  height == 1.2
                      ? 4
                      : height == 1.5
                          ? 3
                          : 2,
                  (index) => Container(
                    width: 20.spx,
                    height: 2,
                    margin: const EdgeInsets.symmetric(vertical: 1.5),
                    decoration: BoxDecoration(
                      color: controller.lineHeight.value == height
                          ? Theme.of(context).primaryColor
                          : const Color(0xFFA9A9A9),
                      borderRadius: BorderRadius.circular(1),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建字体选项
  Widget _buildFontOption(BuildContext context, String fontFamily) {
    return Obx(
      () => Padding(
        padding: const EdgeInsets.only(right: 12.0),
        child: InkWell(
          onTap: () => controller.setFontFamily(fontFamily),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 7),
            decoration: BoxDecoration(
              color: controller.fontFamily.value == fontFamily
                  ? Colors.transparent
                  : const Color(0xFFeff1f4),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: controller.fontFamily.value == fontFamily
                    ? Theme.of(context).primaryColor
                    : Colors.transparent,
                width: 1.5,
              ),
            ),
            child: Text(
              fontFamily,
              style: TextStyle(
                fontFamily: fontFamily,
                fontSize: 15,
                color: controller.fontFamily.value == fontFamily
                    ? Theme.of(context).primaryColor
                    : const Color(0xFF333333),
                fontWeight: controller.fontFamily.value == fontFamily
                    ? FontWeight.w400
                    : FontWeight.normal,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 显示笔记列表底部弹窗
  void _showNoteListBottomSheet() {
    final List<Map<String, dynamic>> notes =
        controller.getAllNotesFromContent();

    if (notes.isEmpty) {
      Get.snackbar(
        'notice'.tr,
        'no_notes_found'.tr,
        icon: const Icon(Icons.info_outline, color: Colors.orange),
        snackPosition: SnackPosition.TOP,
        backgroundColor: const Color(0xFF161617),
        colorText: Colors.white,
      );

      return;
    }

    showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: controller.backgroundColor.value,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: Text(
                      'notes_list'.tr,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: controller.getTextColor(),
                          ),
                    ),
                  ),
                  ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.5,
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: notes.length,
                      itemBuilder: (context, index) {
                        final note = notes[index];
                        final highlightedText = note['highlightedText'] ?? '';
                        final noteContent = note['noteContent'] ?? '';
                        final color = note['color'] ?? '#ffea9d';

                        return InkWell(
                          onTap: () {
                            Navigator.pop(context);
                            // 显示编辑笔记弹窗
                            NoteBottomSheet(
                              context: context,
                              controller: controller,
                              selectedText: note['fullMarkText'],
                              isEdit: true,
                            ).show();
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 高亮文本
                                Container(
                                  padding: const EdgeInsets.all(8.0),
                                  decoration: BoxDecoration(
                                    color: HexColor.fromHex(color)
                                        .withOpacity(0.3),
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  child: Text(
                                    highlightedText,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          color: controller.getTextColor(),
                                        ),
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                const SizedBox(height: 8.0),
                                // 笔记内容
                                Text(
                                  noteContent,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: controller.getTextColor(),
                                      ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                // 日期和操作按钮
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      note['date'] ?? '',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            color: Colors.grey,
                                          ),
                                    ),
                                    Row(
                                      children: [
                                        // 编辑按钮
                                        IconButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                            // 显示编辑笔记弹窗
                                            NoteBottomSheet(
                                              context: context,
                                              controller: controller,
                                              selectedText:
                                                  note['fullMarkText'],
                                              isEdit: true,
                                            ).show();
                                          },
                                          icon: Icon(
                                            Icons.edit,
                                            size: 20,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                          ),
                                        ),
                                        // 删除按钮
                                        IconButton(
                                          onPressed: () async {
                                            final confirmed = await Get.dialog(
                                              Dialog(
                                                backgroundColor: Theme.of(
                                                                context)
                                                            .brightness ==
                                                        Brightness.light
                                                    ? const Color(0xFFF7FAFF)
                                                    : const Color(0xFF444444),
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.all(16),
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      SvgPicture.asset(
                                                        'assets/feeds/feeds_error.svg',
                                                        width: 50,
                                                        height: 50,
                                                      ),
                                                      const SizedBox(
                                                          height: 16),
                                                      Text(
                                                        'confirm_delete_note'
                                                            .tr,
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodySmall
                                                            ?.copyWith(
                                                              fontSize: 14,
                                                            ),
                                                      ),
                                                      const SizedBox(
                                                          height: 16),
                                                      Row(
                                                        children: [
                                                          Expanded(
                                                            child: TextButton(
                                                              onPressed: () =>
                                                                  Get.back(
                                                                      result:
                                                                          false),
                                                              style: TextButton
                                                                  .styleFrom(
                                                                backgroundColor:
                                                                    const Color(
                                                                        0xFFb9c0eb),
                                                                shape:
                                                                    RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              8),
                                                                ),
                                                                padding:
                                                                    const EdgeInsets
                                                                        .symmetric(
                                                                  vertical: 12,
                                                                ),
                                                              ),
                                                              child: Text(
                                                                'cancel'.tr,
                                                                style: Theme.of(
                                                                        context)
                                                                    .textTheme
                                                                    .bodyMedium
                                                                    ?.copyWith(
                                                                      color: Colors
                                                                          .white,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                              width: 16),
                                                          Expanded(
                                                            child: TextButton(
                                                              onPressed: () =>
                                                                  Get.back(
                                                                      result:
                                                                          true),
                                                              style: TextButton
                                                                  .styleFrom(
                                                                backgroundColor:
                                                                    const Color(
                                                                        0xFFd0452f),
                                                                shape:
                                                                    RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              8),
                                                                ),
                                                                padding:
                                                                    const EdgeInsets
                                                                        .symmetric(
                                                                  vertical: 12,
                                                                ),
                                                              ),
                                                              child: Text(
                                                                'delete'.tr,
                                                                style: Theme.of(
                                                                        context)
                                                                    .textTheme
                                                                    .bodyMedium
                                                                    ?.copyWith(
                                                                      color: Colors
                                                                          .white,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            );

                                            if (confirmed == true) {
                                              final success = await controller
                                                  .addOrUpdateNote(
                                                note['fullMarkText'],
                                                '',
                                                1, // 1表示删除
                                              );
                                              if (success) {
                                                Navigator.pop(context);
                                                // _clearSelectionSafely(context);
                                              }
                                            }
                                          },
                                          icon: SvgPicture.asset(
                                            'assets/icons/delete.svg',
                                            width: 20,
                                            height: 20,
                                            colorFilter: const ColorFilter.mode(
                                              Colors.red,
                                              BlendMode.srcIn,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}

/// 扩展类用于处理十六进制颜色转换
extension HexColor on Color {
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 7) {
      buffer.write('ff');
      buffer.write(hexString.replaceFirst('#', ''));
    } else {
      buffer.write(hexString.replaceFirst('#', ''));
    }
    return Color(int.parse(buffer.toString(), radix: 16));
  }
}
