import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import 'package:subfeeds/app/presentation/screens/article/article_controller.dart';
import '../../../../data/models/chat_conversation.dart';
import '../../../../data/services/chat_history_service.dart';
import 'chat_dialog.dart';

class AskAiDialog {
  final String selectedText;
  final ArticleController controller;
  final BuildContext context;

  const AskAiDialog({
    Key? key,
    required this.context,
    required this.selectedText,
    required this.controller,
  });

  /// 显示AI对话框
  void show() {
    // 检查所选文本是否为空
    if (selectedText.trim().isEmpty) {
      Get.snackbar(
        'error'.tr,
        'no_text_selected'.tr,
        snackPosition: SnackPosition.TOP,
        backgroundColor: const Color(0xFF161617),
        colorText: Colors.white,
      );
      return;
    }

    debugPrint('选中的文本: "$selectedText"');

    // 创建对话控制器
    final dialogController = AskAiDialogController(
      selectedText: selectedText,
      articleController: controller,
    );

    // 显示对话框
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _AskAiDialogView(
        controller: dialogController,
        articleController: controller,
      ),
    );
  }
}

/// AI对话控制器
class AskAiDialogController extends GetxController {
  final String selectedText;
  final ArticleController articleController;

  AskAiDialogController({
    required this.selectedText,
    required this.articleController,
  });

  // 状态管理
  final isLoading = false.obs;
  final messages = <ChatMessage>[].obs;
  final scrollController = ScrollController();
  final isComplete = false.obs;

  // 历史记录相关
  final ChatHistoryService _historyService = ChatHistoryService();
  final Uuid _uuid = const Uuid();
  String? _currentConversationId;
  StreamSubscription<String>? _currentStreamSubscription; // 当前的流订阅
  Completer<void>? _currentCompleter; // 当前的完成器
  bool _isUserStopped = false; // 是否是用户主动停止

  // 历史对话的文章内容（当加载历史对话时使用）
  String? _historyArticleContent;
  String? _historyArticleTitle;
  String? _historyArticleUrl;
  @override
  void onInit() {
    super.onInit();
    debugPrint('AskAiDialogController onInit 被调用，选中文本: "$selectedText"');
    // 清理重复的历史记录（一次性迁移）
    _cleanupDuplicateRecords();
    // 初始化对话
    _initializeConversation();
  }

  /// 清理重复的历史记录
  Future<void> _cleanupDuplicateRecords() async {
    try {
      await _historyService.cleanupDuplicateConversations();
      debugPrint('历史记录清理完成');
    } catch (e) {
      debugPrint('清理历史记录失败: $e');
    }
  }

  /// 初始化对话（检查是否有现有对话）
  Future<void> _initializeConversation() async {
    try {
      final articleId = articleController.article.value['id'] ?? '';
      final articleLink = articleController.article.value['link'] ?? '';

      debugPrint('初始化对话 - 文章ID: $articleId');
      debugPrint('初始化对话 - 文章Link: $articleLink');

      // 首先尝试用ID查找现有对话
      ChatConversation? existingConversation =
          await _historyService.getConversationByArticleUrl(articleId);

      // 如果用ID找不到，尝试用link查找（兼容旧数据）
      if (existingConversation == null && articleLink.isNotEmpty) {
        debugPrint('用ID未找到对话，尝试用Link查找');
        existingConversation =
            await _historyService.getConversationByArticleUrl(articleLink);

        // 如果用link找到了，需要更新为使用ID
        if (existingConversation != null) {
          debugPrint('用Link找到旧对话，准备迁移到ID');
          final updatedConversation = existingConversation.copyWith(
            articleUrl: articleId, // 更新为使用ID
            updatedAt: DateTime.now(),
          );
          await _historyService.saveConversation(updatedConversation);
          debugPrint('旧对话已迁移到新ID');
        }
      }

      if (existingConversation != null) {
        // 加载现有对话
        _currentConversationId = existingConversation.id;
        messages.clear();
        messages.addAll(existingConversation.messages);
        messages.refresh();
        _scrollToBottom();
        debugPrint('加载现有对话: ${existingConversation.id}');

        // 添加新的用户消息到现有对话
        _addNewUserMessage();
      } else {
        // 创建新对话
        _currentConversationId = _uuid.v4();
        _sendInitialRequest();
      }
    } catch (e) {
      debugPrint('初始化对话失败: $e');
      // 出错时创建新对话
      _currentConversationId = _uuid.v4();
      _sendInitialRequest();
    }
  }

  /// 添加新的用户消息到现有对话
  void _addNewUserMessage() {
    // 添加用户新消息
    final userMessage = ChatMessage.user(selectedText);
    messages.add(userMessage);

    // 添加AI加载消息
    final aiMessage = ChatMessage.aiLoading();
    messages.add(aiMessage);

    messages.refresh();
    _scrollToBottom();

    // 发送请求
    _sendRequest(selectedText, false);
  }

  /// 停止AI生成
  void stopGeneration() {
    debugPrint('停止AI生成被调用');

    // 标记为用户主动停止
    _isUserStopped = true;

    // 完成当前的异步操作，避免超时
    if (_currentCompleter != null && !_currentCompleter!.isCompleted) {
      _currentCompleter!.complete();
    }

    // 取消当前的流订阅
    _currentStreamSubscription?.cancel();
    _currentStreamSubscription = null;
    _currentCompleter = null;

    // 设置加载状态为false
    isLoading.value = false;

    // 更新最后一条AI消息的状态
    if (messages.isNotEmpty && !messages.last.isUser) {
      final lastMessageIndex = messages.length - 1;
      final lastMessage = messages[lastMessageIndex];

      // 如果最后一条消息是加载中的AI消息，更新其状态
      if (lastMessage.isLoading || !lastMessage.isComplete) {
        final updatedMessage = lastMessage.copyWith(
          isLoading: false,
          isComplete: true,
          content: lastMessage.content.isEmpty
              ? 'Generation stopped by user.'
              : lastMessage.content,
        );
        messages[lastMessageIndex] = updatedMessage;
        messages.refresh();
      }
    }

    debugPrint('AI生成已停止');
  }

  @override
  void onClose() {
    // 清理资源
    _currentStreamSubscription?.cancel();
    if (_currentCompleter != null && !_currentCompleter!.isCompleted) {
      _currentCompleter!.complete();
    }
    scrollController.dispose();
    super.onClose();
  }

  /// 加载历史对话
  void loadHistoryConversation(ChatConversation conversation) {
    debugPrint('加载历史对话: ${conversation.id}');

    try {
      // 停止当前的生成过程
      stopGeneration();

      // 清空当前消息
      messages.clear();

      // 加载历史消息
      messages.addAll(conversation.messages);

      // 设置当前对话ID为历史对话ID，这样新消息会追加到这个对话中
      _currentConversationId = conversation.id;

      // 设置历史对话的文章内容，这样AI就能访问到正确的文章内容
      _historyArticleContent = conversation.articleContent;
      _historyArticleTitle = conversation.articleTitle;
      _historyArticleUrl = conversation.articleUrl;

      // 刷新消息列表
      messages.refresh();

      // 滚动到底部
      _scrollToBottom();

      debugPrint('✅ 历史对话加载成功');
      debugPrint('  - 消息数量: ${messages.length}');
      debugPrint('  - 文章标题: ${_historyArticleTitle}');
      debugPrint('  - 文章内容长度: ${_historyArticleContent?.length ?? 0}');
    } catch (e) {
      debugPrint('❌ 加载历史对话失败: $e');
      // 显示错误提示
      Get.snackbar(
        'error'.tr,
        'failed_to_load_conversation'.tr,
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 清除历史对话状态，回到当前文章模式
  void clearHistoryConversationState() {
    debugPrint('清除历史对话状态');
    _historyArticleContent = null;
    _historyArticleTitle = null;
    _historyArticleUrl = null;
    _currentConversationId = null;
  }

  /// 发送消息
  void sendMessage(String text) {
    if (text.trim().isEmpty || isLoading.value) return;

    // 添加用户消息
    messages.add(ChatMessage.user(text));

    // 添加AI加载消息
    messages.add(ChatMessage.aiLoading());

    // 滚动到底部
    _scrollToBottom();

    // 发送请求
    _sendRequest(text, false);
  }

  /// 发送初始请求
  void _sendInitialRequest() {
    debugPrint('_sendInitialRequest 被调用，准备发送请求');

    // 添加用户初始消息
    messages.add(ChatMessage.user(selectedText));
    debugPrint('添加用户消息，当前消息数量: ${messages.length}');

    // 添加AI加载消息
    messages.add(ChatMessage.aiLoading());
    debugPrint('添加AI加载消息，当前消息数量: ${messages.length}');

    // 发送请求
    _sendRequest(selectedText, true);
  }

  /// 发送请求到AI服务器
  Future<void> _sendRequest(String text, bool isInitial) async {
    debugPrint('_sendRequest 被调用，文本: "$text", isInitial: $isInitial');

    if (isLoading.value) {
      debugPrint('请求被跳过，因为正在加载中');
      return;
    }

    isLoading.value = true;
    debugPrint('设置 isLoading = true，开始请求');

    try {
      await _performAIRequest(text, isInitial);
    } catch (e) {
      debugPrint('请求出错: $e');

      // 如果是用户主动停止，不视为错误
      if (!_isUserStopped) {
        _handleRequestError(e);
      } else {
        debugPrint('用户主动停止生成，不视为错误');
      }
    } finally {
      isLoading.value = false;
      debugPrint('设置 isLoading = false，请求完成');
    }
  }

  /// 执行AI请求
  Future<void> _performAIRequest(String text, bool isInitial) async {
    // 准备请求内容 - 优先使用历史对话的文章内容
    String articleContent;
    if (_historyArticleContent != null && _historyArticleContent!.isNotEmpty) {
      // 使用历史对话的文章内容
      articleContent = _sanitizeHtmlContent(_historyArticleContent!);
      debugPrint('使用历史对话的文章内容，长度: ${articleContent.length}');
    } else {
      // 使用当前页面的文章内容
      articleContent = _sanitizeHtmlContent(
          articleController.article.value['content'] ?? '');
      debugPrint('使用当前页面的文章内容，长度: ${articleContent.length}');
    }

    final instruction = articleController.currentLanguage.value == 'zh'
        ? "基于文章内容，请用中文解释以下内容的含义："
        : "Based on the article content, please explain the meaning of the following content in English:";

    final requestContent =
        "Article Content: ${_sanitizeTextForAI(articleContent)}. $instruction '${_sanitizeTextForAI(text)}'";

    // 构建请求
    final requestData = {
      'model': 'SparkDesk-v3.5',
      'stream': true,
      'messages': [
        {'role': 'user', 'content': requestContent}
      ],
      'temperature': 0.7,
      'max_tokens': 2000,
    };

    // 发送HTTP请求
    final client = HttpClient();
    client.connectionTimeout = const Duration(seconds: 60); //

    final request = await client
        .postUrl(Uri.parse('https://ai.iextend.top/v1/chat/completions'))
        .timeout(const Duration(seconds: 15));

    request.headers.set('Content-Type', 'application/json');
    request.headers.set('Authorization',
        'Bearer sk-ppTkHGHwNrRH87fX8bCc1928337440549997F03f6aF94390');

    final jsonString = jsonEncode(requestData);
    final bytes = utf8.encode(jsonString);
    request.contentLength = bytes.length;
    request.add(bytes);

    final response =
        await request.close().timeout(const Duration(seconds: 1200)); //  60s

    if (response.statusCode != 200) {
      throw Exception('服务器响应错误: ${response.statusCode}');
    }

    // 处理流式响应
    await _handleStreamResponse(response);
  }

  /// 处理流式响应
  Future<void> _handleStreamResponse(HttpClientResponse response) async {
    final completer = Completer<void>();
    final aiResponseBuffer = StringBuffer();
    final aiMessageIndex = messages.length - 1;

    // 重置用户停止标志
    _isUserStopped = false;

    // 取消之前的流订阅和完成器
    _currentStreamSubscription?.cancel();
    if (_currentCompleter != null && !_currentCompleter!.isCompleted) {
      _currentCompleter!.complete();
    }

    // 设置新的完成器
    _currentCompleter = completer;

    _currentStreamSubscription = response.transform(utf8.decoder).listen(
      (data) {
        _processStreamData(data, aiResponseBuffer, aiMessageIndex);
      },
      onDone: () {
        // 更新最后的AI消息状态
        if (aiMessageIndex < messages.length) {
          messages[aiMessageIndex] = messages[aiMessageIndex].copyWith(
            isLoading: false,
          );
          messages.refresh();
        }
        _scrollToBottom();

        // 注意：保存对话到历史记录现在在检测到'data: [DONE]'时进行
        // 这样可以确保在AI消息完全生成完成后才保存

        if (!completer.isCompleted) completer.complete();
      },
      onError: (error) {
        _handleStreamError(error, aiMessageIndex);
        if (!completer.isCompleted) completer.completeError(error);
      },
    );

    await completer.future.timeout(const Duration(seconds: 40));
  }

  /// 处理流数据
  void _processStreamData(String data, StringBuffer buffer, int messageIndex) {
    final lines = data.split('\n');
    bool hasNewContent = false;

    for (final line in lines) {
      if (line.trim().isEmpty) continue;

      if (line == 'data: [DONE]') {
        // 标记AI消息为完成状态
        if (messageIndex < messages.length) {
          final currentMessage = messages[messageIndex];
          final completedMessage = currentMessage.copyWith(
            isComplete: true,
            isLoading: false,
          );
          messages[messageIndex] = completedMessage;
          messages.refresh();
          update();
          debugPrint('AI消息生成完成，标记为完成状态');
        }

        // 保存对话到历史记录
        _saveConversationToHistory();
        debugPrint('AI对话完成，保存到历史记录');

        return;
      }

      if (line.startsWith('data: ')) {
        final content = line.substring(6);
        try {
          final jsonData = jsonDecode(content);
          if (jsonData['choices'] != null &&
              jsonData['choices'].isNotEmpty &&
              jsonData['choices'][0]['delta'] != null &&
              jsonData['choices'][0]['delta']['content'] != null) {
            final deltaContent =
                jsonData['choices'][0]['delta']['content'] as String;

            if (deltaContent.isNotEmpty) {
              buffer.write(deltaContent);
              hasNewContent = true;

              // 立即更新UI以实现真正的流式效果
              if (messageIndex < messages.length) {
                // 直接修改现有对象的内容，而不是创建新对象
                final currentMessage = messages[messageIndex];
                final updatedMessage = currentMessage.copyWith(
                  content: buffer.toString(),
                  isLoading: false,
                  isComplete: false, // 流式生成过程中未完成
                );
                messages[messageIndex] = updatedMessage;
                debugPrint('更新消息: ${updatedMessage.content}');
                // 立即触发UI更新，不等待批处理
                messages.refresh();
                update();
              }
            }
          }
        } catch (e) {
          debugPrint('解析流数据出错: $e');
        }
      }
    }

    // 如果有新内容，强制刷新UI并滚动
    if (hasNewContent) {
      // 使用多种方式确保UI更新
      messages.refresh(); // GetX刷新
      update(); // GetxController刷新
      // 强制下一帧更新UI
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  /// 处理流错误
  void _handleStreamError(dynamic error, int messageIndex) {
    debugPrint('流处理错误: $error');
    if (messageIndex < messages.length) {
      messages[messageIndex] = messages[messageIndex].copyWith(
        content: 'article_ask_ai_failed'.tr,
        isLoading: false,
        isError: true,
      );
    }
  }

  /// 处理请求错误
  void _handleRequestError(dynamic error) {
    // 更新最后一条AI消息为错误状态
    if (messages.isNotEmpty && !messages.last.isUser) {
      final lastIndex = messages.length - 1;
      messages[lastIndex] = messages[lastIndex].copyWith(
        content: 'article_ask_ai_failed'.tr,
        isLoading: false,
        isError: true,
      );
    }
  }

  /// 重新生成消息
  void regenerateMessage(int messageIndex, ChatMessage message) {
    debugPrint('重新生成消息，索引: $messageIndex');

    if (messageIndex < 0 || messageIndex >= messages.length) {
      debugPrint('无效的消息索引: $messageIndex');
      return;
    }

    if (message.isUser) {
      debugPrint('不能重新生成用户消息');
      return;
    }

    if (isLoading.value) {
      debugPrint('正在加载中，无法重新生成');
      return;
    }

    // 找到对应的用户消息（通常是AI消息的前一条）
    String userMessage = '';
    if (messageIndex > 0 && messages[messageIndex - 1].isUser) {
      userMessage = messages[messageIndex - 1].content;
    } else {
      // 如果找不到对应的用户消息，使用选中的文本
      userMessage = selectedText;
    }

    // 将当前AI消息替换为加载状态
    messages[messageIndex] = ChatMessage.aiLoading();

    // 发送重新生成请求
    _sendRequest(userMessage, false);
  }

  /// 滚动到底部
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        // 使用更短的动画时间，让流式渲染更流畅
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 100),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 清理HTML内容
  String _sanitizeHtmlContent(String html) {
    if (html.isEmpty) return '';

    try {
      // 删除script和style标签及其内容
      var result = html;
      result = result.replaceAll(
          RegExp(r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>',
              caseSensitive: false),
          '');
      result = result.replaceAll(
          RegExp(r'<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>',
              caseSensitive: false),
          '');

      // 替换换行标签为空格
      result =
          result.replaceAll(RegExp(r'<br\s*\/?>', caseSensitive: false), ' ');
      result = result.replaceAll(RegExp(r'<\/p>', caseSensitive: false), ' ');
      result = result.replaceAll(RegExp(r'<\/div>', caseSensitive: false), ' ');
      result =
          result.replaceAll(RegExp(r'<\/h[1-6]>', caseSensitive: false), ' ');

      // 移除所有其他HTML标签，但保留其内容
      result = result.replaceAll(RegExp(r'<[^>]*>'), '');

      // 移除多余的空白字符
      result = result.replaceAll(RegExp(r'\s+'), ' ').trim();

      return _sanitizeTextForAI(result);
    } catch (e) {
      debugPrint('HTML清理出错: $e');
      return '';
    }
  }

  /// 专门为AI请求准备的文本清理方法
  String _sanitizeTextForAI(String text) {
    try {
      if (text.isEmpty) return "";

      // 处理引号问题
      String cleaned = text.replaceAll(r'\"', "'");
      cleaned = cleaned.replaceAll('"', "'");

      // 处理不可见控制字符
      cleaned = cleaned.replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '');

      return cleaned.trim();
    } catch (e) {
      debugPrint('为AI清理文本失败: $e');
      return "请解释所选文本";
    }
  }

  /// 保存对话到历史记录
  Future<void> _saveConversationToHistory() async {
    debugPrint('_saveConversationToHistory 被调用');
    debugPrint('_currentConversationId: $_currentConversationId');
    debugPrint('messages.length: ${messages.length}');

    if (_currentConversationId == null || messages.isEmpty) {
      debugPrint('跳过保存：conversationId为空或消息列表为空');
      return;
    }

    try {
      // 优先使用历史对话的文章信息，如果没有则使用当前页面的文章信息
      final articleId =
          _historyArticleUrl ?? articleController.article.value['id'] ?? '';
      final articleTitle = _historyArticleTitle ??
          articleController.article.value['title'] ??
          '未知文章';
      final articleContent = _historyArticleContent ??
          articleController.article.value['content'] ??
          '';

      debugPrint('保存对话 - 文章ID: $articleId');
      debugPrint('保存对话 - 文章标题: $articleTitle');
      debugPrint('保存对话 - 是否使用历史文章内容: ${_historyArticleContent != null}');

      // 检查是否已有该文章的对话记录
      final existingConversation =
          await _historyService.getConversationByArticleUrl(articleId);

      if (existingConversation != null) {
        // 如果已有对话，只保存完整的对话（覆盖更新）
        debugPrint('找到现有对话，准备更新: ${existingConversation.id}');
        final updatedConversation = existingConversation.copyWith(
          updatedAt: DateTime.now(),
          messages: messages.toList(),
          // 保持原有的文章信息，不覆盖
        );
        await _historyService.saveConversation(updatedConversation);
        debugPrint('✅ 更新现有对话成功: ${updatedConversation.id}');
      } else {
        // 创建新对话记录
        debugPrint('未找到现有对话，准备创建新对话');
        final conversation = ChatConversation(
          id: _currentConversationId!,
          articleTitle: articleTitle,
          articleContent: articleContent,
          articleUrl: articleId,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          messages: messages.toList(),
        );
        debugPrint('新对话信息: ${conversation.toString()}');
        await _historyService.saveConversation(conversation);
        debugPrint('✅ 创建新对话成功: ${conversation.id}');
      }
    } catch (e) {
      debugPrint('❌ 保存对话到历史记录失败: $e');
      debugPrint('错误堆栈: ${e.toString()}');
    }
  }

  /// 从历史记录加载对话
  void loadConversationFromHistory(ChatConversation conversation) {
    _currentConversationId = conversation.id;
    messages.clear();
    messages.addAll(conversation.messages);
    messages.refresh();
    _scrollToBottom();
    debugPrint('从历史记录加载对话: ${conversation.id}');
  }

  // 历史记录功能已集成到ChatDialog中，不再需要单独的页面
}

/// AI对话视图
class _AskAiDialogView extends StatelessWidget {
  final AskAiDialogController controller;
  final ArticleController articleController;
  const _AskAiDialogView(
      {required this.controller, required this.articleController});

  @override
  Widget build(BuildContext context) {
    // 使用GetBuilder确保控制器正确初始化，然后在内部使用Obx
    return GetBuilder<AskAiDialogController>(
      init: controller,
      builder: (_) {
        return Obx(() => ChatDialog(
              title: 'SubFeeds AI',
              messages: controller.messages,
              onSendMessage: controller.sendMessage,
              isLoading: controller.isLoading.value,
              scrollController: controller.scrollController,
              isDark: articleController.isDarkMode.value,
              onRegenerateMessage: controller.regenerateMessage,
              onStopGeneration: controller.stopGeneration,
              onLoadConversation: controller.loadHistoryConversation,
              currentArticleTitle: articleController.article.value['title'],
            ));
      },
    );
  }
}
