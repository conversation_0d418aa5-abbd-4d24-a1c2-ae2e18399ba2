import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/data/models/chat_conversation.dart';
import 'package:subfeeds/app/data/services/chat_history_service.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/feeds_list_widget/dialogs/confirmation_dialog.dart';
import 'chat_message_bubble.dart';
import 'chat_input_field.dart';

/// 聊天消息数据模型
class ChatMessage {
  final String content;
  final bool isUser;
  final bool isLoading;
  final bool isError;
  final bool isComplete; // AI消息是否已完成生成
  final DateTime timestamp;
  final String? id;

  ChatMessage({
    required this.content,
    required this.isUser,
    this.isLoading = false,
    this.isError = false,
    this.isComplete = false,
    DateTime? timestamp,
    this.id,
  }) : timestamp = timestamp ?? DateTime.now();

  /// 创建用户消息
  factory ChatMessage.user(String content, {String? id}) {
    return ChatMessage(
      content: content,
      isUser: true,
      isComplete: true, // 用户消息总是完整的
      id: id,
    );
  }

  /// 创建AI消息
  factory ChatMessage.ai(String content,
      {bool isLoading = false,
      bool isError = false,
      bool isComplete = false,
      String? id}) {
    return ChatMessage(
      content: content,
      isUser: false,
      isLoading: isLoading,
      isError: isError,
      isComplete: isComplete,
      id: id,
    );
  }

  /// 创建加载中的AI消息
  factory ChatMessage.aiLoading({String? id}) {
    return ChatMessage(
      content: '',
      isUser: false,
      isLoading: true,
      isComplete: false, // 加载中的消息未完成
      id: id,
    );
  }

  /// 创建错误消息
  factory ChatMessage.error(String errorMessage, {String? id}) {
    return ChatMessage(
      content: errorMessage,
      isUser: false,
      isError: true,
      isComplete: true, // 错误消息视为完成
      id: id,
    );
  }

  /// 复制消息并更新属性
  ChatMessage copyWith({
    String? content,
    bool? isUser,
    bool? isLoading,
    bool? isError,
    bool? isComplete,
    DateTime? timestamp,
    String? id,
  }) {
    return ChatMessage(
      content: content ?? this.content,
      isUser: isUser ?? this.isUser,
      isLoading: isLoading ?? this.isLoading,
      isError: isError ?? this.isError,
      isComplete: isComplete ?? this.isComplete,
      timestamp: timestamp ?? this.timestamp,
      id: id ?? this.id,
    );
  }

  @override
  String toString() {
    return 'ChatMessage(content: $content, isUser: $isUser, isLoading: $isLoading, isError: $isError, isComplete: $isComplete)';
  }
}

/// 聊天对话框组件
class ChatDialog extends StatefulWidget {
  final String title;
  final List<ChatMessage> messages;
  final Function(String) onSendMessage;
  final bool isLoading;
  final bool isDark;
  final ScrollController? scrollController;
  final VoidCallback? onClose;
  final Function(int, ChatMessage)? onRegenerateMessage;
  final VoidCallback? onShowHistory;
  final VoidCallback? onStopGeneration; // 停止生成回调
  final Function(ChatConversation)? onLoadConversation; // 加载历史对话回调
  final String? currentArticleTitle; // 当前文章标题，用于显示上下文

  const ChatDialog({
    super.key,
    required this.title,
    required this.messages,
    required this.onSendMessage,
    this.isLoading = false,
    this.isDark = false,
    this.scrollController,
    this.onClose,
    this.onRegenerateMessage,
    this.onShowHistory,
    this.onStopGeneration,
    this.onLoadConversation,
    this.currentArticleTitle,
  });

  @override
  State<ChatDialog> createState() => _ChatDialogState();
}

class _ChatDialogState extends State<ChatDialog> {
  final PageController _pageController = PageController();
  final ChatHistoryService _historyService = ChatHistoryService();
  List<ChatConversation> _conversations = [];
  bool _isLoadingHistory = false;
  int _currentPageIndex = 0;
  ChatConversation? _currentLoadedConversation; // 当前加载的历史对话

  @override
  void initState() {
    super.initState();
    _loadConversations();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// 加载对话历史
  Future<void> _loadConversations() async {
    try {
      setState(() => _isLoadingHistory = true);
      final conversations = await _historyService.getAllConversations();
      setState(() {
        _conversations = conversations;
        _isLoadingHistory = false;
      });
    } catch (e) {
      debugPrint('加载对话历史失败: $e');
      setState(() => _isLoadingHistory = false);
    }
  }

  /// 切换到历史页面
  void _showHistoryPage() {
    _pageController.animateToPage(
      1,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  /// 切换到对话页面
  void _showChatPage() {
    debugPrint('切换到对话页面');
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanEnd: (details) {
        // 检测左滑手势
        if (details.velocity.pixelsPerSecond.dx > 500 &&
            _currentPageIndex == 0) {
          _showHistoryPage();
        }
      },
      child: Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color:
              widget.isDark ? const Color(0xFF444444) : const Color(0xFFf7faff),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.spx),
            topRight: Radius.circular(20.spx),
          ),
        ),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(top: 10.spx, right: 24.spx),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // 关闭按钮
                  InkWell(
                    onTap: widget.onClose ?? () => Navigator.pop(context),
                    child: Icon(
                      Icons.close_rounded,
                      size: 20.spx,
                      color: widget.isDark
                          ? const Color(0xFFD1D1D1)
                          : const Color(0xFF999999),
                    ),
                  ),
                ],
              ),
            ),

            // PageView内容
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPageIndex = index;
                  });
                },
                children: [
                  // 第一页：对话界面
                  _buildChatPage(context),
                  // 第二页：历史记录界面
                  _buildHistoryPage(context),
                ],
              ),
            ),
          ],
        ),
      ), // Container
    ); // GestureDetector
  }

  /// 构建对话页面
  Widget _buildChatPage(BuildContext context) {
    return Column(
      children: [
        // 顶部标题栏
        _buildHeader(context),

        // 消息列表
        Expanded(
          child: Stack(
            children: [
              _buildMessageList(context),
              // 停止生成按钮
              if (widget.isLoading) _buildStopButton(context),
            ],
          ),
        ),

        // 输入框
        ChatInputField(
          onSendMessage: widget.onSendMessage,
          isLoading: widget.isLoading,
          isDark: widget.isDark,
        ),
      ],
    );
  }

  /// 构建历史记录页面
  Widget _buildHistoryPage(BuildContext context) {
    return Column(
      children: [
        // 历史记录标题栏
        _buildHistoryHeader(context),

        // 历史记录列表
        Expanded(
          child: _buildHistoryList(context),
        ),
      ],
    );
  }

  /// 构建头部
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.spx, horizontal: 24.spx),
      child: Column(
        children: [
          // 主标题行
          Row(
            children: [
              // Logo
              Container(
                width: 24.spx,
                height: 24.spx,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10.spx,
                      offset: Offset(0, 2.spx),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(50.spx),
                  child: Image.asset(
                    'assets/images/logo-dark.png',
                    width: 24.spx,
                    height: 24.spx,
                  ),
                ),
              ),
              SizedBox(width: 5.spx),
              // 标题
              Expanded(
                child: Text(
                  widget.title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 14.spx,
                        color: const Color(0xFF7F8EA7),
                      ),
                ),
              ),
              // history 历史记录列表入口
              InkWell(
                onTap: _showHistoryPage,
                child: Text(
                  'feeds_history'.tr,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontSize: 16.spx,
                        fontWeight: FontWeight.w500,
                        color: widget.isDark
                            ? Colors.white
                            : Theme.of(context).primaryColor,
                      ),
                ),
              ),
            ],
          ),

          // 文章上下文信息
          if (_currentLoadedConversation != null ||
              widget.currentArticleTitle != null)
            _buildArticleContext(context),
        ],
      ),
    );
  }

  /// 构建文章上下文信息
  Widget _buildArticleContext(BuildContext context) {
    final articleTitle = _currentLoadedConversation?.articleTitle ??
        widget.currentArticleTitle ??
        '';
    final isHistoryConversation = _currentLoadedConversation != null;

    return Container(
      margin: EdgeInsets.only(top: 8.spx),
      padding: EdgeInsets.symmetric(horizontal: 12.spx, vertical: 8.spx),
      decoration: BoxDecoration(
        color: widget.isDark
            ? const Color(0xFF565656).withValues(alpha: 0.5)
            : const Color(0xFFE8F4FD).withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8.spx),
        border: Border.all(
          color:
              widget.isDark ? const Color(0xFF747474) : const Color(0xFFB3D9F2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 图标
          Icon(
            isHistoryConversation ? Icons.history : Icons.article_outlined,
            size: 16.spx,
            color: widget.isDark
                ? const Color(0xFFD1D1D1)
                : const Color(0xFF7F8EA7),
          ),
          SizedBox(width: 8.spx),
          // 文章标题
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (isHistoryConversation)
                  Text(
                    'discussing_article'.tr,
                    style: TextStyle(
                      fontSize: 10.spx,
                      color: widget.isDark
                          ? const Color(0xFFD1D1D1)
                          : const Color(0xFF999999),
                    ),
                  ),
                Text(
                  articleTitle,
                  style: TextStyle(
                    fontSize: 12.spx,
                    fontWeight: FontWeight.w500,
                    color:
                        widget.isDark ? Colors.white : const Color(0xFF333333),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          // 历史对话标识
          if (isHistoryConversation)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6.spx, vertical: 2.spx),
              decoration: BoxDecoration(
                color: widget.isDark
                    ? const Color(0xFF4f5050)
                    : const Color(0xFF007AFF),
                borderRadius: BorderRadius.circular(4.spx),
              ),
              child: Text(
                'history'.tr,
                style: TextStyle(
                  fontSize: 10.spx,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建消息列表
  Widget _buildMessageList(BuildContext context) {
    if (widget.messages.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      controller: widget.scrollController,
      padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 10.spx),
      itemCount: widget.messages.length,
      itemBuilder: (context, index) {
        final message = widget.messages[index];

        // 判断是否是最后一条AI消息
        bool isLastAIMessage = false;
        if (!message.isUser) {
          // 找到最后一条AI消息的索引
          int lastAIMessageIndex = -1;
          for (int i = widget.messages.length - 1; i >= 0; i--) {
            if (!widget.messages[i].isUser) {
              lastAIMessageIndex = i;
              break;
            }
          }
          isLastAIMessage = (index == lastAIMessageIndex);
        }

        return ChatMessageBubble(
          key: ValueKey('message_${message.id ?? index}'), // 使用稳定的key，只基于位置和ID
          content: message.content,
          isUser: message.isUser,
          isLoading: message.isLoading,
          // isError: message.isError,
          isError: false,
          isComplete: message.isComplete,
          isDark: widget.isDark,
          messageId: message.id,
          isLastMessage: isLastAIMessage,
          onCopy: () {
            // 可以在这里添加复制后的回调逻辑
          },
          onRegenerate: message.isUser
              ? null
              : () {
                  // 只有AI消息才能重新生成
                  if (widget.onRegenerateMessage != null) {
                    widget.onRegenerateMessage!(index, message);
                  }
                },
        );
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/note-empty.png',
            width: 120,
            height: 120,
          ),
          const SizedBox(height: 16),
          Text(
            'Start a conversation',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).dividerColor,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ask me anything about the article',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).dividerColor,
                ),
          ),
        ],
      ),
    );
  }

  /// 构建历史记录标题栏
  Widget _buildHistoryHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 0.spx),
      child: Row(
        children: [
          // 返回按钮
          InkWell(
            onTap: _showChatPage,
            child: Icon(
              Icons.arrow_back_ios_new,
              size: 18.spx,
              color: const Color(0xFF90A3BF),
            ),
          ),
          SizedBox(width: 12.spx),
          // 标题
          Expanded(
            child: Text(
              'feeds_history'.tr,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                    fontSize: 16.spx,
                    color: widget.isDark
                        ? Colors.white
                        : Theme.of(context).primaryColor,
                  ),
            ),
          ),

          // 清空按钮
          // if (_conversations.isNotEmpty)
          //   InkWell(
          //     onTap: _showClearAllDialog,
          //     child: Icon(
          //       Icons.delete_sweep,
          //       size: 20.spx,
          //       color: widget.isDark ? Colors.white54 : Colors.grey[600],
          //     ),
          //   ),
        ],
      ),
    );
  }

  /// 构建历史记录列表
  Widget _buildHistoryList(BuildContext context) {
    if (_isLoadingHistory) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_conversations.isEmpty) {
      return _buildHistoryEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: _loadConversations,
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 20.spx),
        itemCount: _conversations.length,
        itemBuilder: (context, index) {
          final conversation = _conversations[index];
          return _buildConversationItem(conversation);
        },
      ),
    );
  }

  /// 构建历史记录空状态
  Widget _buildHistoryEmptyState(context) {
    return Center(
        child: Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.spx, vertical: 10.spx),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/note-empty.png',
            width: 120,
            height: 120,
          ),
          SizedBox(height: 10.spx),
          Text(
            'start_conversation_history'.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12.spx,
              color: const Color(0xFF7F8EA7),
            ),
          ),
        ],
      ),
    ));
  }

  /// 构建对话项目
  Widget _buildConversationItem(ChatConversation conversation) {
    return Padding(
      padding: EdgeInsets.only(bottom: 10.spx),
      child: Slidable(
        key: ValueKey(conversation.id),
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          extentRatio: 0.2, // 控制滑动面板的宽度
          children: [
            SizedBox(width: 5.spx),
            CustomSlidableAction(
              onPressed: (_) => _deleteConversation(conversation),
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding:
                  EdgeInsets.symmetric(vertical: 22.spx, horizontal: 15.spx),
              borderRadius: BorderRadius.circular(6.spx),
              child: Container(
                width: 60.spx,
                height: 50.spx,
                padding: EdgeInsets.zero,
                child: SvgPicture.asset(
                  'assets/feeds/feeds_delete.svg',
                  width: 20.spx,
                  height: 20.spx,
                  colorFilter:
                      const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                ),
              ),
            ),
          ],
        ),
        child: InkWell(
          onTap: () => _loadConversation(conversation),
          borderRadius: BorderRadius.circular(12.spx),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20.spx, vertical: 15.spx),
            decoration: BoxDecoration(
              color: widget.isDark
                  ? const Color(0xFF565656)
                  : const Color(0xFFffffff),
              borderRadius: BorderRadius.circular(12.spx),
              border: Border.all(
                color: widget.isDark
                    ? const Color(0xFF747474)
                    : const Color(0xFFD5D5D5),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // 左侧：标题与描述
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 标题和时间
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              conversation.title,
                              style: TextStyle(
                                fontSize: 13.spx,
                                fontWeight: FontWeight.w600,
                                fontFamily: 'Poppins',
                                color:
                                    widget.isDark ? Colors.white : Colors.black,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(width: 8.spx),
                          Text(
                            _formatDate(conversation.updatedAt),
                            style: TextStyle(
                              fontSize: 12.spx,
                              color: widget.isDark
                                  ? const Color(0xFFD1D1D1)
                                  : const Color(0xFF999999),
                              fontFamily: 'Poppins',
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8.spx),
                      // 描述
                      Text(
                        conversation.description,
                        style: TextStyle(
                          fontSize: 14.spx,
                          color:
                              widget.isDark ? Colors.white70 : Colors.grey[700],
                          height: 1.4,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                SizedBox(width: 12.spx),

                // 右侧：圆形盒子
                Stack(
                  children: [
                    Container(
                      padding: EdgeInsets.all(10.spx),
                      decoration: BoxDecoration(
                        color: widget.isDark
                            ? const Color(0xFF4f5050)
                            : const Color(0xFFedefff),
                        shape: BoxShape.circle,
                      ),
                      child: SvgPicture.asset(
                        'assets/icons/chart_pop.svg',
                        width: 12.spx,
                        height: 12.spx,
                        colorFilter: ColorFilter.mode(
                            widget.isDark
                                ? Color(0xFFd1d1d1)
                                : Color(0xFF999999),
                            BlendMode.srcIn),
                      ),
                    ),
                    // Positioned(
                    //   bottom: 0,
                    //   right: 0,
                    //   child: Text(
                    //     '${conversation.messages.length}',
                    //     style: TextStyle(
                    //       fontSize: 12.spx,
                    //       fontWeight: FontWeight.w500,
                    //       color: widget.isDark
                    //           ? const Color(0xFFd1d1d1)
                    //           : const Color(0xFF999999),
                    //     ),
                    //   ),
                    // ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 删除对话
  Future<void> _deleteConversation(ChatConversation conversation) async {
    final confirmed = await _showDeleteConfirmDialog(conversation.title);
    if (!confirmed) return;

    try {
      await _historyService.deleteConversation(conversation.id);
      setState(() {
        _conversations.removeWhere((c) => c.id == conversation.id);
      });
    } catch (e) {
      debugPrint('删除对话失败: $e');
    }
  }

  /// 加载对话
  void _loadConversation(ChatConversation conversation) {
    debugPrint('加载历史对话: ${conversation.id}');

    // 设置当前加载的对话
    setState(() {
      _currentLoadedConversation = conversation;
    });

    // 通过回调通知父组件加载对话
    if (widget.onLoadConversation != null) {
      widget.onLoadConversation!(conversation);
    }

    // 切换回对话页面
    _showChatPage();

    // 显示加载成功的提示
    _showConversationLoadedSnackBar(conversation);
  }

  /// 显示对话加载成功的提示
  void _showConversationLoadedSnackBar(ChatConversation conversation) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'conversation_loaded'
              .tr
              .replaceAll('{title}', conversation.articleTitle),
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).size.height * 0.7,
          left: 16,
          right: 16,
        ),
      ),
    );
  }

  /// 显示删除确认对话框
  Future<bool> _showDeleteConfirmDialog(String title) async {
    final Completer<bool> completer = Completer<bool>();

    Get.dialog(
      ConfirmationDialog(
        title: 'delete_conversation'.tr,
        message:
            'confirm_delete_conversation'.tr.replaceAll('{title}', '"$title"'),
        confirmText: 'delete',
        cancelText: 'cancel',
        confirmColor: const Color(0xFFd0452f),
        onConfirm: () {
          completer.complete(true);
        },
      ),
    ).then((_) {
      // 如果对话框被取消（点击外部或返回键），返回false
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });

    return completer.future;
  }

  /// 构建停止生成按钮
  Widget _buildStopButton(BuildContext context) {
    return Positioned(
      bottom: 20.spx,
      left: 0,
      right: 0,
      child: Center(
        child: GestureDetector(
          onTap: () {
            if (widget.onStopGeneration != null) {
              widget.onStopGeneration!();
            }
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.spx, vertical: 8.spx),
            decoration: BoxDecoration(
              color: widget.isDark
                  ? const Color(0xFF747474) // Dark模式背景色
                  : const Color(0xFFd5d5d5), // Light模式背景色
              borderRadius: BorderRadius.circular(10.spx),
            ),
            child: Text(
              'stop_generation'.tr,
              style: TextStyle(
                fontFamily: 'Poppins',
                fontSize: 12.spx,
                fontWeight: FontWeight.w500,
                color: Colors.white, // 两种模式都使用白色文字
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return DateFormat('HH:mm').format(date);
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return DateFormat('MM/dd').format(date);
    }
  }
}

/// 显示聊天对话框的便捷方法
void showChatDialog({
  required BuildContext context,
  required String title,
  required List<ChatMessage> messages,
  required Function(String) onSendMessage,
  bool isLoading = false,
  ScrollController? scrollController,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => ChatDialog(
      title: title,
      messages: messages,
      onSendMessage: onSendMessage,
      isLoading: isLoading,
      scrollController: scrollController,
    ),
  );
}
