import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/article/widgets/article_bottom_bar.dart';
import 'package:subfeeds/app/presentation/screens/article/widgets/ask_ai_dialog.dart';
import 'package:subfeeds/app/presentation/screens/article/widgets/note_bottom_sheet.dart';
import 'package:subfeeds/app/presentation/screens/article/widgets/share_menu_sheet.dart';
import 'package:subfeeds/app/presentation/screens/article/widgets/image_preview_screen.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:subfeeds/app/presentation/screens/article/article_controller.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
// 添加导入文本选择所需的库
import 'package:flutter/gestures.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

/// 空的文本选择控制器，用于禁用系统默认菜单
class EmptyTextSelectionControls extends TextSelectionControls {
  /// 构建工具栏 - 返回空容器，不显示系统工具栏
  @override
  Widget buildToolbar(
    BuildContext context,
    Rect globalEditableRegion,
    double textLineHeight,
    Offset selectionMidpoint,
    List<TextSelectionPoint> endpoints,
    TextSelectionDelegate delegate,
    ValueListenable<ClipboardStatus>? clipboardStatus,
    Offset? lastSecondaryTapDownPosition,
  ) {
    // 返回空容器，不显示任何系统默认工具栏
    return const SizedBox.shrink();
  }

  /// 构建手柄 - 仍然显示选择手柄
  @override
  Widget buildHandle(
    BuildContext context,
    TextSelectionHandleType type,
    double textLineHeight, [
    VoidCallback? onTap,
  ]) {
    // 使用默认的Material风格的选择手柄
    return MaterialTextSelectionControls().buildHandle(
      context,
      type,
      textLineHeight,
      onTap,
    );
  }

  /// 获取手柄大小
  @override
  Size getHandleSize(double textLineHeight) {
    return const Size(20.0, 20.0);
  }

  /// 获取选择附件的本地位置
  @override
  Offset getHandleAnchor(TextSelectionHandleType type, double textLineHeight) {
    return Offset(20.0, textLineHeight);
  }
}

/// 文章详情页面
class ArticleScreen extends GetView<ArticleController> {
  ArticleScreen({Key? key}) : super(key: key);

  // 添加OverlayEntry成员变量来管理菜单
  OverlayEntry? _selectionMenuOverlay;

  @override
  Widget build(BuildContext context) {
    return Obx(() => PopScope(
          canPop: true,
          onPopInvokedWithResult: (didPop, result) {
            _removeSelectionMenu();
          },
          child: Scaffold(
              backgroundColor: controller.backgroundColor.value,
              appBar: controller.isImmersiveMode.value
                  ? null
                  : AppBar(
                      backgroundColor: controller.backgroundColor.value,
                      leading: IconButton(
                        icon: Icon(
                          Icons.arrow_back,
                          color: controller.getTextColor(),
                        ),
                        onPressed: () {
                          _removeSelectionMenu();
                          Get.back();
                        },
                      ),
                      centerTitle: true,
                      title: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          controller.article.value['feedsName'] == 'Google News'
                              ? Container(
                                  width: 32.spx,
                                  height: 32.spx,
                                  padding: const EdgeInsets.all(4),
                                  child: SvgPicture.asset(
                                    'assets/share/google.svg',
                                    width: 24.spx,
                                    height: 24.spx,
                                  ),
                                )
                              : Container(
                                  width: 20.spx,
                                  height: 20.spx,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Theme.of(context).primaryColor,
                                      width: 1,
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(15),
                                    child: CachedNetworkImage(
                                        imageUrl:
                                            controller.article.value['img'] ??
                                                '',
                                        fit: BoxFit.cover,
                                        width: 20.spx,
                                        height: 20.spx,
                                        placeholder: (context, url) =>
                                            const LoadingIndicator(),
                                        errorWidget: (context, url, error) =>
                                            SvgPicture.asset(
                                              'assets/feeds/feeds_logo.svg',
                                              width: 20.spx,
                                              height: 20.spx,
                                            )),
                                  ),
                                ),
                          SizedBox(width: 5.spx),
                          Flexible(
                            child: Text(
                              controller.article.value['feedsName']?.trim() ??
                                  '',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: controller.getTextColor(),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      // 添加分享按钮
                      actions: [
                        IconButton(
                          icon: SvgPicture.asset(
                            'assets/icons/share-border.svg',
                            width: 24,
                            height: 24,
                            colorFilter: ColorFilter.mode(
                              controller.getTextColor(),
                              BlendMode.srcIn,
                            ),
                          ),
                          onPressed: () {
                            // 显示文章分享菜单
                            ShareMenuSheet(
                              context: context,
                              controller: controller,
                            ).show();
                          },
                          tooltip: 'share'.tr,
                        ),
                      ],
                    ),
              body: controller.article.value.isEmpty
                  ? const Center(child: LoadingIndicator())
                  : _buildArticleContent(context),
              bottomNavigationBar:
                  controller.isImmersiveMode.value ? null : ArticleBottomBar()),
        ));
  }

  /// 根据来源名称生成颜色
  Color _getSourceColor(String? source) {
    if (source == null || source.isEmpty) return Colors.grey;

    final List<Color> colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
      Colors.cyan,
    ];

    return colors[source.hashCode % colors.length];
  }

  /// 构建文章内容
  Widget _buildArticleContent(BuildContext context) {
    return GestureDetector(
        onTap: () {
          if (_selectionMenuOverlay != null) {
            _removeSelectionMenu();
            _clearSelectionSafely(context);
          }
        },
        child: Obx(
          () => Stack(
            children: [
              SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            controller.article.value['title'] ?? '',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: controller.fontFamily.value,
                                  color: controller.getTextColor(),
                                ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 14,
                                color:
                                    controller.getTextColor().withOpacity(0.6),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                controller.article.value['publishDate'] ??
                                    'Unknown date',
                                style: TextStyle(
                                  color: controller
                                      .getTextColor()
                                      .withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: _buildSelectableHtmlContent(context),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  /// 构建可选择的HTML内容
  Widget _buildSelectableHtmlContent(BuildContext context) {
    return SelectableText.rich(
      TextSpan(
        children: [
          WidgetSpan(
            child: GestureDetector(
              onTap: () {
                // 当点击的对象是图片时通知html解析器，图片的点击事件
              },
              child: Listener(
                // 使用Listener而不是GestureDetector，这样不会拦截事件
                onPointerDown: (PointerDownEvent event) {
                  // 记录指针位置，用于菜单定位
                  controller.lastTapPosition.value = event.position;
                  controller.selectionDragStartPosition.value = event.position;
                  controller.isSelectionDragging.value = false;
                  // 关闭菜单
                  if (_selectionMenuOverlay != null) {
                    _removeSelectionMenu();
                    _clearSelectionSafely(context);
                  }
                },
                onPointerMove: (PointerMoveEvent event) {
                  // 处理拖动事件，更新选区
                  if (controller.isTextSelected.value) {
                    controller.isSelectionDragging.value = true;
                    _updateSelectionOnDrag(context, event.position);
                  }
                },
                onPointerUp: (PointerUpEvent event) {
                  // 处理释放事件，完成选区
                  if (controller.isSelectionDragging.value) {
                    controller.isSelectionDragging.value = false;
                    _handleSelectionDragEnd(context, event.position);
                  }
                },
                child: SelectionArea(
                  focusNode: FocusNode(),
                  magnifierConfiguration: TextMagnifierConfiguration.disabled,
                  // 使用自定义空控制器，禁用系统选择菜单
                  selectionControls: EmptyTextSelectionControls(),
                  // 移除contextMenuBuilder属性
                  child: Obx(() => HtmlWidget(
                        controller.article.value['content'] ?? '',
                        textStyle: TextStyle(
                          fontSize: controller.fontSize.value,
                          fontFamily: controller.fontFamily.value,
                          height: controller.lineHeight.value,
                          color: controller.getTextColor(),
                        ),
                        onTapImage: (imageMetadata) {
                          openImagePreview(imageMetadata.sources.first.url);
                        },
                        // 设置更多配置选项控制HTML渲染
                        renderMode: RenderMode.column,
                        buildAsync: true,
                        enableCaching: true,
                        // 出现渲染错误则不显示
                        onErrorBuilder: (context, error, stackTrace) {
                          return const SizedBox.shrink();
                        },
                        onLoadingBuilder: (context, loading, error) {
                          return const LoadingIndicator();
                        },
                        // 自定义标签处理
                        customWidgetBuilder: (element) {
                          // 处理video标签
                          if (element.localName == 'video') {
                            // 检查是否有子源标签
                            final sources =
                                element.getElementsByTagName('source');
                            String? videoUrl;

                            if (sources.isNotEmpty &&
                                sources.first.attributes.containsKey('src')) {
                              videoUrl = sources.first.attributes['src'];
                            }

                            // 检查视频URL是否为YouTube
                            if (videoUrl != null &&
                                videoUrl.contains('youtube.com')) {
                              // 尝试从URL中提取YouTube视频ID
                              String? videoId;

                              // 处理youtube.com/embed/VIDEO_ID 格式
                              if (videoUrl.contains('/embed/')) {
                                final embedPattern =
                                    RegExp(r'youtube\.com/embed/([^?&]+)');
                                final match = embedPattern.firstMatch(videoUrl);
                                if (match != null && match.groupCount >= 1) {
                                  videoId = match.group(1);
                                }
                              }
                              // 处理youtube.com/watch?v=VIDEO_ID 格式
                              else if (videoUrl.contains('watch?v=')) {
                                videoId =
                                    YoutubePlayer.convertUrlToId(videoUrl);
                              }
                              // 处理youtube.com/v/VIDEO_ID 格式
                              else if (videoUrl.contains('/v/')) {
                                final vPattern =
                                    RegExp(r'youtube\.com/v/([^?&]+)');
                                final match = vPattern.firstMatch(videoUrl);
                                if (match != null && match.groupCount >= 1) {
                                  videoId = match.group(1);
                                }
                              }

                              if (videoId != null) {
                                // 使用自定义InAppWebView播放器
                                return VideoPlayerWidget(videoId: videoId);
                              }
                            }

                            // 如果不是YouTube视频或无法解析，显示占位符
                            return Container(
                              margin:
                                  const EdgeInsets.symmetric(vertical: 16.0),
                              height: 200.spx,
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(Icons.videocam, size: 40),
                                    const SizedBox(height: 8),
                                    Text(
                                      'content_tip'.tr,
                                      style: TextStyle(
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                    TextButton(
                                      onPressed: () async {
                                        if (videoUrl != null) {
                                          final uri = Uri.parse(videoUrl);
                                          if (await canLaunchUrl(uri)) {
                                            await launchUrl(uri);
                                          }
                                        }
                                      },
                                      child: Text('查看视频'.tr),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }

                          // 使用cacheimage
                          if (element.localName == 'img') {
                            final src = element.attributes['src'];
                            if (src != null) {
                              return GestureDetector(
                                onTap: () {
                                  openImagePreview(src);
                                },
                                child: CachedNetworkImage(
                                    imageUrl: src,
                                    width: MediaQuery.of(context).size.width,
                                    fit: BoxFit.contain,
                                    placeholder: (context, url) =>
                                        const LoadingIndicator(),
                                    errorWidget: (context, url, error) =>
                                        const SizedBox.shrink()),
                              );
                            }
                          }
                          // 处理iframe标签，特别是YouTube嵌入
                          if (element.localName == 'iframe') {
                            final src = element.attributes['src'];
                            if (src != null && src.contains('youtube.com')) {
                              // 尝试从URL中提取YouTube视频ID
                              String? videoId;

                              // 处理youtube.com/embed/VIDEO_ID 格式
                              if (src.contains('/embed/')) {
                                final embedPattern =
                                    RegExp(r'youtube\.com/embed/([^?&]+)');
                                final match = embedPattern.firstMatch(src);
                                if (match != null && match.groupCount >= 1) {
                                  videoId = match.group(1);
                                }
                              }
                              // 处理youtube.com/watch?v=VIDEO_ID 格式
                              else if (src.contains('watch?v=')) {
                                videoId = YoutubePlayer.convertUrlToId(src);
                              }
                              // 处理youtube.com/v/VIDEO_ID 格式
                              else if (src.contains('/v/')) {
                                final vPattern =
                                    RegExp(r'youtube\.com/v/([^?&]+)');
                                final match = vPattern.firstMatch(src);
                                if (match != null && match.groupCount >= 1) {
                                  videoId = match.group(1);
                                }
                              }

                              if (videoId != null) {
                                // 使用自定义InAppWebView播放器
                                return VideoPlayerWidget(videoId: videoId);
                              }
                            }

                            // 非YouTube iframe，显示占位符
                            return Container(
                              margin:
                                  const EdgeInsets.symmetric(vertical: 16.0),
                              height: 200,
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(Icons.web, size: 40),
                                    const SizedBox(height: 8),
                                    Text(
                                      'iframe_content'.tr,
                                      style: TextStyle(
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                    if (src != null)
                                      TextButton(
                                        onPressed: () async {
                                          final uri = Uri.parse(src);
                                          if (await canLaunchUrl(uri)) {
                                            await launchUrl(uri);
                                          }
                                        },
                                        child: Text('check_content'.tr),
                                      ),
                                  ],
                                ),
                              ),
                            );
                          }

                          return null; // 返回null使用默认渲染
                        },
                        // 全局样式配置
                        customStylesBuilder: (element) {
                          final Map<String, String> styles = {};

                          if (element.localName == 'p' ||
                              element.localName == 'span' ||
                              element.localName == 'div' ||
                              element.localName == 'h1' ||
                              element.localName == 'h2' ||
                              element.localName == 'h3' ||
                              element.localName == 'h4' ||
                              element.localName == 'h5' ||
                              element.localName == 'h6' ||
                              element.localName == 'li') {
                            styles['color'] = controller
                                .getTextColor()
                                .value
                                .toRadixString(16)
                                .padLeft(8, '0')
                                .substring(2);
                          }

                          // 处理mark标签，确保显示高亮背景色
                          if (element.localName == 'mark') {
                            // 从data-color属性获取高亮颜色
                            final String? highlightColor =
                                element.attributes['data-color'];
                            if (highlightColor != null) {
                              styles['background-color'] = highlightColor;
                            } else {
                              // 默认黄色高亮
                              styles['background-color'] = '#ffea9d';
                            }
                          }

                          if (element.localName == 'img') {
                            styles.addAll({
                              'display': 'block',
                              'margin': '8px 0',
                              'width': '100%',
                              'border-radius': '8px',
                              'max-width': '100%',
                              'height': 'auto',
                              'object-fit': 'contain',
                            });
                          }

                          // 添加video标签样式
                          if (element.localName == 'video') {
                            styles.addAll({
                              'display': 'block',
                              'margin': '16px 0',
                              'width': '100%',
                              'border-radius': '8px',
                              'max-width': '100%',
                              'height': 'auto',
                            });
                          }

                          if (element.localName == 'p') {
                            styles.addAll({
                              'margin': '0 0 16px 0',
                            });
                          }

                          if (element.localName == 'a') {
                            styles['color'] = Theme.of(context)
                                .primaryColor
                                .value
                                .toRadixString(16)
                                .padLeft(8, '0')
                                .substring(2);
                          }

                          return styles.isEmpty ? null : styles;
                        },
                        onTapUrl: (url) async {
                          try {
                            final uri = Uri.parse(url);
                            if (await canLaunchUrl(uri)) {
                              await launchUrl(uri);
                              return true;
                            }
                          } catch (e) {
                            print('Error launching URL: $e');
                          }
                          return false;
                        },
                      )),
                  // 文本选择变化回调
                  onSelectionChanged: (value) {
                    if (value != null && value.plainText.isNotEmpty) {
                      // 设置选中文本和状态
                      controller.selectedText.value = value.plainText;
                      controller.isTextSelected.value = true;

                      // 使用之前捕获的指针位置作为菜单位置
                      if (controller.lastTapPosition.value != Offset.zero) {
                        controller.selectionPosition.value =
                            controller.lastTapPosition.value;
                        print(
                            "使用捕获的指针位置: ${controller.selectionPosition.value}");
                      } else {
                        // 回退到计算选择位置
                        _getSelectionPosition(context);
                      }

                      print("文本已选中: ${value.plainText}");

                      // 显示自定义菜单，添加延迟以允许选区完全建立
                      if (!controller.isSelectionDragging.value) {
                        // 仅在不处于拖动状态时显示菜单
                        Future.delayed(const Duration(milliseconds: 150), () {
                          if (controller.isTextSelected.value &&
                              controller.selectedText.value.isNotEmpty &&
                              !controller.isSelectionDragging.value) {
                            _showCustomSelectionMenu(context);
                          }
                        });
                      }
                    } else {
                      // 没有文本被选中时不立即清除状态
                      // 延迟处理，避免影响菜单操作和选择调整
                      if (!controller.isTextSelected.value) {
                        controller.selectedText.value = '';
                      }
                    }
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示自定义文本选择菜单
  void _showCustomSelectionMenu(BuildContext context) {
    // 先移除旧的菜单（如果存在）
    _removeSelectionMenu();

    // 如果正在拖动中，不显示菜单
    if (controller.isSelectionDragging.value) {
      return;
    }

    // 创建自定义菜单
    _selectionMenuOverlay = OverlayEntry(
      builder: (BuildContext overlayContext) {
        // 获取屏幕尺寸
        final screenSize = MediaQuery.of(context).size;

        // 设置菜单尺寸
        final menuWidth = 240.0;
        final menuHeight = 70.0; // 减小高度

        // 获取选择区域的位置
        final position = controller.selectionPosition.value;
        print("建立菜单使用位置: $position");

        // 将菜单放在选择位置的上方
        double left = position.dx - (menuWidth / 2);
        double top = position.dy - menuHeight - 20; // 在选择区域上方20像素

        // 确保菜单不会超出屏幕边界
        if (left < 10) left = 10;
        if (left > screenSize.width - menuWidth - 10)
          left = screenSize.width - menuWidth - 10;

        // 如果菜单会超出屏幕顶部，才放在下方
        if (top < 10) {
          top = position.dy + 20; // 在选择区域下方20像素
        }

        print("菜单最终位置: left=$left, top=$top");

        return Stack(
          children: [
            // 定位菜单
            Positioned(
              left: left,
              top: top,
              child: Material(
                elevation: 4.0,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  width: menuWidth,
                  height: menuHeight,
                  decoration: BoxDecoration(
                    color: const Color(0xFF4e4e4e),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Flexible(
                        child: _buildVerticalMenuOptionItem(
                          "article_ask_ai".tr,
                          Image.asset(
                            'assets/images/logo-dark.png',
                            width: 18.spx,
                            height: 18.spx,
                          ),
                          () {
                            // 关闭菜单
                            _removeSelectionMenu();
                            // 获取选中的文本
                            final selectedText = controller.selectedText.value;
                            // 这里可以添加Ask AI的具体功能

                            // 显示AI对话框
                            // _showAskAiDialog(context, selectedText);
                            AskAiDialog(
                                    context: context,
                                    selectedText: selectedText,
                                    controller: controller)
                                .show();
                          },
                        ),
                      ),
                      Flexible(
                        child: _buildVerticalMenuOptionItem(
                          "notes".tr,
                          SvgPicture.asset(
                            'assets/icons/note-fill.svg',
                            width: 16.spx,
                            height: 16.spx,
                            colorFilter: const ColorFilter.mode(
                              Colors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                          () {
                            // 关闭菜单
                            _removeSelectionMenu();
                            // 获取选中的文本
                            final selectedText = controller.selectedText.value;
                            // 显示笔记弹窗
                            // _showNoteBottomSheet(selectedText: selectedText);
                            NoteBottomSheet(
                                    context: context,
                                    controller: controller,
                                    selectedText: selectedText)
                                .show();
                          },
                        ),
                      ),
                      Flexible(
                        child: _buildVerticalMenuOptionItem(
                          'share'.tr,
                          SvgPicture.asset(
                            'assets/icons/share.svg',
                            width: 16.spx,
                            height: 16.spx,
                            colorFilter: const ColorFilter.mode(
                              Colors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                          () {
                            // 关闭菜单
                            _removeSelectionMenu();
                            // 获取选中的文本
                            final selectedText = controller.selectedText.value;
                            // 显示分享选中文本的菜单
                            if (selectedText.isNotEmpty) {
                              ShareMenuSheet(
                                      context: context,
                                      controller: controller,
                                      selectedText: selectedText)
                                  .show();
                            } else {
                              // 没有选中文本时显示错误提示
                              Get.snackbar(
                                'error'.tr,
                                'no_text_selected'.tr,
                                snackPosition: SnackPosition.TOP,
                                icon:
                                    const Icon(Icons.error, color: Colors.red),
                                backgroundColor: const Color(0xFF161617),
                                colorText: Colors.white,
                              );
                            }
                            // 不立即清除选择，等用户操作完成后再清除
                          },
                        ),
                      ),
                      Flexible(
                        child: _buildVerticalMenuOptionItem(
                          'copy'.tr,
                          SvgPicture.asset(
                            'assets/icons/link.svg',
                            width: 16.spx,
                            height: 16.spx,
                            colorFilter: const ColorFilter.mode(
                              Colors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                          () async {
                            // 关闭菜单
                            _removeSelectionMenu();
                            // 获取选中的文本
                            final selectedText = controller.selectedText.value;
                            print('尝试复制文本: "$selectedText"');

                            if (selectedText.isNotEmpty) {
                              try {
                                // 添加iOS特定处理
                                if (GetPlatform.isIOS) {
                                  await Future.delayed(
                                      const Duration(milliseconds: 100));
                                }

                                // 复制文本到剪贴板
                                await Clipboard.setData(
                                    ClipboardData(text: selectedText));

                                // 添加震动反馈
                                HapticFeedback.lightImpact();

                                // 显示成功通知
                                Get.snackbar(
                                  'success'.tr,
                                  'text_copied_to_clipboard'.tr,
                                  snackPosition: SnackPosition.TOP,
                                  icon: SvgPicture.asset(
                                      'assets/feeds/right.svg'),
                                  backgroundColor: const Color(0xFF161617),
                                  colorText: Colors.white,
                                  duration: const Duration(seconds: 2),
                                );
                              } catch (e) {
                                // 错误处理
                                debugPrint('复制文本失败: $e');
                                Get.snackbar(
                                  'error'.tr,
                                  'copy_failed'.tr,
                                  snackPosition: SnackPosition.TOP,
                                  icon: const Icon(Icons.error,
                                      color: Colors.red),
                                  backgroundColor: const Color(0xFF161617),
                                  colorText: Colors.white,
                                );
                              }
                            } else {
                              // 没有选中文本时显示错误提示
                              Get.snackbar(
                                'error'.tr,
                                'no_text_selected'.tr,
                                snackPosition: SnackPosition.TOP,
                                icon:
                                    const Icon(Icons.error, color: Colors.red),
                                backgroundColor: const Color(0xFF161617),
                                colorText: Colors.white,
                              );
                            }

                            // 清除选择
                            _clearSelectionSafely(context);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );

    // 显示菜单
    Overlay.of(context).insert(_selectionMenuOverlay!);
  }

  /// 移除选择菜单
  void _removeSelectionMenu() {
    _selectionMenuOverlay?.remove();
    _selectionMenuOverlay = null;
  }

  /// 获取文本选择的真实位置
  void _getSelectionPosition(BuildContext context) {
    // 获取渲染对象
    final RenderBox? box = context.findRenderObject() as RenderBox?;

    if (box == null) {
      print("无法获取渲染对象");
      return;
    }

    // 尝试获取焦点节点中的选择位置
    final FocusNode? focusNode = FocusManager.instance.primaryFocus;
    final BuildContext? focusContext = focusNode?.context;
    Offset? selectionPosition;

    if (focusContext != null) {
      // 尝试从焦点上下文中获取选择信息
      final RenderObject? renderObj = focusContext.findRenderObject();
      if (renderObj is RenderEditable) {
        // 从RenderEditable获取文本选择的位置
        final TextSelection? selection = renderObj.selection;

        if (selection != null && !selection.isCollapsed) {
          // 获取选择的边界框并计算中心位置
          final List<TextBox> boxes = renderObj.getBoxesForSelection(selection);
          if (boxes.isNotEmpty) {
            // 使用第一个选择框的中心点
            final TextBox selectionBox = boxes.first;
            final Offset localOffset = Offset(
                selectionBox.toRect().center.dx, selectionBox.toRect().top);

            // 转换为全局坐标
            selectionPosition = renderObj.localToGlobal(localOffset);
            print("从RenderEditable获取选择位置: $selectionPosition");
          }
        }
      }
    }

    // 如果上面的方法失败，尝试使用更通用的方法
    if (selectionPosition == null) {
      // 尝试直接读取当前手势的位置
      try {
        // 获取可视区域中心偏上位置
        final ScrollableState? scrollable = Scrollable.maybeOf(context);
        if (scrollable != null) {
          final RenderBox scrollRenderBox =
              scrollable.context.findRenderObject() as RenderBox;
          final scrollPosition = scrollable.position;

          final visibleArea = scrollPosition.viewportDimension;
          final scrollOffset = scrollPosition.pixels;

          // 视口中间偏上位置
          final offset = scrollOffset + (visibleArea * 0.3);
          selectionPosition = scrollRenderBox
              .localToGlobal(Offset(scrollRenderBox.size.width / 2, offset));

          print("使用滚动视图位置: $selectionPosition");
        } else {
          // 使用渲染框的中心作为位置
          selectionPosition = box
              .localToGlobal(Offset(box.size.width / 2, box.size.height * 0.3));
          print("使用渲染框中心位置: $selectionPosition");
        }
      } catch (e) {
        print('计算位置时出错: $e');
        // 使用屏幕中心偏上位置
        final Size screenSize = MediaQuery.of(context).size;
        selectionPosition =
            Offset(screenSize.width / 2, screenSize.height * 0.3);
        print("使用屏幕中心位置");
      }
    }

    // 设置最终位置
    controller.selectionPosition.value = selectionPosition!;
    print("设置菜单位置: ${controller.selectionPosition.value}");
  }

  // 安全地清除选择，避免异常
  void _clearSelectionSafely(BuildContext context) {
    // 移除菜单
    _removeSelectionMenu();

    // 避免在用户正在调整选择时清除选择
    if (controller.selectedText.value.isEmpty) {
      return; // 如果没有选择内容，直接返回
    }

    // 确保清除选择状态
    controller.isTextSelected.value = false;
    controller.selectedText.value = '';
    controller.lastTapPosition.value = Offset.zero;

    try {
      // 通过解除焦点尝试清除选择
      FocusManager.instance.primaryFocus?.unfocus();
    } catch (e) {
      print('安全清除选中文本时发生错误: $e');
    }

    // 更新UI状态
    controller.update();

    // 延迟一点时间再次尝试清除选择
    Future.delayed(const Duration(milliseconds: 200), () {
      try {
        // 再次尝试获取当前焦点并清除
        FocusManager.instance.primaryFocus?.unfocus();

        // 强制触发界面刷新
        controller.isTextSelected.value = false;
        controller.update();
      } catch (e) {
        print('延迟清除文本选择时出错: $e');
      }
    });
  }

  /// 根据拖动更新文本选区
  void _updateSelectionOnDrag(BuildContext context, Offset currentPosition) {
    try {
      // 计算拖动的方向和距离
      final dragDelta =
          currentPosition - controller.selectionDragStartPosition.value;

      // 更新选区位置
      controller.selectionPosition.value = currentPosition;

      // 尝试获取焦点节点
      final FocusNode? focusNode = FocusManager.instance.primaryFocus;
      final BuildContext? focusContext = focusNode?.context;

      // 如果已经有焦点节点，尝试扩展选区
      if (focusContext != null) {
        final RenderObject? renderObj = focusContext.findRenderObject();
        if (renderObj is RenderEditable) {
          // 这里我们使用Flutter底层API尝试调整选区
          // 注意：这在实际应用中可能需要更复杂的实现

          // 获取当前选区
          final TextSelection? selection = renderObj.selection;

          if (selection != null && !selection.isCollapsed) {
            // 我们有一个非折叠的选区，根据拖动方向调整边界

            // 判断拖动方向
            final isDraggingForward =
                dragDelta.dx > 0 || (dragDelta.dx == 0 && dragDelta.dy > 0);

            // 更新选区状态
            controller.update();

            print(
                "选区更新: isDraggingForward=$isDraggingForward, delta=$dragDelta");
          }
        }
      } else {
        // 无法获取具体的文本选择对象，只能通知系统更新
        controller.update();
      }

      print("拖动更新选区: $currentPosition, 距离开始点: $dragDelta");
    } catch (e) {
      print('更新选区时出错: $e');
    }
  }

  /// 处理选择拖动结束事件
  void _handleSelectionDragEnd(BuildContext context, Offset endPosition) {
    try {
      // 计算总体拖动距离
      final totalDragDistance =
          (endPosition - controller.selectionDragStartPosition.value).distance;

      if (totalDragDistance > 10) {
        // 有明显的拖动距离，更新选区
        controller.selectionPosition.value = endPosition;

        // 获取当前选择区域
        final String selectedText = controller.selectedText.value;

        if (selectedText.isNotEmpty) {
          // 如果有选中内容，显示选择菜单
          print("选择拖动结束，选中内容: $selectedText");

          // 延迟一点时间，让系统先完成选择操作
          Future.delayed(const Duration(milliseconds: 200), () {
            if (controller.isTextSelected.value) {
              // 显示自定义菜单
              _showCustomSelectionMenu(context);
            }
          });
        }
      }

      // 重置拖动状态
      controller.selectionDragStartPosition.value = Offset.zero;

      // 更新UI
      controller.update();
    } catch (e) {
      print('处理选择拖动结束时出错: $e');
    }
  }

  /// 构建垂直排列的菜单选项
  Widget _buildVerticalMenuOptionItem(
      String label, Widget icon, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon,
              SizedBox(height: 4.spx),
              Text(
                label,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12.spx,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理双击选择词语
  void _handleDoubleTapToSelectWord(BuildContext context) {
    try {
      // 获取当前点击位置附近的文本
      final position = controller.lastTapPosition.value;
      // 更新选区位置以便显示菜单
      controller.selectionPosition.value = position;

      // 在真实实现中，这里应该找到点击位置附近的词语并选中它
      // 由于这取决于具体的文本布局和渲染，我们这里只做一个模拟实现

      // 延迟一下以等待系统处理双击事件
      Future.delayed(const Duration(milliseconds: 100), () {
        // 通知系统选区已更新
        controller.update();
      });
    } catch (e) {
      print('双击选择词语时出错: $e'); // 双击选择词语时出错: Instance of 'NullPointerException'
    }
  }

  /// 打开图片预览
  void openImagePreview(String imageUrl) {
    final controller = Get.find<ArticleController>();
    final imageUrls = controller.extractImageUrls();
    if (imageUrls.isEmpty) {
      return;
    }

    final initialIndex = controller.getImageIndex(imageUrl);
    if (initialIndex == -1) {
      return;
    }

    Navigator.of(Get.context!).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            ImagePreviewScreen(
          imageUrls: imageUrls,
          initialIndex: initialIndex,
          isDark: controller.isDarkMode.value,
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
        reverseTransitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}

/// 扩展类用于处理十六进制颜色转换
extension HexColor on Color {
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 7) {
      buffer.write('ff');
      buffer.write(hexString.replaceFirst('#', ''));
    } else {
      buffer.write(hexString.replaceFirst('#', ''));
    }
    return Color(int.parse(buffer.toString(), radix: 16));
  }
}

// 在类内添加VideoPlayerWidget类
class VideoPlayerWidget extends StatefulWidget {
  final String videoId;

  const VideoPlayerWidget({Key? key, required this.videoId}) : super(key: key);

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  bool _isLoading = true;
  late YoutubePlayerController _controller;

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  void _initializeController() {
    _controller = YoutubePlayerController(
      initialVideoId: widget.videoId,
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
        disableDragSeek: false,
        loop: false,
        enableCaption: true,
        forceHD: false,
      ),
    );

    // 添加控制器监听器
    _controller.addListener(() {
      if (mounted && _isLoading && _controller.value.isReady) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 220,
      margin: const EdgeInsets.symmetric(vertical: 16.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.black,
      ),
      clipBehavior: Clip.hardEdge,
      child: YoutubePlayer(
        controller: _controller,
        showVideoProgressIndicator: true,
        progressIndicatorColor: Theme.of(context).primaryColor,
        progressColors: ProgressBarColors(
          playedColor: Theme.of(context).primaryColor,
          handleColor: Theme.of(context).primaryColor,
        ),
        onReady: () {
          setState(() {
            _isLoading = false;
          });
          debugPrint('YouTube播放器就绪');
        },
      ),
    );
  }

  /// 打开图片预览
  void openImagePreview(String imageUrl) {
    final controller = Get.find<ArticleController>();
    final imageUrls = controller.extractImageUrls();
    if (imageUrls.isEmpty) {
      return;
    }

    final initialIndex = controller.getImageIndex(imageUrl);
    if (initialIndex == -1) {
      return;
    }

    Navigator.of(Get.context!).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            ImagePreviewScreen(
          imageUrls: imageUrls,
          initialIndex: initialIndex,
          isDark: controller.isDarkMode.value,
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
        reverseTransitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}
