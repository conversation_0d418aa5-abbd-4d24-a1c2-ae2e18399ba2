import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:intl/intl.dart';
import 'package:subfeeds/app/data/repositories/article_repository.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// 文章详情页控制器
class ArticleController extends GetxController {
  final ArticleRepository _articleRepository = ArticleRepository();

  // 文章数据
  final article = Rx<Map<String, dynamic>>({});
  final currentLanguage = ''.obs;
  // 是否已收藏
  final isBookmarked = false.obs;
  final isDarkMode = false.obs;
  // 是否已加入稍后阅读
  final isReadLater = false.obs;
  // 是否已订阅该源
  final isSubscribed = false.obs;

  // 创建静态事件流，用于在文章状态改变时通知其他控制器
  static final articleStatusChanged =
      StreamController<Map<String, dynamic>>.broadcast();

  // 阅读设置
  final fontSize = 16.0.obs;
  final fontFamily = 'Roboto'.obs;
  final lineHeight = 1.5.obs;

  // 阅读模式
  final isImmersiveMode = false.obs;

  // 添加主题样式状态
  final backgroundColor = const Color(0xFFF7F7F7).obs;

  // 添加文本选择相关属性
  final isTextSelected = false.obs;
  final selectedText = ''.obs;
  final selectionPosition = Offset.zero.obs;

  // 记录最后的长按/点击位置，用于更准确地定位菜单
  final lastTapPosition = Offset.zero.obs;

  // 缓存提取的图片URL列表
  List<String> _cachedImageUrls = [];

  // 添加拖动选择相关的变量
  final selectionDragStartPosition = Offset.zero.obs;
  final isSelectionDragging = false.obs;

  // 主题样式列表
  final List<Color> themeColors = [
    const Color(0xFFF7F7F7),
    const Color(0xFFE2ECD5),
    const Color(0xFFF2ECD7),
    const Color(0xFF596774),
    const Color(0xFF213D4F),
    const Color(0xFF2D2D2D),
  ];

  // 笔记相关属性
  final selectedNoteColor = '#ffea9d'.obs; // 默认高亮颜色
  final availableNoteColors = [
    '#ffea9d',
    '#c1ff9d',
    '#9dd5ff',
    '#af9dff',
    '#ea9dff',
    '#ff9d9d',
  ];

  @override
  void onInit() {
    super.onInit();
    getIsDarkMode();
    getCurrentLanguage();
    // 从路由参数获取文章数据
    print('获取到的参数: ${Get.arguments['article']['isSub']}');

    if (Get.arguments != null && Get.arguments['article'] != null) {
      final rawArticle = Map<String, dynamic>.from(Get.arguments['article']);

      // 设置初始状态
      isBookmarked.value = rawArticle['isCollect'] == 1;
      isReadLater.value = rawArticle['isLaterRead'] == 1;
      // 设置订阅状态
      isSubscribed.value = rawArticle['isSub'] == 1;
      debugPrint('订阅状态: ${rawArticle['isSub']},${isSubscribed.value}');
      // 转换数据格式
      article.value = {
        'id': rawArticle['id'],
        'title': rawArticle['title'] ?? '无标题',
        'publishDate':
            _formatDateTime(rawArticle['pubDate'] ?? rawArticle['createTime']),
        'source': rawArticle['creator'] ?? '未知来源',
        'feedsId': rawArticle['feedsId'] ?? '',
        'suffixTable': rawArticle['suffixTable'] ?? '',
        'url': rawArticle['link'] != null && rawArticle['link'].isNotEmpty
            ? rawArticle['link']
            : 'https://www.subfeeds.com',
        'content': rawArticle['description'] ?? '暂无内容',
        'language': rawArticle['language'] ?? 'en',
        'feedsName': rawArticle['feedsName'] ?? rawArticle['creator'] ?? '未知来源',
        'isCollect': rawArticle['isCollect'] ?? 0,
        'isLaterRead': rawArticle['isLaterRead'] ?? 0,
        'isRead': rawArticle['isRead'] ?? 0,
        'img': rawArticle['img'] ?? '',
        'isHome': Get.arguments['isHome'] ?? false,
        'isSub': rawArticle['isSub'] ?? 0,
        'type': rawArticle['type'] ?? 1,
        'feedId': rawArticle['feedsId'] ?? '',
      };
    } else {
      article.value = {
        'title': '测试文章标题',
        'publishDate': '2023-02-25',
        'source': '测试来源',
        'content': '这是一篇测试文章的内容...',
        'url': 'https://example.com/test-article',
      };
    }
    // 加载阅读设置
    _loadReadingSettings();
  }

  @override
  void onClose() {
    // 控制器被销毁时关闭事件流
    // 注意：因为是静态流，其他地方可能还在使用，这里不关闭
    // articleStatusChanged.close();
    super.onClose();
  }

  // 获取当前语言
  Future<void> getCurrentLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    print('获取到的语言: ${prefs.getString('locale')}');
    currentLanguage.value = prefs.getString('locale') == 'en_US' ? 'en' : 'zh';
  }

  // 获取当前主题
  Future<bool> getIsDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    isDarkMode.value = prefs.getBool('isDarkMode') ?? false;
    return isDarkMode.value;
  }

  /// 格式化时间戳为日期字符串
  String _formatDateTime(dynamic time) {
    try {
      if (time == null) return '';

      DateTime dateTime;
      if (time is String) {
        // 处理pubDate格式
        dateTime = DateTime.parse(time);
      } else if (time is int) {
        // 处理createTime时间戳
        dateTime = DateTime.fromMillisecondsSinceEpoch(time * 1000);
      } else {
        return '';
      }

      final now = DateTime.now();
      final difference = now.difference(dateTime);

      // 1天以内，返回小时
      if (difference.inDays < 1) {
        final hours = difference.inHours;
        return '${hours}H';
      }
      // 30天以内，返回天数
      else if (difference.inDays < 30) {
        return '${difference.inDays}D';
      }
      // 超过30天，返回完整日期
      else {
        return DateFormat('yyyy-MM-dd').format(dateTime);
      }
    } catch (e) {
      debugPrint('时间格式化失败: $e');
      return '';
    }
  }

  /// 加载阅读设置
  Future<void> _loadReadingSettings() async {
    final prefs = await SharedPreferences.getInstance();
    try {
      // 尝试获取fontSizeValue(double)，如果失败则根据fontSize(String)转换
      fontSize.value = prefs.getDouble('fontSizeValue') ?? 16.0;
    } catch (e) {
      // 如果fontSizeValue不存在，则尝试从fontSize(String)中获取并转换
      String? fontSizeStr = prefs.getString('fontSize');
      if (fontSizeStr != null) {
        switch (fontSizeStr) {
          case '小号':
            fontSize.value = 14.0;
            break;
          case '大号':
            fontSize.value = 18.0;
            break;
          case '中号':
          default:
            fontSize.value = 16.0;
            break;
        }
      } else {
        fontSize.value = 16.0;
      }
    }

    fontFamily.value = prefs.getString('fontFamily') ?? 'Roboto';
    lineHeight.value = prefs.getDouble('lineHeight') ?? 1.5;

    // 加载背景色
    final savedColor = prefs.getInt('backgroundColor');

    if (savedColor != null) {
      // 如果当前主题与保存的主题一致，则使用保存的主题
      final isCurrentDarkMode = isDarkBackground(Color(savedColor));
      if (isDarkMode.value == isCurrentDarkMode) {
        backgroundColor.value = Color(savedColor);
      } else {
        // 如果当前主题与保存的主题不一致，则使用当前主题
        backgroundColor.value =
            isDarkMode.value ? Color(0xFF2D2D2D) : Color(0xFFF7F7F7);
        isDarkMode.value = isDarkMode.value;
      }
    } else {
      backgroundColor.value =
          isDarkMode.value ? Color(0xFF2D2D2D) : Color(0xFFF7F7F7);
    }
  }

  /// 保存阅读设置
  Future<void> saveReadingSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('fontSizeValue', fontSize.value);
    await prefs.setString('fontFamily', fontFamily.value);
    await prefs.setDouble('lineHeight', lineHeight.value);
    // 保存背景色
    await prefs.setInt('backgroundColor', backgroundColor.value.value);
  }

  /// 切换收藏状态
  Future<void> toggleBookmark() async {
    try {
      if (article.value.isEmpty || article.value['id'] == null) return;

      final int articleId = int.parse(article.value['id'].toString());

      // 先更新UI状态
      isBookmarked.value = !isBookmarked.value;

      // 发送API请求
      final response = isBookmarked.value
          ? await _articleRepository.insertCollect(
              articleId,
              int.tryParse(article.value['feedsId'].toString()) ?? -1,
              article.value['suffixTable'] ?? '',
            )
          : await _articleRepository.deleteCollect([articleId]);

      if (response.isSuccess) {
        // 更新文章状态
        article.value['isCollect'] = isBookmarked.value ? 1 : 0;

        // 同步更新首页的文章状态
        final homeController = Get.find<HomeController>();
        final index = homeController.articles.indexWhere(
            (item) => item['id'].toString() == articleId.toString());
        if (index != -1) {
          homeController.articles[index]['isCollect'] =
              isBookmarked.value ? 1 : 0;
          homeController.filteredArticles.refresh();
        }

        // 发送事件通知其他控制器
        articleStatusChanged.sink.add({
          'id': articleId.toString(),
          'isCollect': isBookmarked.value ? 1 : 0,
          'isLaterRead': article.value['isLaterRead'],
        });
      } else {
        // 如果请求失败，回退状态
        isBookmarked.value = !isBookmarked.value;
        Get.snackbar(
          'error'.tr,
          response.msg,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // 发生错误，回退状态
      isBookmarked.value = !isBookmarked.value;
      debugPrint('切换收藏状态失败: $e');
      Get.snackbar(
        'error'.tr,
        'article_collect_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 切换稍后阅读状态
  Future<void> toggleReadLater() async {
    try {
      if (article.value.isEmpty || article.value['id'] == null) return;

      final int articleId = int.parse(article.value['id'].toString());

      // 先更新UI状态
      isReadLater.value = !isReadLater.value;

      // 发送API请求
      final response = isReadLater.value
          ? await _articleRepository.setReadLater(
              articleId,
              int.tryParse(article.value['feedsId'].toString()) ?? -1,
              article.value['suffixTable'] ?? '',
            )
          : await _articleRepository.deleteLaterRead([articleId]);

      if (response.isSuccess) {
        // 更新文章状态
        article.value['isLaterRead'] = isReadLater.value ? 1 : 0;

        // 同步更新首页的文章状态
        final homeController = Get.find<HomeController>();
        final index = homeController.articles.indexWhere(
            (item) => item['id'].toString() == articleId.toString());
        if (index != -1) {
          homeController.articles[index]['isLaterRead'] =
              isReadLater.value ? 1 : 0;
          homeController.filteredArticles.refresh();
        }

        // 发送事件通知其他控制器
        articleStatusChanged.sink.add({
          'id': articleId.toString(),
          'isCollect': article.value['isCollect'],
          'isLaterRead': isReadLater.value ? 1 : 0,
        });
      } else {
        // 如果请求失败，回退状态
        isReadLater.value = !isReadLater.value;
        Get.snackbar(
          'error'.tr,
          response.msg,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // 发生错误，回退状态
      isReadLater.value = !isReadLater.value;
      debugPrint('切换稍后阅读状态失败: $e');
      Get.snackbar(
        'error'.tr,
        'article_readlater_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 切换阅读模式
  void toggleImmersiveMode() {
    isImmersiveMode.value = !isImmersiveMode.value;
  }

  /// 设置字体大小
  void setFontSize(double size) {
    fontSize.value = size;
    saveReadingSettings();
  }

  /// 设置字体
  void setFontFamily(String family) {
    fontFamily.value = family;
    saveReadingSettings();
  }

  /// 设置行高
  void setLineHeight(double height) {
    lineHeight.value = height;
    saveReadingSettings();
  }

  /// 设置背景颜色
  Future<void> setBackgroundColor(Color color) async {
    backgroundColor.value = color;
    isDarkMode.value = isDarkBackground(color);
    // 保存到本地
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', isDarkMode.value);
    // 保存背景色设置
    saveReadingSettings();
    // 强制刷新，确保所有使用 getTextColor 的地方都更新
    update();
  }

  /// 分享到其他app
  Future<void> shareOtherApp(
      {required String appType, String text = ''}) async {
    final url = article.value['url'];
    final title = article.value['title'];
    if (url == null || url.isEmpty) return;
    // 判断是否为文字分享
    bool isShareText = text.isNotEmpty;
    String shareUrl = '';
    switch (appType) {
      case 'Facebook':
        shareUrl = Uri.encodeFull(
            'https://www.facebook.com/sharer/sharer.php?u=$url&quote=${isShareText ? title : text}');
        break;
      case 'X':
      case 'Twitter':
        shareUrl = Uri.encodeFull(
            'https://twitter.com/intent/tweet?text=${isShareText ? title : text}&url=$url');
        break;
      case 'LinkedIn':
        shareUrl = isShareText
            ? Uri.encodeFull(
                'https://www.linkedin.com/sharing/share-offsite/?url=$url&summary=$text')
            : Uri.encodeFull(
                'https://www.linkedin.com/sharing/share-offsite/?url=$url');
        break;
      case 'Reddit':
        shareUrl = isShareText
            ? Uri.encodeFull(
                'https://www.reddit.com/submit?url=$url&title=${text.length > 100 ? '${text.substring(0, 100)}...' : text}')
            : Uri.encodeFull(
                'https://www.reddit.com/submit?url=$url&title=$title');
      case 'Pinterest':
        // text.length > 100 ? '${text.substring(0, 100)}...' : text
        shareUrl = isShareText
            ? Uri.encodeFull(
                'https://pinterest.com/pin/create/button/?url=$url&description=${text.length > 100 ? '${text.substring(0, 100)}...' : text}')
            : Uri.encodeFull(
                'https://pinterest.com/pin/create/button/?url=$url&description=$title');
        break;
      case 'Telegram':
        shareUrl =
            Uri.encodeFull('https://t.me/share/url?url=$url&text=$title');
        break;
      case 'Instagram':
        if (isShareText) {
          shareTextToSystem(text);
          return;
        } else {
          shareUrl = Uri.encodeFull(
              'https://www.instagram.com/share?url=$url&caption=$title');
          break;
        }
      default:
        return;
    }
    await _launchUrl(shareUrl);
  }

  /// 通过系统分享菜单分享选中文本
  Future<void> shareTextToSystem(String text) async {
    try {
      // 这里需要使用插件share_plus
      // 由于没有导入这个插件，我们可以添加以下代码：
      // await Share.share(text);

      // 作为替代，我们可以使用剪贴板并显示提示
      await Clipboard.setData(ClipboardData(text: text));
      Get.snackbar(
        'success'.tr,
        'text_copied_use_system_share'.tr,
        snackPosition: SnackPosition.TOP,
        backgroundColor: const Color(0xFF161617),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      debugPrint('系统分享失败: $e');
      Get.snackbar(
        'error'.tr,
        'share_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: const Color(0xFF161617),
        colorText: Colors.white,
      );
    }
  }

  /// 启动URL
  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        Get.snackbar(
          'error'.tr,
          'cannot_open_url'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      debugPrint('启动URL失败: $e');
      Get.snackbar(
        'error'.tr,
        'share_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  // 判断是否为深色背景
  bool isDarkBackground(Color color) {
    // 计算颜色的亮度，亮度小于0.5认为是深色
    return color.computeLuminance() < 0.5;
  }

  // 获取基于背景色的文本颜色
  Color getTextColor() {
    return isDarkBackground(backgroundColor.value)
        ? Colors.white
        : const Color(0xFF333333);
  }

  /// 从文章内容中提取所有图片URL
  List<String> extractImageUrls() {
    if (_cachedImageUrls.isNotEmpty) {
      return _cachedImageUrls;
    }

    final content = article.value['content'] ?? '';
    if (content.isEmpty) {
      return [];
    }

    try {
      // 使用正则表达式提取img标签的src属性（参考article_item.dart的实现）
      final RegExp imgRegExp =
          RegExp(r'<img[^>]+src="([^"]+)"[^>]*>', caseSensitive: false);
      final matches = imgRegExp.allMatches(content);

      final List<String> imageUrls = [];
      for (final match in matches) {
        String? src = match.group(1);
        if (src != null && src.isNotEmpty) {
          // 处理相对URL
          if (src.startsWith('//')) {
            src = 'https:$src';
          } else if (src.startsWith('/')) {
            // 如果是相对路径，需要基于文章来源构建完整URL
            final articleUrl = article.value['link'] ?? '';
            if (articleUrl.isNotEmpty) {
              try {
                final uri = Uri.parse(articleUrl);
                src = '${uri.scheme}://${uri.host}$src';
              } catch (e) {
                debugPrint('解析文章URL失败: $e');
              }
            }
          }

          if (src?.startsWith('http') ?? false) {
            imageUrls.add(src ?? '');
          }
        }
      }

      _cachedImageUrls = imageUrls;
      return _cachedImageUrls;
    } catch (e) {
      debugPrint('提取图片URL失败: $e');
      return [];
    }
  }

  /// 获取指定图片URL在图片列表中的索引
  int getImageIndex(String imageUrl) {
    final imageUrls = extractImageUrls();
    return imageUrls.indexOf(imageUrl);
  }

  /// 清除图片URL缓存（当文章内容改变时调用）
  void clearImageUrlCache() {
    _cachedImageUrls.clear();
  }

  /// 添加或更新笔记
  Future<bool> addOrUpdateNote(
    String selectedContent,
    String noteContent, [
    int deleteTag = 0, // 0: 新增, 1: 删除, 2: 修改
  ]) async {
    try {
      if (article.value.isEmpty || article.value['id'] == null) return false;

      final articleId = article.value['id'].toString();
      final feedsId = article.value['feedsId']?.toString() ?? '';

      String updatedContent;
      if (deleteTag == 1) {
        // 删除操作：移除mark标签，保留原文本
        updatedContent = _removeHighlightMark(selectedContent);
      } else {
        // 新增或修改操作：添加或更新高亮标记
        updatedContent = _addHighlightMarkToContent(
          selectedContent,
          noteContent,
          selectedNoteColor.value,
        );
      }

      // 构建noteList
      final noteList = [
        {
          'content': noteContent.trim(),
          'deleteTag': deleteTag,
          'selectContent': selectedContent,
          'color': selectedNoteColor.value,
        }
      ];

      // 调用API保存笔记
      final response = await _articleRepository.manageUserNote(
        articleId: articleId,
        content: updatedContent,
        feedsId: feedsId,
        noteList: noteList,
        suffixTable: article.value['suffixTable'],
      );

      if (response.isSuccess) {
        // 更新文章内容
        article.value = {
          ...article.value,
          'content': updatedContent,
        };
        update();
        return true;
      } else {
        Get.snackbar(
          'error'.tr,
          response.msg,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return false;
      }
    } catch (e) {
      debugPrint('添加笔记失败: $e');
      Get.snackbar(
        'error'.tr,
        'note_add_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      return false;
    }
  }

  /// 移除高亮标记，保留原文本
  String _removeHighlightMark(String text) {
    try {
      String originalContent = article.value['content'] ?? '';

      // 查找并替换整个mark标签
      final RegExp markRegex = RegExp(r'<mark[^>]*>([^<]*)</mark>');
      final match = markRegex.firstMatch(text);

      if (match != null) {
        final plainText = match.group(1) ?? '';
        originalContent = originalContent.replaceFirst(text, plainText);
      }

      return originalContent;
    } catch (e) {
      debugPrint('移除高亮标记失败: $e');
      return article.value['content'] ?? '';
    }
  }

  /// 检查文本是否已被高亮
  bool isTextHighlighted(String text) {
    return text.contains('<mark');
  }

  /// 在文章内容中添加高亮标记
  String _addHighlightMarkToContent(
      String selectedText, String noteContent, String color) {
    try {
      // 获取当前文章内容
      String originalContent = article.value['content'] ?? '';

      // 检查是否是编辑现有笔记
      if (selectedText.contains('<mark')) {
        // 解析现有的mark标签
        final RegExp markRegex = RegExp(
          r'<mark[^>]*data-popover-id="([^"]*)"[^>]*>([^<]*)</mark>',
          caseSensitive: false,
        );
        final match = markRegex.firstMatch(selectedText);

        if (match != null) {
          final existingPopoverId = match.group(1) ?? '';
          final highlightedText = match.group(2) ?? '';

          // 创建新的mark标签，保持原有的popover-id
          final String markTag =
              '<mark data-markjs="true" class="add-note-mark" ' +
                  'data-popover-id="$existingPopoverId" ' +
                  'data-content="$noteContent" ' +
                  'data-date="${DateFormat('M-d-yyyy').format(DateTime.now())}" ' +
                  'data-color="$color" ' +
                  'style="background-color: $color;">' +
                  highlightedText +
                  '</mark>';

          // 直接替换整个mark标签
          originalContent = originalContent.replaceFirst(selectedText, markTag);

          debugPrint('更新现有mark标签成功');
          return originalContent;
        }
      }

      // 如果不是编辑现有笔记，执行原有的添加新笔记逻辑
      // 生成唯一ID
      final String popoverId =
          'popover-${DateTime.now().millisecondsSinceEpoch}-${selectedText.hashCode}';
      final String date = DateFormat('M-d-yyyy').format(DateTime.now());

      // 确保颜色格式转换正确
      final String formattedColor = color.startsWith('#') ? color : '#$color';

      // 创建mark标签，使用改进的样式组合来防止换行和断开
      final String markTag =
          '<mark data-markjs="true" class="add-note-mark" data-popover-id="$popoverId" data-content="$noteContent" data-date="$date" data-color="$formattedColor" style="background-color: $formattedColor; display: inline; white-space: normal; vertical-align: baseline;">$selectedText</mark>';

      debugPrint('创建高亮标记: $markTag');

      // 先尝试采用改进的算法查找并替换选中文本
      int startPos = -1;
      // 使用简单查找尝试定位选中文本
      startPos = originalContent.indexOf(selectedText);

      if (startPos != -1) {
        // 检查选中的文本前后是否有标签，避免破坏HTML结构
        // 检查该文本是否在标签内部
        bool isInTag = false;
        int openTagPos = originalContent.lastIndexOf('<', startPos);
        int closeTagPos = originalContent.lastIndexOf('>', startPos);

        if (openTagPos > closeTagPos) {
          // 文本可能在标签内部
          closeTagPos = originalContent.indexOf('>', startPos);
          if (closeTagPos != -1) {
            String tag = originalContent.substring(openTagPos, closeTagPos + 1);
            if (!tag.startsWith('</') && !tag.endsWith('/>')) {
              isInTag = true;
            }
          }
        }

        if (!isInTag) {
          // 确保不在标签内部，执行替换
          originalContent = originalContent.substring(0, startPos) +
              markTag +
              originalContent.substring(startPos + selectedText.length);
          debugPrint('直接替换文本为高亮标记成功');
        } else {
          debugPrint('文本位于HTML标签内部，跳过替换');
          // 尝试在HTML处理后的纯文本中查找
          // 简单地移除HTML标签来获取纯文本
          String plainText = originalContent.replaceAll(RegExp(r'<[^>]*>'), '');
          int plainTextPos = plainText.indexOf(selectedText);

          if (plainTextPos != -1) {
            debugPrint('在纯文本中找到了位置，但无法精确替换HTML中的位置');
          }
        }
      } else {
        // 使用更保守的方法，特别处理HTML实体
        debugPrint('无法直接找到文本，尝试更保守的搜索');
        // 将originalContent中的所有<mark>标签替换为空字符串后搜索
        String contentWithoutMarks =
            originalContent.replaceAll(RegExp(r'<mark[^>]*>|</mark>'), '');
        startPos = contentWithoutMarks.indexOf(selectedText);

        if (startPos != -1) {
          // 找到位置，但需要映射回原始内容
          // 这里可能需要更复杂的逻辑来处理
          debugPrint('在去除mark标签后找到了文本，但可能无法精确替换');
        }
      }

      // 如果所有方法都失败，提示用户
      if (startPos == -1) {
        debugPrint('无法找到文本的精确位置，请尝试选择更具体的文本');
        // 尝试一次最后的字符串替换，但这可能导致HTML格式问题
        if (originalContent.contains(selectedText)) {
          originalContent = originalContent.replaceFirst(
              selectedText,
              markTag,
              originalContent.indexOf('</body>') > 0
                  ? originalContent.indexOf('<body>')
                  : 0);
          debugPrint('使用最后的替换方法，可能会影响HTML结构');
        }
      }

      // 更新文章内容
      article.value = {
        ...article.value,
        'content': originalContent,
      };

      return originalContent;
    } catch (e) {
      debugPrint('添加高亮标记失败: $e');
      return article.value['content'] ?? '';
    }
  }

  /// 设置笔记高亮颜色
  void setNoteHighlightColor(String color) {
    selectedNoteColor.value = color;
  }

  /// 从文章内容中提取所有笔记
  List<Map<String, dynamic>> getAllNotesFromContent() {
    try {
      final String content = article.value['content'] ?? '';
      final List<Map<String, dynamic>> notes = [];

      // 使用正则表达式查找所有mark标签
      final RegExp markRegex = RegExp(
        r'<mark[^>]*data-content="([^"]*)"[^>]*data-date="([^"]*)"[^>]*data-color="([^"]*)"[^>]*>([^<]*)</mark>',
        caseSensitive: false,
      );

      final matches = markRegex.allMatches(content);

      for (final match in matches) {
        if (match.groupCount >= 4) {
          final noteContent = match.group(1) ?? '';
          final date = match.group(2) ?? '';
          final color = match.group(3) ?? '';
          final highlightedText = match.group(4) ?? '';
          final fullMarkText = match.group(0) ?? '';

          notes.add({
            'noteContent': noteContent,
            'date': date,
            'color': color,
            'highlightedText': highlightedText,
            'fullMarkText': fullMarkText,
          });
        }
      }

      return notes;
    } catch (e) {
      debugPrint('提取笔记失败: $e');
      return [];
    }
  }

  /// 切换订阅状态
  Future<void> toggleSubscription() async {
    try {
      if (article.value.isEmpty || article.value['feedId'] == null) return;

      final String feedId = article.value['feedId'].toString();
      final int feedType = article.value['type'] ?? 1;
      // 先更新UI状态
      isSubscribed.value = !isSubscribed.value;
      // 发送API请求
      final response = isSubscribed.value
          ? await _articleRepository.subscribeRss({
              "searchValue": feedId,
              "type": feedType,
            })
          : await _articleRepository.deleteRss([int.parse(feedId)]);

      if (response.isSuccess) {
        // 更新文章状态
        article.value['isSub'] = isSubscribed.value ? 1 : 0;
        // 同步更新首页的源状态
        try {
          final homeController = Get.find<HomeController>();
          // 尝试查找并更新该源在主页推荐源中的订阅状态
          final index = homeController.articles
              .indexWhere((item) => item['feedsId'].toString() == feedId);
          debugPrint('index: $index');
          if (index != -1) {
            homeController.articles[index]['isSub'] =
                isSubscribed.value ? 1 : 0;
            homeController.filteredArticles.refresh();
          }
        } catch (e) {
          debugPrint('更新主页推荐源状态失败: $e');
        }

        // 显示成功通知
        Get.closeAllSnackbars();
        Get.snackbar(
          'success'.tr,
          isSubscribed.value
              ? 'subscribe_success'.tr
              : 'unsubscribe_success'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        // 如果请求失败，回退状态
        isSubscribed.value = !isSubscribed.value;
        Get.closeAllSnackbars();
        Get.snackbar(
          'error'.tr,
          response.msg,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // 发生错误，回退状态
      isSubscribed.value = !isSubscribed.value;
      debugPrint('切换订阅状态失败: $e');
      Get.closeAllSnackbars();
      Get.snackbar(
        'error'.tr,
        isSubscribed.value ? 'subscribe_failed'.tr : 'unsubscribe_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }
}
