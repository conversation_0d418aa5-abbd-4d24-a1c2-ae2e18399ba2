import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/data/repositories/article_repository.dart';
import 'package:subfeeds/app/presentation/screens/article/article_controller.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/article_list_view.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/error_state_view.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/feed_action_buttons.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/feed_header.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/share_bottom_sheet.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/recommend_feeds.dart';

class UnfollowFeedsScreen extends StatefulWidget {
  final Map<String, dynamic> feed;

  const UnfollowFeedsScreen({super.key, required this.feed});

  @override
  State<UnfollowFeedsScreen> createState() => _UnfollowFeedsScreenState();
}

class _UnfollowFeedsScreenState extends State<UnfollowFeedsScreen> {
  final ArticleRepository _articleRepository = Get.find<ArticleRepository>();
  final ScrollController _scrollController = ScrollController();
  List<Map<String, dynamic>> _articles = [];
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isFollowing = false;
  int _currentPage = 1;
  final int _pageSize = 10;
  bool _isRefreshing = false;
  int total = 0;
  StreamSubscription? _articleStatusSubscription;

  // 当前显示的feed信息
  Map<String, dynamic> _currentFeed = {};

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    // 初始化当前feed信息
    _currentFeed = Map<String, dynamic>.from(widget.feed);
    setState(() {
      _isFollowing = _currentFeed['isSub'] == 1 ? true : false;
    });
    Future.microtask(() {
      _loadArticles();
    });

    _articleStatusSubscription =
        ArticleController.articleStatusChanged.stream.listen((statusData) {
      _updateArticleStatus(statusData);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _articleStatusSubscription?.cancel();
    super.dispose();
  }

  void _onScroll() {
    // 移除滚动加载更多功能
  }

  // 处理推荐feeds选择
  void _onRecommendFeedSelected(Map<String, dynamic> selectedFeed) {
    setState(() {
      _currentFeed = Map<String, dynamic>.from(selectedFeed);
      _isFollowing = _currentFeed['isSub'] == 1 ? true : false;
      // 重置文章列表状态
      _articles.clear();
      _isLoading = true;
      _hasError = false;
      _currentPage = 1;
      total = 0;
    });

    // 重新加载文章
    _loadArticles();
  }

  // 订阅源
  Future<bool> _subscribeFeed() async {
    try {
      final String feedId = _currentFeed['id']?.toString() ?? '';
      if (feedId.isEmpty) {
        _showErrorSnackbar('invalid_feed_id'.tr);
        return false;
      }

      final params = {
        "searchValue": feedId,
        "type": _currentFeed['type'] ?? 1,
      };
      setState(() {
        _isFollowing = true;
      });

      final response = await _articleRepository.subscribeRss(params);

      if (response.isSuccess) {
        return true;
      } else {
        setState(() {
          _isFollowing = false;
        });
        _showErrorSnackbar(response.msg);
        return false;
      }
    } catch (e) {
      _showErrorSnackbar(e.toString());
      return false;
    }
  }

  // 取消订阅源
  Future<bool> _unsubscribeFeed() async {
    try {
      final String feedId = _currentFeed['id']?.toString() ?? '';
      if (feedId.isEmpty) {
        _showErrorSnackbar('invalid_feed_id'.tr);
        return false;
      }
      setState(() {
        _isFollowing = false;
      });

      final response = await _articleRepository.deleteRss([int.parse(feedId)]);

      if (response.isSuccess) {
        return true;
      } else {
        _showErrorSnackbar(response.msg);
        setState(() {
          _isFollowing = true;
        });
        return false;
      }
    } catch (e) {
      _showErrorSnackbar(e.toString());
      return false;
    }
  }

  void _showErrorSnackbar(String message) {
    Get.closeAllSnackbars();
    Get.snackbar(
      'error'.tr,
      message,
      duration: const Duration(seconds: 2),
      icon: const Icon(Icons.error, color: Colors.red),
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF161617)
          : const Color(0xFF161617),
      colorText: Colors.white,
    );
  }

  // 更新文章状态
  void _updateArticleStatus(Map<String, dynamic> statusData) {
    if (!mounted) return;

    final String articleId = statusData['id']?.toString() ?? '';
    if (articleId.isEmpty) return;

    setState(() {
      for (int i = 0; i < _articles.length; i++) {
        if (_articles[i]['id'].toString() == articleId) {
          if (statusData.containsKey('isCollect')) {
            _articles[i]['isCollect'] = statusData['isCollect'];
          }
          if (statusData.containsKey('isLaterRead')) {
            _articles[i]['isLaterRead'] = statusData['isLaterRead'];
          }
          break;
        }
      }
    });
  }

  Future<void> _loadArticles() async {
    if (_isLoading && _currentPage != 1) return;

    debugPrint('getRssArticle');
    try {
      setState(() {
        _isLoading = true;
        if (_currentPage == 1) {
          _hasError = false;
        }
      });

      final feedId = int.tryParse(_currentFeed['id'].toString()) ?? -1;
      debugPrint('使用feedId: $feedId 获取文章列表');

      if (feedId == -1) {
        throw Exception('无效的订阅源ID');
      }

      final response = await _articleRepository.getAllArticle(
        feedId,
        _pageSize,
        1,
      );

      debugPrint('API响应: ${response.toJson()}');

      if (response.isSuccess && response.data != null) {
        final responseData = response.data;
        final data = responseData?['data'];
        final pageList = data?['pageList'] as List? ?? [];
        total = data?['total'] as int? ?? 0;

        if (mounted) {
          setState(() {
            _articles.clear();
            _articles.addAll(pageList.cast<Map<String, dynamic>>());
            _currentPage = 1;
            _isLoading = false;
            _hasError = false;
          });
        }
      } else {
        throw Exception(response.msg);
      }
    } catch (e) {
      debugPrint('加载文章失败: $e');

      if (mounted) {
        setState(() {
          final articleList = _currentFeed['articleList'] as List?;
          if (articleList != null && articleList.isNotEmpty) {
            _articles.clear();
            _articles.addAll(
                articleList.take(_pageSize).cast<Map<String, dynamic>>());
            total = articleList.length;
            _isLoading = false;
            _hasError = false;
            debugPrint('使用feed中的articleList作为备选内容: ${_articles.length}条');
          } else {
            if (_currentPage == 1) {
              _hasError = true;
              _errorMessage = '加载失败: $e';
              _articles.clear();
            }
            _isLoading = false;
          }
        });
      }
    }
  }

  Future<void> _refreshArticles() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
      _currentPage = 1;
    });

    await _loadArticles();

    setState(() {
      _isRefreshing = false;
    });
  }

  // 设置已读
  Future<void> _setRead(int index) async {
    if (index < 0 || index >= _articles.length) return;

    try {
      final article = _articles[index];
      final int articleId = int.parse(article['id'].toString());

      final response = await _articleRepository.insertHistory(
        articleId,
        int.tryParse(article['feedsId'].toString()) ?? -1,
        article['suffixTable'] ?? '',
      );

      if (response.isSuccess) {
        setState(() {
          article['isRead'] = 1;
          _articles[index] = article;
        });
      } else {
        debugPrint('设置已读失败: ${response.msg}');
      }
    } catch (e) {
      debugPrint('设置已读失败: $e');
    }
  }

  void _showShareMenu() {
    ShareBottomSheet.show(context, _currentFeed);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDarkMode ? const Color(0xFF1E1F1F) : const Color(0xFFeef2f9);

    return Scaffold(
      backgroundColor: backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // 顶部导航栏
            Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: 16.spx, vertical: 12.spx),
              child: Row(
                children: [
                  IconButton(
                    icon: SvgPicture.asset(
                      'assets/feeds/back.svg',
                      width: 16,
                      height: 16,
                      colorFilter: const ColorFilter.mode(
                        Color(0xFF7F8EA7),
                        BlendMode.srcIn,
                      ),
                    ),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
            ),

            // Feed头部信息
            FeedHeader(feed: _currentFeed, total: total),
            SizedBox(height: 12.spx),

            // 操作按钮
            FeedActionButtons(
              isFollowing: _isFollowing,
              onFollowToggle: () async {
                if (_isFollowing) {
                  await _unsubscribeFeed();
                } else {
                  await _subscribeFeed();
                }
              },
              onShare: _showShareMenu,
            ),
            SizedBox(height: 12.spx),

            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0.0, -1.0), // 从上方滑入
                    end: Offset.zero,
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeInOut,
                  )),
                  child: FadeTransition(
                    opacity: animation,
                    child: child,
                  ),
                );
              },
              child: _isFollowing
                  ? Padding(
                      key: const ValueKey('recommend_feeds'),
                      padding: EdgeInsets.symmetric(horizontal: 16.spx),
                      child: RecommendFeeds(
                        onFeedSelected: _onRecommendFeedSelected,
                      ),
                    )
                  : const SizedBox.shrink(
                      key: ValueKey('empty'),
                    ),
            ),
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: _isFollowing ? 12.spx : 0,
            ),

            // 搜索结果标题
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.spx),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      '$total ${'search_results'.tr}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontSize: 14.spx, fontWeight: FontWeight.w700),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10.spx),

            // 文章列表
            Expanded(
              child: _hasError
                  ? ErrorStateView(
                      errorMessage: _errorMessage,
                      onRetry: _loadArticles,
                    )
                  : ArticleListView(
                      articles: _articles,
                      feed: _currentFeed,
                      total: total,
                      isLoading: _isLoading,
                      scrollController: _scrollController,
                      onRefresh: _refreshArticles,
                      onSetRead: _setRead,
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
