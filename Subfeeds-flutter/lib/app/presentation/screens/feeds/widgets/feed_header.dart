import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

class FeedHeader extends StatelessWidget {
  final Map<String, dynamic> feed;
  final int total;

  const FeedHeader({
    super.key,
    required this.feed,
    required this.total,
  });

  String formatTime(dynamic time) {
    try {
      if (time == null) return '';

      DateTime dateTime;
      if (time is String) {
        // 处理pubDate格式
        dateTime = DateTime.parse(time);
      } else if (time is int) {
        // 处理createTime时间戳
        dateTime = DateTime.fromMillisecondsSinceEpoch(time * 1000);
      } else {
        return '';
      }

      final now = DateTime.now();
      final difference = now.difference(dateTime);
      // 3小时以内 返回'刚刚'
      if (difference.inHours < 3) {
        return 'just_now'.tr;
      }
      // 1天以内，返回小时
      else if (difference.inDays < 1) {
        final hours = difference.inHours;
        return '${hours}H';
      }
      // 30天以内，返回天数
      else if (difference.inDays < 30) {
        return '${difference.inDays}D';
      }
      // 超过30天，返回完整日期
      else {
        return DateFormat('yyyy-MM-dd').format(dateTime);
      }
    } catch (e) {
      debugPrint('时间格式化失败: $e');
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.spx),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 第一行：Logo和基本信息
          Row(
            children: [
              // Logo
              Container(
                width: 50.spx,
                height: 50.spx,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(30),
                  child: Image.network(
                    feed['img'] ??
                        'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['link']}&size=64',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      debugPrint('图片加载失败: $error');
                      return Container(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                        child: const Icon(Icons.rss_feed,
                            size: 30, color: Colors.white),
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                        child: const Center(
                          child: CircularProgressIndicator(
                            color: Colors.white,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              const Spacer(),
              // 右侧：创建时间和文章数量（两列布局）
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if (feed['lastVerifyTime'] != null)
                    // 第一列：创建时间
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          formatTime(feed['lastVerifyTime']),
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontSize: 14.spx, fontWeight: FontWeight.w500),
                        ),
                        SizedBox(height: 4.spx),
                        Text(
                          'update_time'.tr,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                fontSize: 12.spx,
                                color: isDarkMode
                                    ? const Color(0xFFD1D1D1)
                                    : const Color(0xFF999999),
                              ),
                        ),
                      ],
                    ),
                  const SizedBox(width: 30),
                  // 第二列：文章数量
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        total.toString(),
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontSize: 14.spx, fontWeight: FontWeight.w500),
                      ),
                      SizedBox(height: 4.spx),
                      Text(
                        'articles'.tr,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontSize: 12.spx,
                              color: isDarkMode
                                  ? const Color(0xFFD1D1D1)
                                  : const Color(0xFF999999),
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 12.spx),
          // 第二行：Feeds标题
          Text(
            feed['name'] ?? '',
            style: TextStyle(
              fontSize: 14.spx,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
