import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/article_item_unfeeds.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/article_list_skeleton.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

class ArticleListView extends StatelessWidget {
  final List<Map<String, dynamic>> articles;
  final Map<String, dynamic> feed;
  final int total;
  final bool isLoading;
  final ScrollController scrollController;
  final VoidCallback onRefresh;
  final Function(int) onSetRead;

  const ArticleListView({
    super.key,
    required this.articles,
    required this.feed,
    required this.total,
    required this.isLoading,
    required this.scrollController,
    required this.onRefresh,
    required this.onSetRead,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading && articles.isEmpty) {
      return const ArticleListSkeleton();
    }

    if (articles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/article_empty.png',
              width: 200,
              height: 200,
            ),
            const SizedBox(height: 16),
            Text(
              'no_articles_found'.tr,
              style: Theme.of(context)
                  .textTheme
                  .titleMedium
                  ?.copyWith(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async => onRefresh(),
            child: ListView.builder(
              controller: scrollController,
              itemCount: articles.length,
              padding: EdgeInsets.symmetric(horizontal: 10.spx),
              itemBuilder: (context, index) {
                final article = articles[index];
                return ArticleItem(
                  article: article,
                  feed: feed,
                  isShowBookmark: false,
                  isShowLaterRead: false,
                  isDarkMode: Theme.of(context).brightness == Brightness.dark,
                  onTap: () {
                    onSetRead(index);
                    Get.toNamed(
                      Routes.ARTICLE,
                      arguments: {
                        'article': {...article, 'img': feed['img']},
                      },
                    );
                  },
                  onReadLater: () => {},
                  onBookmark: () => {},
                );
              },
            ),
          ),
        ),
        // 添加"订阅以查看更多"提示
        if (total > articles.length)
          Container(
            padding: EdgeInsets.symmetric(vertical: 16.spx),
            width: double.infinity,
            child: Text(
              'please_subscribe_to_view_more'.tr,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
      ],
    );
  }
}
