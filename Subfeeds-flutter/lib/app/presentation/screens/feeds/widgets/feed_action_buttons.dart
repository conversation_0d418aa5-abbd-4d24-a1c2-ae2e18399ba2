import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

class FeedActionButtons extends StatelessWidget {
  final bool isFollowing;
  final VoidCallback onFollowToggle;
  final VoidCallback onShare;

  const FeedActionButtons({
    super.key,
    required this.isFollowing,
    required this.onFollowToggle,
    required this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.spx),
      child: Row(
        children: [
          // Follow按钮 - 使用Expanded和flex实现响应式布局
          Expanded(
            flex: isFollowing ? 65 : 100,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: ElevatedButton(
                onPressed: onFollowToggle,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isFollowing
                      ? isDarkMode
                          ? const Color(0xFF767676)
                          : const Color(0xFF9BAAFF)
                      : isDarkMode
                          ? Colors.white
                          : Theme.of(context).colorScheme.primary,
                  foregroundColor: isDarkMode ? Colors.white : Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6.spx),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 9.spx),
                ),
                child: Text(
                  isFollowing ? 'unfollow'.tr : 'follow'.tr,
                  style: TextStyle(
                    fontSize: 14.spx,
                    fontWeight: FontWeight.w600,
                    color: isFollowing
                        ? isDarkMode
                            ? Colors.white
                            : Colors.white
                        : isDarkMode
                            ? const Color(0xFF333333)
                            : Colors.white,
                  ),
                ),
              ),
            ),
          ),
          // Share按钮 - 使用AnimatedSize实现平滑的显示/隐藏动画
          AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: isFollowing
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(width: 10.spx),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.35 - 30.spx,
                        child: ElevatedButton(
                          onPressed: onShare,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isDarkMode
                                ? const Color(0xFF909090)
                                : const Color(0xFFBCC2CC),
                            foregroundColor:
                                isDarkMode ? Colors.white : Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6.spx),
                            ),
                            padding: EdgeInsets.symmetric(vertical: 9.spx),
                          ),
                          child: Text(
                            'share'.tr,
                            style: TextStyle(
                              fontSize: 14.spx,
                              fontWeight: FontWeight.w600,
                              color: isDarkMode
                                  ? Colors.white
                                  : const Color(0xFF333333),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
