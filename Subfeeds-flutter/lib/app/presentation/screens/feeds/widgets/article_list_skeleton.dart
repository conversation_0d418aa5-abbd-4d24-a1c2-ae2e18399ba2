import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/article_item_unfeeds.dart';

/// 文章列表骨架屏组件
class ArticleListSkeleton extends StatelessWidget {
  final int itemCount;

  const ArticleListSkeleton({
    super.key,
    this.itemCount = 8,
  });

  /// 获取骨架屏颜色
  static Color? getSkeletonColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color.fromRGBO(97, 97, 97, 1)
        : const Color.fromRGBO(224, 224, 224, 1);
  }

  /// 生成骨架屏数据
  List<Map<String, dynamic>> generateSkeletonItems(int count) {
    return List.generate(
      count,
      (index) => {
        'id': 'skeleton_$index',
        'title':
            'This is a skeleton article title that will be replaced with real content when data loads',
        'description':
            'This is a skeleton description that will be replaced with real content when the data loads from the server. It contains enough text to show the layout properly.',
        'pubDate': DateTime.now().toIso8601String(),
        'createTime': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'creator': 'Skeleton Author',
        'link': 'https://example.com/article_$index',
        'img': 'https://via.placeholder.com/50x50/CCCCCC/969696?text=IMG',
        'isCollect': 0,
        'isLaterRead': 0,
        'isRead': 0,
        'feedsId': 'skeleton_feed_$index',
        'suffixTable': '',
        'isSkeleton': true, // 标记为骨架屏数据
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final skeletonData = generateSkeletonItems(itemCount);
    final baseColor = getSkeletonColor(context);

    return Skeletonizer(
      enabled: true,
      effect: ShimmerEffect(
        baseColor: baseColor ?? Colors.grey[300]!,
        highlightColor: const Color.fromRGBO(245, 245, 245, 1),
        duration: const Duration(seconds: 1),
      ),
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 10.spx),
        itemCount: skeletonData.length,
        itemBuilder: (context, index) {
          final article = skeletonData[index];
          return ArticleItem(
            article: article,
            feed: {
              'name': 'Skeleton Feed Name',
              'img': 'https://via.placeholder.com/20x20/CCCCCC/969696?text=F',
            },
            isDarkMode: Theme.of(context).brightness == Brightness.dark,
            isShowBookmark: false,
            isShowLaterRead: false,
            onTap: () {},
            onBookmark: () {},
            onReadLater: () {},
          );
        },
      ),
    );
  }
}
