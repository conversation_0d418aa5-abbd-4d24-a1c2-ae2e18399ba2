import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:url_launcher/url_launcher.dart';

class ShareBottomSheet extends StatelessWidget {
  final Map<String, dynamic> feed;

  const ShareBottomSheet({
    super.key,
    required this.feed,
  });

  // 构建分享项
  Widget _buildShareItem(
    BuildContext context,
    String iconPath,
    String title,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          SvgPicture.asset(
            iconPath,
            width: 32,
            height: 32,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontSize: 12,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 启动URL
  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        Get.snackbar(
          'error'.tr,
          'cannot_open_url'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: const Color(0xFF161617),
          colorText: Colors.white,
        );
      }
    } catch (e) {
      debugPrint('启动URL失败: $e');
      Get.snackbar(
        'error'.tr,
        'share_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: const Color(0xFF161617),
        colorText: Colors.white,
      );
    }
  }

  // 分享到各个平台的方法
  void _shareToFacebook(BuildContext context) {
    Get.back(); // 关闭分享菜单
    final url = feed['link'];
    final title = feed['name'];
    if (url == null || url.isEmpty) return;

    final shareUrl = Uri.encodeFull(
        'https://www.facebook.com/sharer/sharer.php?u=$url&quote=$title');
    _launchUrl(shareUrl);
  }

  void _shareToTwitter(BuildContext context) {
    Get.back(); // 关闭分享菜单
    final url = feed['link'];
    final title = feed['name'];
    if (url == null || url.isEmpty) return;

    final shareUrl =
        Uri.encodeFull('https://twitter.com/intent/tweet?text=$title&url=$url');
    _launchUrl(shareUrl);
  }

  void _shareToInstagram(BuildContext context) {
    Get.back(); // 关闭分享菜单
    final url = feed['link'];
    final title = feed['name'];
    if (url == null || url.isEmpty) return;

    final shareUrl = Uri.encodeFull(
        'https://www.instagram.com/share?url=$url&caption=$title');
    _launchUrl(shareUrl);
  }

  void _shareToLinkedIn(BuildContext context) {
    Get.back(); // 关闭分享菜单
    final url = feed['link'];
    if (url == null || url.isEmpty) return;

    final shareUrl = Uri.encodeFull(
        'https://www.linkedin.com/sharing/share-offsite/?url=$url');
    _launchUrl(shareUrl);
  }

  void _shareToPinterest(BuildContext context) {
    Get.back(); // 关闭分享菜单
    final url = feed['link'];
    final title = feed['name'];
    if (url == null || url.isEmpty) return;

    final shareUrl = Uri.encodeFull(
        'https://pinterest.com/pin/create/button/?url=$url&description=$title');
    _launchUrl(shareUrl);
  }

  void _shareToReddit(BuildContext context) {
    Get.back(); // 关闭分享菜单
    final url = feed['link'];
    final title = feed['name'];
    if (url == null || url.isEmpty) return;

    final shareUrl =
        Uri.encodeFull('https://www.reddit.com/submit?url=$url&title=$title');
    _launchUrl(shareUrl);
  }

  void _shareToTelegram(BuildContext context) {
    Get.back(); // 关闭分享菜单
    final url = feed['link'];
    final title = feed['name'];
    if (url == null || url.isEmpty) return;

    final shareUrl =
        Uri.encodeFull('https://t.me/share/url?url=$url&text=$title');
    _launchUrl(shareUrl);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFFF7FAFF)
            : const Color(0xFF444444),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.spx, vertical: 12.spx),
            child: Text(
              'share_to'.tr,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black,
                  ),
            ),
          ),
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 4,
            padding: EdgeInsets.symmetric(horizontal: 16.spx, vertical: 12.spx),
            mainAxisSpacing: 16.spx,
            crossAxisSpacing: 16.spx,
            children: [
              _buildShareItem(
                context,
                'assets/share/facebook.svg',
                'platform_facebook'.tr,
                () => _shareToFacebook(context),
              ),
              _buildShareItem(
                context,
                'assets/share/twitter.svg',
                'platform_twitter'.tr,
                () => _shareToTwitter(context),
              ),
              _buildShareItem(
                context,
                'assets/share/instagram.svg',
                'platform_instagram'.tr,
                () => _shareToInstagram(context),
              ),
              _buildShareItem(
                context,
                'assets/share/linkedin.svg',
                'platform_linkedin'.tr,
                () => _shareToLinkedIn(context),
              ),
              _buildShareItem(
                context,
                'assets/share/pinterest.svg',
                'platform_pinterest'.tr,
                () => _shareToPinterest(context),
              ),
              _buildShareItem(
                context,
                'assets/share/reddit.svg',
                'platform_reddit'.tr,
                () => _shareToReddit(context),
              ),
              _buildShareItem(
                context,
                'assets/share/telegram.svg',
                'platform_telegram'.tr,
                () => _shareToTelegram(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  static void show(BuildContext context, Map<String, dynamic> feed) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF292c42)
          : Colors.white,
      builder: (context) => ShareBottomSheet(feed: feed),
    );
  }
}
