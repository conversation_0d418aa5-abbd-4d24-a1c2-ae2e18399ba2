import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/data/repositories/feeds_repository.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';

class RecommendFeeds extends StatefulWidget {
  final Function(Map<String, dynamic>) onFeedSelected;

  const RecommendFeeds({
    super.key,
    required this.onFeedSelected,
  });

  @override
  State<RecommendFeeds> createState() => _RecommendFeedsState();
}

class _RecommendFeedsState extends State<RecommendFeeds> {
  final ArticleRepository _feedsRepository = ArticleRepository();
  List<Map<String, dynamic>> _recommendFeeds = [];
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadRecommendFeeds();
  }

  Future<void> _loadRecommendFeeds() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      final response = await _feedsRepository.getRecommendFeeds();

      if (response.isSuccess && response.data != null) {
        final data = response.data;
        final pageList = data?['pageList'] as List? ?? [];

        setState(() {
          _recommendFeeds = pageList.cast<Map<String, dynamic>>();
          _isLoading = false;
        });
      } else {
        setState(() {
          _hasError = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('加载推荐feeds失败: $e');
      setState(() {
        _hasError = true;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none, // 允许子组件超出Stack边界
      children: [
        Container(
          height: 110.spx,
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF565656)
                : const Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(8.spx),
          ),
          padding: EdgeInsets.only(
              left: 9.spx, right: 9.spx, top: 8.spx, bottom: 0.spx),
          child: Column(
            children: [
              Row(
                children: [
                  Text(
                    textAlign: TextAlign.left,
                    'other_subscribers_also_liked'.tr,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 12.spx,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ],
              ),
              Expanded(
                child: _buildRecommendFeedsCard(context),
              ),
            ],
          ),
        ),
        // 三角形指示器
        Positioned(
          left: 70.spx,
          top: -6.spx, // 向上偏移6px，让三角形超出容器顶部
          child: CustomPaint(
            painter: TrianglePainter(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF565656)
                  : const Color(0xFFFFFFFF),
            ),
            size: Size(12.spx, 8.spx),
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendFeedsCard(BuildContext context) {
    if (_isLoading) {
      return LoadingIndicator(
        size: 80.spx,
      );
    }

    if (_hasError || _recommendFeeds.isEmpty) {
      return Center(
        child: Text(
          'no_recommend_feeds'.tr,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey,
                fontSize: 11.spx,
              ),
        ),
      );
    }

    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: _recommendFeeds.length,
      itemBuilder: (context, index) {
        final feed = _recommendFeeds[index];
        return _buildFeedItem(context, feed);
      },
    );
  }

  Widget _buildFeedItem(BuildContext context, Map<String, dynamic> feed) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => widget.onFeedSelected(feed),
      child: Container(
        width: 60.spx,
        margin: EdgeInsets.only(right: 12.spx),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Feed Logo
            Container(
              width: 40.spx,
              height: 40.spx,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isDarkMode
                      ? const Color(0xFF444444)
                      : const Color(0xFFE0E0E0),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20.spx),
                child: CachedNetworkImage(
                  imageUrl: feed['img'] ??
                      'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${feed['link']}&size=64',
                  fit: BoxFit.cover,
                  errorWidget: (context, error, stackTrace) {
                    return Container(
                      color: isDarkMode
                          ? const Color(0xFF333333)
                          : const Color(0xFFF5F5F5),
                      child: SvgPicture.asset(
                        'assets/feeds/feeds_logo.svg',
                        width: 20.spx,
                        height: 20.spx,
                        color: isDarkMode
                            ? const Color(0xFF666666)
                            : const Color(0xFF999999),
                      ),
                    );
                  },
                  placeholder: (context, url) {
                    return Container(
                      color: isDarkMode
                          ? const Color(0xFF333333)
                          : const Color(0xFFF5F5F5),
                      child: Center(
                        child: LoadingIndicator(size: 50.spx),
                      ),
                    );
                  },
                ),
              ),
            ),
            SizedBox(height: 6.spx),
            // Feed Name
            Text(
              feed['name'] ?? 'Unknown Feed',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontSize: 9.spx,
                    fontWeight: FontWeight.w500,
                  ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// 自定义三角形画笔，用于绘制带圆角头部的三角形
class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();

    // 三角形的宽度和高度
    final width = size.width;
    final height = size.height;

    // 圆角半径
    final radius = 2.0;

    // 三角形中心点
    final centerX = width / 2;

    // 开始绘制路径
    // 从左下角开始
    path.moveTo(0, height);

    // 画到右下角
    path.lineTo(width, height);

    // 画到右侧顶部圆角开始点
    path.lineTo(centerX + radius, radius);

    // 添加顶部圆角 - 从右到左的弧线
    path.arcToPoint(
      Offset(centerX - radius, radius),
      radius: Radius.circular(radius),
      clockwise: false,
    );

    // 回到左下角，完成三角形
    path.lineTo(0, height);

    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return oldDelegate is! TrianglePainter || oldDelegate.color != color;
  }
}
