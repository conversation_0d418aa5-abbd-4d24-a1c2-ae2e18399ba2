import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final UserController _userController = Get.find<UserController>();
  final _formKey = GlobalKey<FormState>();

  final _oldPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _obscureOldPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  // 密码强度
  String _passwordStrength = '';
  Color _passwordStrengthColor = Colors.transparent;

  // 实时验证错误状态
  String? _oldPasswordError;
  String? _newPasswordError;
  String? _confirmPasswordError;

  // 字段是否已经被用户交互过（用于控制错误显示时机）
  bool _oldPasswordTouched = false;
  bool _newPasswordTouched = false;
  bool _confirmPasswordTouched = false;

  @override
  void dispose() {
    _oldPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // 创建统一的错误显示组件
  Widget _buildErrorText(String errorText) {
    return Container(
      margin: EdgeInsets.only(top: 4.spx),
      padding: EdgeInsets.symmetric(horizontal: 8.spx, vertical: 4.spx),
      child: Row(
        children: [
          Expanded(
            child: Text(
              errorText,
              style: TextStyle(
                color: const Color(0xFFD64E36),
                fontSize: 12.spx,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final user = _userController.user.value;
      final hasPassword = user?.password != null;

      return Scaffold(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF1e2020)
              : const Color(0xFFeef2f9),
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(50),
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 16.spx,
                ),
                child: Stack(
                  children: [
                    // 标题居中
                    Align(
                      alignment: Alignment.center,
                      child: Text(
                        hasPassword
                            ? 'change_password'.tr
                            : 'personal_info_set_password'.tr,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontSize: 16.spx,
                              fontWeight: FontWeight.w600,
                            ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    // 返回按钮
                    Align(
                      alignment: Alignment.centerLeft,
                      child: InkWell(
                        onTap: () => Get.back(),
                        borderRadius: BorderRadius.circular(16),
                        child: SvgPicture.asset(
                          'assets/feeds/back.svg',
                          width: 20.spx,
                          height: 20.spx,
                          colorFilter: ColorFilter.mode(
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : const Color(0xff333333),
                            BlendMode.srcIn,
                          ),
                          fit: BoxFit.none,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.only(
              left: 16.spx,
              right: 16.spx,
              top: 0.spx,
              bottom: 20.spx,
            ),
            child: Card(
              elevation: 0,
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF3b3b3b)
                  : const Color(0xFFFFFFFF),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.spx),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.spx),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // 头像和用户名一行显示
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 6.spx, vertical: 10.spx),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'change_password'.tr,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontSize: 14.spx,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 6.spx),
                        child: Divider(
                          height: 0.5,
                          thickness: 0.5,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFF747474)
                              : const Color(0xFFD5D5D5),
                        ),
                      ),
                      SizedBox(height: 10.spx),
                      // 旧密码 - 只在用户已有密码时显示
                      if (hasPassword) ...[
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'personal_info_old_password'.tr,
                              style: TextStyle(
                                fontSize: 12.spx,
                                fontWeight: FontWeight.w500,
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? const Color(0xFFD1D1D1)
                                    : const Color(0xFF999999),
                              ),
                            ),
                            SizedBox(height: 8.spx),
                            TextFormField(
                              controller: _oldPasswordController,
                              obscureText: _obscureOldPassword,
                              style: TextStyle(
                                fontSize: 13.spx,
                                fontWeight: FontWeight.w500,
                              ),
                              decoration: InputDecoration(
                                fillColor: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? const Color(0xFF444444)
                                    : const Color(0xFFf7faff),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.spx),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.spx),
                                  borderSide: BorderSide(
                                    color: _oldPasswordTouched &&
                                            _oldPasswordError != null
                                        ? const Color(0xFFD64E36)
                                        : Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? const Color(0xFF747474)
                                            : const Color(0xFFD5D5D5),
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.spx),
                                  borderSide: BorderSide(
                                    color: _oldPasswordTouched &&
                                            _oldPasswordError != null
                                        ? const Color(0xFFD64E36)
                                        : Theme.of(context).primaryColor,
                                  ),
                                ),
                                hintText: 'please_input_old_password'.tr,
                                errorBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.spx),
                                  borderSide: const BorderSide(
                                    color: Color(0xFFD64E36),
                                  ),
                                ),
                                focusedErrorBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.spx),
                                  borderSide: const BorderSide(
                                    color: Color(0xFFD64E36),
                                  ),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8.spx,
                                  vertical: 10.spx,
                                ),
                                suffixIcon: IconButton(
                                  icon: SvgPicture.asset(
                                    _obscureOldPassword
                                        ? 'assets/icons/pwd_off.svg'
                                        : 'assets/icons/pwd_on.svg',
                                    colorFilter: ColorFilter.mode(
                                      Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? const Color(0xFFFFFFFF)
                                          : const Color(0xFF333333),
                                      BlendMode.srcIn,
                                    ),
                                    width: 16.spx,
                                    height: 16.spx,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscureOldPassword =
                                          !_obscureOldPassword;
                                    });
                                  },
                                ),
                              ),
                              onChanged: _validateOldPassword,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'please_input_old_password'.tr;
                                }
                                return null;
                              },
                            ),
                            // 实时错误显示
                            if (_oldPasswordTouched &&
                                _oldPasswordError != null)
                              _buildErrorText(_oldPasswordError!),
                          ],
                        ),
                        SizedBox(height: 10.spx),
                      ],

                      // 新密码
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'personal_info_new_password'.tr,
                                style: TextStyle(
                                  fontSize: 12.spx,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? const Color(0xFFD1D1D1)
                                      : const Color(0xFF999999),
                                ),
                              ),
                              if (_passwordStrength.isNotEmpty)
                                Text(
                                  _passwordStrength,
                                  style: TextStyle(
                                    fontSize: 12.spx,
                                    fontWeight: FontWeight.w600,
                                    color: _passwordStrengthColor,
                                  ),
                                ),
                            ],
                          ),
                          SizedBox(height: 8.spx),
                          TextFormField(
                            controller: _newPasswordController,
                            obscureText: _obscureNewPassword,
                            style: TextStyle(
                              fontSize: 13.spx,
                              fontWeight: FontWeight.w500,
                            ),
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.spx),
                              ),
                              fillColor: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFF444444)
                                  : const Color(0xFFf7faff),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.spx),
                                borderSide: BorderSide(
                                  color: _newPasswordTouched &&
                                          _newPasswordError != null
                                      ? const Color(0xFFD64E36)
                                      : Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? const Color(0xFF747474)
                                          : const Color(0xFFD5D5D5),
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.spx),
                                borderSide: BorderSide(
                                  color: _newPasswordTouched &&
                                          _newPasswordError != null
                                      ? const Color(0xFFD64E36)
                                      : Theme.of(context).primaryColor,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.spx),
                                borderSide: const BorderSide(
                                  color: Color(0xFFD64E36),
                                ),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.spx),
                                borderSide: const BorderSide(
                                  color: Color(0xFFD64E36),
                                ),
                              ),
                              hintText: 'please_input_new_password'.tr,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 8.spx,
                                vertical: 10.spx,
                              ),
                              suffixIcon: IconButton(
                                icon: SvgPicture.asset(
                                  _obscureNewPassword
                                      ? 'assets/icons/pwd_off.svg'
                                      : 'assets/icons/pwd_on.svg',
                                  colorFilter: ColorFilter.mode(
                                    Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? const Color(0xFFFFFFFF)
                                        : const Color(0xFF333333),
                                    BlendMode.srcIn,
                                  ),
                                  width: 16.spx,
                                  height: 16.spx,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscureNewPassword = !_obscureNewPassword;
                                  });
                                },
                              ),
                            ),
                            onChanged: (value) {
                              _checkPasswordStrength(value);
                              _validateNewPassword(value);
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'personal_info_password_empty'.tr;
                              }
                              if (value.length < 8) {
                                return 'personal_info_password_rule'.tr;
                              }
                              final passwordRegExp = RegExp(
                                  r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\$&*~]).{8,}$');
                              if (!passwordRegExp.hasMatch(value)) {
                                return 'personal_info_password_rule'.tr;
                              }
                              return null;
                            },
                          ),
                          // 实时错误显示
                          if (_newPasswordTouched && _newPasswordError != null)
                            _buildErrorText(_newPasswordError!),
                        ],
                      ),

                      SizedBox(height: 10.spx),

                      // 确认新密码
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'personal_info_confirm_password'.tr,
                            style: TextStyle(
                              fontSize: 12.spx,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFFD1D1D1)
                                  : const Color(0xFF999999),
                            ),
                          ),
                          SizedBox(height: 8.spx),
                          TextFormField(
                            controller: _confirmPasswordController,
                            obscureText: _obscureConfirmPassword,
                            style: TextStyle(
                              fontSize: 13.spx,
                              fontWeight: FontWeight.w500,
                            ),
                            decoration: InputDecoration(
                              fillColor: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFF444444)
                                  : const Color(0xFFf7faff),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.spx),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.spx),
                                borderSide: BorderSide(
                                  color: _confirmPasswordTouched &&
                                          _confirmPasswordError != null
                                      ? const Color(0xFFD64E36)
                                      : Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? const Color(0xFF747474)
                                          : const Color(0xFFD5D5D5),
                                ),
                              ),
                              hintText: 'please_input_confirm_password'.tr,
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.spx),
                                borderSide: BorderSide(
                                  color: _confirmPasswordTouched &&
                                          _confirmPasswordError != null
                                      ? const Color(0xFFD64E36)
                                      : Theme.of(context).primaryColor,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.spx),
                                borderSide: const BorderSide(
                                  color: Color(0xFFD64E36),
                                ),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.spx),
                                borderSide: const BorderSide(
                                  color: Color(0xFFD64E36),
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 8.spx,
                                vertical: 10.spx,
                              ),
                              suffixIcon: IconButton(
                                icon: SvgPicture.asset(
                                  _obscureConfirmPassword
                                      ? 'assets/icons/pwd_off.svg'
                                      : 'assets/icons/pwd_on.svg',
                                  colorFilter: ColorFilter.mode(
                                    Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? const Color(0xFFFFFFFF)
                                        : const Color(0xFF333333),
                                    BlendMode.srcIn,
                                  ),
                                  width: 16.spx,
                                  height: 16.spx,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscureConfirmPassword =
                                        !_obscureConfirmPassword;
                                  });
                                },
                              ),
                            ),
                            onChanged: _validateConfirmPassword,
                            validator: (value) {
                              if (value != _newPasswordController.text) {
                                return 'personal_info_password_not_match'.tr;
                              }
                              return null;
                            },
                          ),
                          // 实时错误显示
                          if (_confirmPasswordTouched &&
                              _confirmPasswordError != null)
                            _buildErrorText(_confirmPasswordError!),
                        ],
                      ),

                      SizedBox(height: 10.spx),

                      // 提交按钮
                      Obx(() => SizedBox(
                            width: MediaQuery.of(context).size.width,
                            child: ElevatedButton(
                              onPressed: _userController.isLoading.value
                                  ? null
                                  : _changePassword,
                              style: ElevatedButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 11.spx,
                                  vertical: 8.spx,
                                ),
                                backgroundColor: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? const Color(0xFF2B2B2B)
                                    : Theme.of(context).primaryColor,
                                foregroundColor: Colors.white,
                              ),
                              child: _userController.isLoading.value
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    )
                                  : Text(
                                      hasPassword
                                          ? 'save'.tr
                                          : 'personal_info_set_password'.tr,
                                      style: TextStyle(
                                        fontSize: 14.spx,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          )),

                      SizedBox(height: 16.spx),

                      // 错误信息
                      Obx(() => _userController.errorMessage.value.isNotEmpty
                          ? Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.red.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                _userController.errorMessage.value,
                                style: TextStyle(
                                  color: Color(0xFFD64E36),
                                  fontSize: 12.spx,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            )
                          : const SizedBox.shrink()),
                    ],
                  ),
                ),
              ),
            ),
          ));
    });
  }

  // 修改密码
  void _changePassword() async {
    if (_formKey.currentState!.validate()) {
      final user = _userController.user.value;
      final hasPassword = user?.password != null;

      final success = await _userController.changePassword(
        hasPassword ? _oldPasswordController.text : '', // 如果没有旧密码，传空字符串
        _newPasswordController.text,
      );

      if (success) {
        Get.back();
        Get.snackbar(
          'success'.tr,
          hasPassword
              ? 'personal_info_password_updated'.tr
              : 'personal_info_password_set'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    }
  }

  // 实时验证旧密码
  void _validateOldPassword(String value) {
    setState(() {
      _oldPasswordTouched = true;
      if (value.isEmpty) {
        _oldPasswordError = 'please_input_old_password'.tr;
      } else {
        _oldPasswordError = null;
      }
    });
  }

  // 实时验证新密码
  void _validateNewPassword(String value) {
    setState(() {
      _newPasswordTouched = true;
      if (value.isEmpty) {
        _newPasswordError = 'personal_info_password_empty'.tr;
      } else if (value.length < 8) {
        _newPasswordError = 'personal_info_password_rule'.tr;
      } else {
        final passwordRegExp = RegExp(
            r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\$&*~]).{8,}$');
        if (!passwordRegExp.hasMatch(value)) {
          _newPasswordError = 'personal_info_password_rule'.tr;
        } else {
          _newPasswordError = null;
        }
      }

      // 如果确认密码已经输入过，需要重新验证确认密码
      if (_confirmPasswordTouched) {
        _validateConfirmPassword(_confirmPasswordController.text);
      }
    });
  }

  // 实时验证确认密码
  void _validateConfirmPassword(String value) {
    setState(() {
      _confirmPasswordTouched = true;
      if (value != _newPasswordController.text) {
        _confirmPasswordError = 'personal_info_password_not_match'.tr;
      } else {
        _confirmPasswordError = null;
      }
    });
  }

  // 检测密码强度
  void _checkPasswordStrength(String password) {
    if (password.isEmpty) {
      setState(() {
        _passwordStrength = '';
        _passwordStrengthColor = Colors.transparent;
      });
      return;
    }

    // 检查各种条件
    bool hasUppercase = RegExp(r'[A-Z]').hasMatch(password);
    bool hasLowercase = RegExp(r'[a-z]').hasMatch(password);
    bool hasDigits = RegExp(r'[0-9]').hasMatch(password);
    bool hasSpecialCharacters = RegExp(r'[!@#\$&*~]').hasMatch(password);
    bool hasMinLength = password.length >= 8;

    int conditionsMet = 0;
    if (hasUppercase) conditionsMet++;
    if (hasLowercase) conditionsMet++;
    if (hasDigits) conditionsMet++;
    if (hasSpecialCharacters) conditionsMet++;

    setState(() {
      if (!hasMinLength) {
        // 长度不足，显示为弱
        _passwordStrength = 'Low';
        _passwordStrengthColor = const Color(0xFFFF6B6B); // 红色
      } else if (conditionsMet <= 2) {
        // 弱：仅满足1-2个条件
        _passwordStrength = 'Low';
        _passwordStrengthColor = const Color(0xFFFF6B6B); // 红色
      } else if (conditionsMet == 3) {
        // 中：满足3个条件
        _passwordStrength = 'Medium';
        _passwordStrengthColor = const Color(0xFFFFB347); // 橙色
      } else if (conditionsMet == 4 && hasMinLength) {
        // 强：满足全部4个条件且长度>=8
        _passwordStrength = 'Strong';
        _passwordStrengthColor = const Color(0xFF51CF66); // 绿色
      }
    });
  }
}
