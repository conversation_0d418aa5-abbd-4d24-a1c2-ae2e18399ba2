import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 设置页控制器
class SettingsController extends GetxController {
  // 是否暗黑模式
  final isDarkMode = false.obs;

  // 当前语言
  final currentLocale = 'zh_CN'.obs;

  // 字体大小选项
  final fontSizeOptions = ['小号', '中号', '大号'].obs;
  final selectedFontSize = '中号'.obs;

  // 字体家族选项
  final fontFamilyOptions =
      ['Roboto', 'SourceSerif4', 'Montserrat', 'Poppins'].obs;
  final selectedFontFamily = 'Roboto'.obs;

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
  }

  /// 加载用户设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // 加载主题设置
    isDarkMode.value = prefs.getBool('isDarkMode') ?? false;

    // 加载语言设置
    currentLocale.value = prefs.getString('locale') ?? 'zh_CN';

    // 加载字体大小设置
    final fontSize = prefs.getString('fontSize') ?? '中号';
    selectedFontSize.value = fontSize;

    // 加载字体家族设置
    final fontFamily = prefs.getString('fontFamily') ?? 'Roboto';
    selectedFontFamily.value = fontFamily;
  }

  /// 切换深色模式
  Future<void> toggleDarkMode() async {
    isDarkMode.value = !isDarkMode.value;

    // 保存设置
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', isDarkMode.value);

    // 更新应用主题
    Get.changeThemeMode(isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
  }

  /// 切换语言
  Future<void> changeLocale(String locale) async {
    if (currentLocale.value == locale) return;

    currentLocale.value = locale;

    // 保存设置
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('locale', locale);

    // 更新应用语言
    Locale newLocale;
    switch (locale) {
      case 'en_US':
        newLocale = const Locale('en', 'US');
        break;
      case 'zh_CN':
      default:
        newLocale = const Locale('zh', 'CN');
        break;
    }

    Get.updateLocale(newLocale);
  }

  /// 设置字体大小
  Future<void> setFontSize(String size) async {
    selectedFontSize.value = size;

    // 保存设置
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('fontSize', size);

    // 根据选择的字体大小设置实际大小
    double fontSize;
    switch (size) {
      case '小号':
        fontSize = 14.0;
        break;
      case '大号':
        fontSize = 18.0;
        break;
      case '中号':
      default:
        fontSize = 16.0;
        break;
    }

    await prefs.setDouble('fontSizeValue', fontSize);
  }

  /// 设置字体家族
  Future<void> setFontFamily(String family) async {
    selectedFontFamily.value = family;

    // 保存设置
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('fontFamily', family);
  }
}
