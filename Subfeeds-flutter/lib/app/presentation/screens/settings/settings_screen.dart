import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/language_controller.dart';
import 'package:subfeeds/app/controllers/theme_controller.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/core/theme/app_theme.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

/// 设置页面
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();
    final languageController = Get.find<LanguageController>();
    final userController = Get.find<UserController>();

    return Scaffold(
      appBar: AppBar(
        title: Text('设置'.tr),
        elevation: 0,
      ),
      body: ListView(
        children: [
          // 账户设置
          _buildSectionHeader('账户'.tr),

          // 用户信息
          Obx(() => userController.isLoggedIn
              ? _buildUserInfoTile(userController)
              : _buildLoginTile()),

          // 主题设置
          _buildSectionHeader('外观'.tr),

          // 暗黑模式
          Obx(() => SwitchListTile(
                title: Text('深色模式'.tr),
                subtitle: Text('切换应用的明暗主题'.tr),
                value: themeController.isDarkMode.value,
                activeColor: AppTheme.primaryColor,
                onChanged: (value) {
                  themeController.toggleThemeMode();
                },
              )),

          // 语言设置
          _buildSectionHeader('语言'.tr),

          // 当前语言
          Obx(() => ListTile(
                title: Text('语言'.tr),
                subtitle: Text(languageController.getCurrentLanguageName()),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () => _showLanguageDialog(context, languageController),
              )),

          // 关于
          _buildSectionHeader('关于'.tr),

          // 应用信息
          ListTile(
            title: Text('关于 SubFeeds'.tr),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // 导航到关于页面
              Get.toNamed(Routes.ABOUT);
            },
          ),

          // 隐私政策
          ListTile(
            title: Text('隐私政策'.tr),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // 导航到隐私政策页面
              Get.toNamed(Routes.PRIVACY_POLICY);
            },
          ),

          // 用户协议
          ListTile(
            title: Text('用户协议'.tr),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // 导航到用户协议页面
              Get.toNamed(Routes.TERMS_OF_SERVICE);
            },
          ),

          // Drawer测试页面
          ListTile(
            title: const Text('Drawer测试页面'),
            subtitle: const Text('测试持久化的Drawer组件'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // 导航到Drawer测试页面
              Get.toNamed(Routes.DRAWER_TEST);
            },
          ),

          // 版本信息
          ListTile(
            title: Text('版本'.tr),
            subtitle: const Text('1.0.0'),
            onTap: () {
              // 检查更新
            },
          ),

          // 退出登录
          Obx(() => userController.isLoggedIn
              ? ListTile(
                  title: Text(
                    '退出登录'.tr,
                    style: const TextStyle(color: Colors.red),
                  ),
                  leading: const Icon(
                    Icons.logout,
                    color: Colors.red,
                  ),
                  onTap: () => _showLogoutDialog(context, userController),
                )
              : const SizedBox.shrink()),
        ],
      ),
    );
  }

  // 构建分区标题
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  // 构建用户信息条目
  Widget _buildUserInfoTile(UserController userController) {
    return ListTile(
      leading: CircleAvatar(
        backgroundImage: userController.user.value?.avatar != null
            ? NetworkImage(userController.user.value!.avatar!)
            : null,
        child: userController.user.value?.avatar == null
            ? const Icon(Icons.person)
            : null,
      ),
      title: Text(userController.user.value?.name ?? '用户'.tr),
      subtitle: Text(userController.user.value?.account ?? ''),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        // 导航到个人资料页面
        Get.toNamed(Routes.PROFILE);
      },
    );
  }

  // 构建登录条目
  Widget _buildLoginTile() {
    return ListTile(
      leading: const CircleAvatar(
        child: Icon(Icons.person),
      ),
      title: Text('登录/注册'.tr),
      subtitle: Text('登录以使用更多功能'.tr),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        // 导航到登录页面
        Get.toNamed(Routes.LOGIN_SELECTION);
      },
    );
  }

  // 显示语言选择对话框
  void _showLanguageDialog(
      BuildContext context, LanguageController languageController) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('选择语言'.tr),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: languageController.supportedLanguages.length,
              itemBuilder: (BuildContext context, int index) {
                final language = languageController.supportedLanguages[index];
                return ListTile(
                  title: Text(language['name']!),
                  onTap: () {
                    languageController.updateLanguage(
                      language['languageCode']!,
                      language['countryCode']!,
                    );
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('取消'.tr),
            ),
          ],
        );
      },
    );
  }

  // 显示退出登录对话框
  void _showLogoutDialog(BuildContext context, UserController userController) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('退出登录'.tr),
          content: Text('确定要退出登录吗？'.tr),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('取消'.tr),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await userController.logout();
                Get.offAllNamed(Routes.LOGIN_SELECTION);
              },
              child: Text('确定'.tr),
            ),
          ],
        );
      },
    );
  }
}
