import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

class PremiumPlanScreen extends StatefulWidget {
  const PremiumPlanScreen({Key? key}) : super(key: key);

  @override
  State<PremiumPlanScreen> createState() => _PremiumPlanScreenState();
}

class _PremiumPlanScreenState extends State<PremiumPlanScreen> {
  bool _isYearlyPlan = true; // 默认选择年度计划
  // 添加状态跟踪变量
  late final ValueNotifier<bool> _isYearlyPlanNotifier;
  late final ValueNotifier<int> _selectedPlanNotifier;
  late final PageController _pageController;
  int _currentPage = 1;

  // 切换计划类型（年度/月度）
  void _togglePlanType(bool isYearly) {
    setState(() {
      _isYearlyPlan = isYearly;
      _isYearlyPlanNotifier.value = isYearly;
    });
  }

  @override
  void initState() {
    super.initState();
    _isYearlyPlanNotifier = ValueNotifier<bool>(_isYearlyPlan);
    _selectedPlanNotifier = ValueNotifier<int>(1); // 默认选中Family Plan
    _pageController = PageController(
      viewportFraction: 0.82,
      initialPage: 1, // 默认显示中间的套餐(Family Plan)
    );

    _pageController.addListener(() {
      if (_pageController.page != null) {
        final page = _pageController.page!.round();
        if (_currentPage != page) {
          setState(() {
            _currentPage = page;
            _selectedPlanNotifier.value = page;
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _isYearlyPlanNotifier.dispose();
    _selectedPlanNotifier.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFeae8ff), Color(0xFFfaf6f8)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const SizedBox(height: 20),
                        _buildHeaderSection(),
                        const SizedBox(height: 30),
                        _buildPlanTypeToggle(),
                        const SizedBox(height: 24),
                        _buildPlanCards(),
                        const SizedBox(height: 24),
                        _buildPolicyText(),
                        const SizedBox(height: 24),
                        // _buildContinueButton(),
                        // const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 顶部AppBar，只展示返回按钮
  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 20),
            onPressed: () => Get.back(),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  // 头部标题与描述
  Widget _buildHeaderSection() {
    return Column(
      children: [
        Text(
          'unlock_all_premium_features'.tr,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'pick_a_plan_that_fits'.tr,
          style: const TextStyle(
            fontSize: 14,
            color: Color(0xFF73839d),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // 计划类型切换（年度/月度）
  Widget _buildPlanTypeToggle() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 年度计划选项
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: GestureDetector(
              onTap: () => _togglePlanType(true),
              child: Text(
                'yearly'.tr,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: _isYearlyPlan ? FontWeight.bold : FontWeight.w500,
                  color: _isYearlyPlan
                      ? Theme.of(context).primaryColor
                      : const Color(0xFF8e8e8e),
                ),
              ),
            ),
          ),

          // 切换按钮
          Transform.scale(
            scale: 1,
            child: SizedBox(
              width: 60.spx,
              child: Switch(
                value: !_isYearlyPlan,
                onChanged: (value) => _togglePlanType(!value),
                activeColor: Colors.white,
                activeTrackColor: Theme.of(context).primaryColor,
                inactiveTrackColor: Theme.of(context).primaryColor,
                inactiveThumbColor: Colors.white,
              ),
            ),
          ),

          // 月度计划选项
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: GestureDetector(
              onTap: () => _togglePlanType(false),
              child: Text(
                'monthly'.tr,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight:
                      !_isYearlyPlan ? FontWeight.bold : FontWeight.w500,
                  color: !_isYearlyPlan
                      ? Theme.of(context).primaryColor
                      : const Color(0xFF8e8e8e),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 套餐计划卡片
  Widget _buildPlanCards() {
    return Column(
      children: [
        SizedBox(
          height: 450.spx, // 增加高度
          child: PageView.builder(
            controller: _pageController,
            itemCount: 3,
            itemBuilder: (context, index) {
              final plans = [
                _buildPlanItem(
                  title: 'Basic Plan',
                  monthlyPrice: '5.99',
                  yearlyPrice: '59.99',
                  features: _getBasicPlanFeatures(),
                  isPopular: false,
                  color: const Color(0xFF6D8BFF),
                  index: 0,
                ),
                _buildPlanItem(
                  title: 'Family Plan',
                  monthlyPrice: '3.99',
                  yearlyPrice: '39.99',
                  features: _getFamilyPlanFeatures(),
                  isPopular: true,
                  color: const Color(0xFFC472DF),
                  index: 1,
                ),
                _buildPlanItem(
                  title: 'Premium Plan',
                  monthlyPrice: '9.99',
                  yearlyPrice: '99.99',
                  features: _getPremiumPlanFeatures(),
                  isPopular: false,
                  color: const Color(0xFFF7A071),
                  index: 2,
                ),
              ];
              return plans[index];
            },
          ),
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(3, (index) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              height: 8,
              width: _currentPage == index ? 20 : 8,
              decoration: BoxDecoration(
                color: _currentPage == index
                    ? Theme.of(context).primaryColor
                    : Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            );
          }),
        ),
      ],
    );
  }

  // 单个套餐卡片项
  Widget _buildPlanItem({
    required String title,
    required String monthlyPrice,
    required String yearlyPrice,
    required List<PlanFeature> features,
    required bool isPopular,
    required Color color,
    required int index,
  }) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _isYearlyPlanNotifier,
        _selectedPlanNotifier,
      ]),
      builder: (context, _) {
        final isSelected = _selectedPlanNotifier.value == index;
        return TweenAnimationBuilder<double>(
          tween: Tween<double>(
            begin: 0.9,
            end: isSelected ? 1.0 : 0.9,
          ),
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          builder: (context, value, child) {
            return Transform.scale(
              scale: value,
              child: child,
            );
          },
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedPlanNotifier.value = index;
              });
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: isSelected
                          ? color.withOpacity(0.3)
                          : Colors.black.withOpacity(0.05),
                      blurRadius: isSelected ? 20 : 10,
                      spreadRadius: isSelected ? 2 : 0,
                      offset: const Offset(0, 5),
                    ),
                  ],
                  border:
                      isSelected ? Border.all(color: color, width: 2) : null,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 套餐顶部
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 16),
                      decoration: BoxDecoration(
                        color: color.withOpacity(0.1),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(14),
                          topRight: Radius.circular(14),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: color,
                            ),
                          ),
                          if (isPopular)
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 3),
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                'popular'.tr,
                                style: const TextStyle(
                                  fontSize: 11,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),

                    // 套餐价格
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '\$',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: color,
                                ),
                              ),
                              TweenAnimationBuilder<double>(
                                tween: Tween<double>(
                                  begin: double.parse(_isYearlyPlan
                                      ? yearlyPrice
                                      : monthlyPrice),
                                  end: double.parse(_isYearlyPlan
                                      ? yearlyPrice
                                      : monthlyPrice),
                                ),
                                duration: const Duration(milliseconds: 500),
                                curve: Curves.easeOutCubic,
                                builder: (context, value, _) {
                                  return Text(
                                    value.toStringAsFixed(2),
                                    style: TextStyle(
                                      fontSize: 28,
                                      fontWeight: FontWeight.bold,
                                      color: color,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child: Text(
                              _isYearlyPlan ? 'per_year'.tr : 'per_month'.tr,
                              key: ValueKey<bool>(_isYearlyPlan),
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF73839d),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 特性列表
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 4),
                        child: ListView(
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          children: features
                              .map((feature) =>
                                  _buildFeatureItem(feature, color: color))
                              .toList(),
                        ),
                      ),
                    ),

                    if (isSelected)
                      Padding(
                        padding: const EdgeInsets.only(
                            bottom: 12, left: 16, right: 16, top: 4),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          height: 36,
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              // 选择此计划
                              setState(() {
                                _selectedPlanNotifier.value = index;
                              });
                              Get.closeAllSnackbars();
                              // 继续按钮也可以使用这个选中的计划
                              Get.snackbar(
                                'waring'.tr,
                                'pricing_unopen'.tr,
                                snackPosition: SnackPosition.TOP,
                                backgroundColor: color.withOpacity(0.2),
                                colorText: color,
                                duration: const Duration(seconds: 1),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: color,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              padding: const EdgeInsets.symmetric(
                                  vertical: 0, horizontal: 0),
                            ),
                            child: Text(
                              'select_plan'.tr,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      )
                    else
                      const SizedBox(height: 12),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // 单个权限特性项
  Widget _buildFeatureItem(PlanFeature feature, {Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(
            feature.iconPath,
            width: 18.spx,
            height: 18.spx,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              feature.description,
              style: const TextStyle(
                fontSize: 13,
                color: Color(0xFF333333),
                height: 1.3,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // 隐私政策文本
  Widget _buildPolicyText() {
    return Text(
      'subscription_auto_renew_notice'.tr,
      style: const TextStyle(
        fontSize: 12,
        color: Color(0xFF73839d),
      ),
      textAlign: TextAlign.center,
    );
  }

  // 继续按钮
  // Widget _buildContinueButton() {
  //   return SizedBox(
  //     width: double.infinity,
  //     child: ElevatedButton(
  //       onPressed: () {
  //         // 处理付费逻辑
  //         final selectedPlanIndex = _selectedPlanNotifier.value;
  //         final planNames = ['Basic Plan', 'Family Plan', 'Premium Plan'];
  //         final selectedPlanName = planNames[selectedPlanIndex];

  //         Get.snackbar(
  //           'success'.tr,
  //           'selected_plan_processing'.tr + selectedPlanName,
  //           snackPosition: SnackPosition.BOTTOM,
  //           backgroundColor: Theme.of(context).primaryColor.withOpacity(0.2),
  //           colorText: Theme.of(context).primaryColor,
  //           duration: const Duration(seconds: 2),
  //         );

  //         // TODO: 实际的付费处理逻辑
  //       },
  //       style: ElevatedButton.styleFrom(
  //         backgroundColor: Theme.of(context).primaryColor,
  //         foregroundColor: Colors.white,
  //         padding: const EdgeInsets.symmetric(vertical: 14),
  //         shape: RoundedRectangleBorder(
  //           borderRadius: BorderRadius.circular(8),
  //         ),
  //       ),
  //       child: Text(
  //         'continue'.tr,
  //         style: const TextStyle(
  //           fontSize: 16,
  //           fontWeight: FontWeight.bold,
  //         ),
  //       ),
  //     ),
  //   );
  // }

  // Basic Plan 特性
  List<PlanFeature> _getBasicPlanFeatures() {
    return [
      PlanFeature(
          iconPath: 'assets/pay/pay1.svg',
          description: 'Update Speed: 60 mins'),
      PlanFeature(
          iconPath: 'assets/pay/pay2.svg', description: 'Followed Blogs: 50'),
      PlanFeature(
          iconPath: 'assets/pay/pay3.svg', description: 'Feeds Count: 100'),
      PlanFeature(iconPath: 'assets/pay/pay4.svg', description: 'Basic Search'),
      PlanFeature(
          iconPath: 'assets/pay/pay5.svg',
          description: 'Limited Theme Options'),
      PlanFeature(
          iconPath: 'assets/pay/pay6.svg', description: 'Standard Features'),
    ];
  }

  // Family Plan 特性
  List<PlanFeature> _getFamilyPlanFeatures() {
    return [
      PlanFeature(
          iconPath: 'assets/pay/pay1.svg',
          description: 'Update Speed: 45 mins'),
      PlanFeature(
          iconPath: 'assets/pay/pay2.svg', description: 'Followed Blogs: 200'),
      PlanFeature(
          iconPath: 'assets/pay/pay3.svg', description: 'Feeds Count: 500'),
      PlanFeature(
          iconPath: 'assets/pay/pay4.svg', description: 'Advanced Search'),
      PlanFeature(
          iconPath: 'assets/pay/pay5.svg', description: 'Ad-Free Experience'),
      PlanFeature(
          iconPath: 'assets/pay/pay6.svg',
          description: 'Family Sharing (5 users)'),
    ];
  }

  // Premium Plan 特性
  List<PlanFeature> _getPremiumPlanFeatures() {
    return [
      PlanFeature(
          iconPath: 'assets/pay/pay1.svg',
          description: 'Update Speed: 30 mins'),
      PlanFeature(
          iconPath: 'assets/pay/pay2.svg', description: 'Followed Blogs: 400'),
      PlanFeature(
          iconPath: 'assets/pay/pay3.svg', description: 'Feeds Count: 1,000'),
      PlanFeature(
          iconPath: 'assets/pay/pay4.svg',
          description: 'Basic Search & Article Summary'),
      PlanFeature(
          iconPath: 'assets/pay/pay5.svg',
          description: 'Ad-Free & Theme Customization'),
      PlanFeature(
          iconPath: 'assets/pay/pay6.svg', description: 'Premium Features'),
    ];
  }
}

// 套餐特性模型
class PlanFeature {
  final String iconPath;
  final String description;

  PlanFeature({required this.iconPath, required this.description});
}
