import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/data/repositories/user_repository.dart';
import 'dart:io';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/data/services/http_service.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:cached_network_image/cached_network_image.dart';

class PersonalInfoScreen extends StatefulWidget {
  const PersonalInfoScreen({super.key});
  @override
  State<PersonalInfoScreen> createState() => _PersonalInfoScreenState();
}

class _PersonalInfoScreenState extends State<PersonalInfoScreen> {
  final UserController _userController = Get.find<UserController>();
  final UserRepository _userRepository = UserRepository();
  final _nameFormKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  XFile? _selectedImage;
  bool _isLoading = false;
  bool _isInitializing = true;

  // 错误信息状态
  String? _nameError;

  @override
  void initState() {
    super.initState();
    _initializeUserInfo();
  }

  Future<void> _initializeUserInfo() async {
    print('开始初始化用户信息');
    try {
      // 直接从服务器获取最新信息
      final response = await _userRepository.getUserInfo();
      print('从服务器获取的用户信息: ${response.data?.toJson()}');

      if (response.isSuccess && response.data != null) {
        _userController.user.value = response.data;
        _nameController.text = response.data?.name ?? '';
        // 保存到本地
        await _userRepository.saveUserInfo(response.data!);
      } else {
        // 如果服务器获取失败，尝试使用本地数据
        final localUser = await _userRepository.getUserInfoFromLocal();
        if (localUser != null) {
          _userController.user.value = localUser;
          _nameController.text = localUser.name ?? '';
        } else {
          _userController.user.value = null;
        }
      }
    } catch (e) {
      print('获取用户信息错误: $e');
      Get.snackbar(
        'error'.tr,
        'get_user_info_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    } finally {
      // 无论成功失败，都将初始化状态设置为false
      setState(() {
        _isInitializing = false;
      });
    }
  }

  Future<void> _updateAvatar() async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      // 如果用户没有选择图片，直接返回
      if (image == null) return;

      // 判断头像大小 大于5mb提示错误
      final File imageFile = File(image.path);
      final int fileSize = await imageFile.length();
      if (fileSize > 5 * 1024 * 1024) {
        // 使用 Dialog 显示错误信息，使用 SelectableText.rich 展示
        Get.snackbar(
          'error'.tr,
          'avatar_size_error'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        return; // 文件过大，提前返回不继续上传
      }

      setState(() {
        _selectedImage = image;
        _isLoading = true;
      });
      // 上传头像
      final uploadResponse = await _userRepository.uploadAvatar(image.path);
      if (uploadResponse.isSuccess) {
        // 头像上传成功后，刷新用户信息以获取新的头像URL
        final userResponse = await _userRepository.getUserInfo();
        if (userResponse.isSuccess && userResponse.data != null) {
          _userController.user.value = userResponse.data;
          await _userRepository.saveUserInfo(userResponse.data!);
          Get.snackbar(
            'success'.tr,
            'personal_info_success'.tr,
            snackPosition: SnackPosition.TOP,
            icon: SvgPicture.asset('assets/feeds/right.svg'),
            backgroundColor:
                Theme.of(Get.context!).brightness == Brightness.dark
                    ? const Color(0xFF161617)
                    : const Color(0xFF161617),
            colorText: Theme.of(Get.context!).brightness == Brightness.dark
                ? Colors.white
                : Colors.white,
          );
        }
      } else {
        // 使用 Dialog 显示错误信息，使用 SelectableText.rich 展示
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('error'.tr),
            content: SelectableText.rich(
              TextSpan(
                text: uploadResponse.msg ?? 'upload_failed'.tr,
                style: TextStyle(color: Colors.red),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('确定'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      print('头像更新错误: $e');
      // 使用 Dialog 显示错误信息，使用 SelectableText.rich 展示
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('error'.tr),
          content: SelectableText.rich(
            TextSpan(
              text: e.toString(),
              style: TextStyle(color: Colors.red),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('确定'),
            ),
          ],
        ),
      );
    } finally {
      setState(() {
        _selectedImage = null;
        _isLoading = false;
      });
    }
  }

  Future<void> _updateName() async {
    // 清除之前的错误
    setState(() {
      _nameError = null;
    });

    if (_nameFormKey.currentState?.validate() ?? false) {
      setState(() => _isLoading = true);
      try {
        final user = _userController.user.value;
        if (user != null) {
          final updatedUser = user.copyWith(name: _nameController.text);
          final response = await _userRepository.updateUserProfile(updatedUser);
          if (response.isSuccess) {
            Get.snackbar(
              'success'.tr,
              'personal_info_success'.tr,
              snackPosition: SnackPosition.TOP,
              icon: SvgPicture.asset('assets/feeds/right.svg'),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              colorText: Theme.of(Get.context!).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            );
            await _userController.refreshUserInfo();
          } else {
            setState(() {
              _nameError = response.msg ?? 'personal_info_error'.tr;
            });
          }
        }
      } catch (e) {
        setState(() {
          _nameError = 'personal_info_error'.tr;
        });
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF1e2020)
          : const Color(0xFFeef2f9),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(50),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 20.spx,
            ),
            child: Stack(
              children: [
                // 标题居中
                Align(
                  alignment: Alignment.center,
                  child: Text(
                    'settings_personal_info'.tr,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontSize: 16.spx,
                          fontWeight: FontWeight.w600,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
                // 返回按钮
                Align(
                  alignment: Alignment.centerLeft,
                  child: InkWell(
                    onTap: () => Get.back(),
                    borderRadius: BorderRadius.circular(16),
                    child: SvgPicture.asset(
                      'assets/feeds/back.svg',
                      width: 20.spx,
                      height: 20.spx,
                      colorFilter: ColorFilter.mode(
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black,
                        BlendMode.srcIn,
                      ),
                      fit: BoxFit.none,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: _isInitializing
          ? Center(
              child: LoadingIndicator(
                size: 100.spx,
              ),
            ) // 显示加载中
          : Obx(() {
              final user = _userController.user.value;
              if (user == null) {
                return Center(
                    child: LoadingIndicator(
                  size: 100.spx,
                )); // 用加载动画替换错误文本
              }

              return _isLoading
                  ? Center(
                      child: LoadingIndicator(
                        size: 100.spx,
                      ),
                    )
                  : SingleChildScrollView(
                      padding: EdgeInsets.symmetric(
                        horizontal: 20.spx,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 个人信息卡片
                          Card(
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.spx),
                            ),
                            child: Column(
                              children: [
                                // 头像和用户名一行显示
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8.spx, vertical: 10.spx),
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      'settings_account'.tr,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            fontSize: 14.spx,
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 8.spx),
                                  child: Divider(
                                    height: 0.5,
                                    thickness: 0.5,
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? const Color(0xFF747474)
                                        : const Color(0xFFD5D5D5),
                                  ),
                                ),
                                SizedBox(height: 10.spx),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 8.spx),
                                  child: Row(
                                    children: [
                                      // 头像部分
                                      Stack(
                                        clipBehavior: Clip.none,
                                        children: [
                                          ClipOval(
                                            child: user.avatar != null &&
                                                    user.avatar!.isNotEmpty
                                                ? SizedBox(
                                                    width: 54.spx,
                                                    height: 54.spx,
                                                    child: CachedNetworkImage(
                                                      imageUrl: user.avatar!,
                                                      fit: BoxFit.cover,
                                                      placeholder:
                                                          (context, url) =>
                                                              SizedBox(
                                                        width: 54.spx,
                                                        height: 54.spx,
                                                        child: LoadingIndicator(
                                                          size: 54.spx,
                                                        ),
                                                      ),
                                                      errorWidget: (context,
                                                              error,
                                                              stackTrace) =>
                                                          Image.asset(
                                                        'assets/images/default_avatar.png',
                                                        fit: BoxFit.cover,
                                                      ),
                                                    ))
                                                : Image.asset(
                                                    'assets/images/default_avatar.png',
                                                    fit: BoxFit.cover,
                                                  ),
                                          ),
                                          Positioned(
                                            right: -15.spx,
                                            bottom: -15.spx,
                                            child: IconButton(
                                              icon: Container(
                                                padding:
                                                    EdgeInsets.all(3.6.spx),
                                                decoration: BoxDecoration(
                                                  color: Theme.of(context)
                                                      .primaryColor,
                                                  shape: BoxShape.circle,
                                                  border: Border.all(
                                                    color: Theme.of(context)
                                                                .brightness ==
                                                            Brightness.dark
                                                        ? const Color(
                                                            0xFF1e2020)
                                                        : const Color(
                                                            0xFFeef2f9),
                                                    width: 2.spx,
                                                  ),
                                                ),
                                                child: SvgPicture.asset(
                                                  'assets/icons/edit_avatar.svg',
                                                  colorFilter: ColorFilter.mode(
                                                    Theme.of(context)
                                                                .brightness ==
                                                            Brightness.dark
                                                        ? const Color(
                                                            0xFF1E1F1F)
                                                        : const Color(
                                                            0xFFEEF2F9),
                                                    BlendMode.srcIn,
                                                  ),
                                                ),
                                              ),
                                              onPressed: _updateAvatar,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(width: 16),
                                      // 用户名输入框
                                      Expanded(
                                        child: Form(
                                          key: _nameFormKey,
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'personal_info_name'.tr,
                                                style: TextStyle(
                                                  fontSize: 12.spx,
                                                  fontWeight: FontWeight.w500,
                                                  color: Theme.of(context)
                                                              .brightness ==
                                                          Brightness.dark
                                                      ? const Color(0xFFD1D1D1)
                                                      : const Color(0xFF999999),
                                                ),
                                              ),
                                              SizedBox(height: 8.spx),
                                              TextFormField(
                                                controller: _nameController,
                                                style: TextStyle(
                                                  fontSize: 13.spx,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                decoration: InputDecoration(
                                                  fillColor: Theme.of(context)
                                                              .brightness ==
                                                          Brightness.dark
                                                      ? const Color(0xFF444444)
                                                      : const Color(0xFFf7faff),
                                                  border: OutlineInputBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8.spx),
                                                  ),
                                                  enabledBorder:
                                                      OutlineInputBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8.spx),
                                                    borderSide: BorderSide(
                                                        color: Theme.of(context)
                                                                    .brightness ==
                                                                Brightness.dark
                                                            ? const Color(
                                                                0xFF747474)
                                                            : const Color(
                                                                0xFFD5D5D5)),
                                                  ),
                                                  focusedBorder:
                                                      OutlineInputBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8.spx),
                                                    borderSide: BorderSide(
                                                      color: Theme.of(context)
                                                          .primaryColor,
                                                    ),
                                                  ),
                                                  errorBorder:
                                                      OutlineInputBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8.spx),
                                                    borderSide:
                                                        const BorderSide(
                                                      color: Color(0xFFD64E36),
                                                    ),
                                                  ),
                                                  focusedErrorBorder:
                                                      OutlineInputBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8.spx),
                                                    borderSide:
                                                        const BorderSide(
                                                      color: Color(0xFFD64E36),
                                                    ),
                                                  ),
                                                  hintText:
                                                      'personal_info_edit_name'
                                                          .tr,
                                                  contentPadding:
                                                      EdgeInsets.symmetric(
                                                          horizontal: 8.spx,
                                                          vertical: 10.spx),
                                                ),
                                                validator: (value) {
                                                  if (value == null ||
                                                      value.isEmpty) {
                                                    return 'personal_info_error'
                                                        .tr;
                                                  }
                                                  return null;
                                                },
                                              ),
                                              if (_nameError != null) ...[
                                                SizedBox(height: 4.spx),
                                                Text(
                                                  _nameError!,
                                                  style: TextStyle(
                                                    color:
                                                        const Color(0xFFD64E36),
                                                    fontSize: 12.spx,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ],
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 10.spx),

                                // 邮箱显示
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 8.spx),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'personal_info_email'.tr,
                                        style: TextStyle(
                                          fontSize: 12.spx,
                                          fontWeight: FontWeight.w500,
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? const Color(0xFFD1D1D1)
                                              : const Color(0xFF999999),
                                        ),
                                      ),
                                      SizedBox(height: 8.spx),
                                      TextFormField(
                                        enabled: false,
                                        controller: user.email != null
                                            ? TextEditingController(
                                                text: user.email)
                                            : user.account != null
                                                ? TextEditingController(
                                                    text: user.account)
                                                : null,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.spx),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.spx),
                                            borderSide: BorderSide(
                                                color: Theme.of(context)
                                                            .brightness ==
                                                        Brightness.dark
                                                    ? const Color(0xFF747474)
                                                    : const Color(0xFFD5D5D5)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.spx),
                                            borderSide: BorderSide(
                                              color: Theme.of(context)
                                                  .primaryColor,
                                            ),
                                          ),
                                          errorBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            borderSide: const BorderSide(
                                              color: Color(0xFFD64E36),
                                            ),
                                          ),
                                          fillColor:
                                              Theme.of(context).brightness ==
                                                      Brightness.dark
                                                  ? const Color(0xFF444444)
                                                  : const Color(0xFFf7faff),
                                          focusedErrorBorder:
                                              OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            borderSide: const BorderSide(
                                              color: Color(0xFFD64E36),
                                            ),
                                          ),
                                          disabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            borderSide: BorderSide(
                                                color: Theme.of(context)
                                                            .brightness ==
                                                        Brightness.dark
                                                    ? const Color(0xFF747474)
                                                    : const Color(0xFFD5D5D5)),
                                          ),
                                          hintText: 'personal_info_email'.tr,
                                          contentPadding: EdgeInsets.symmetric(
                                            horizontal: 8.spx,
                                            vertical: 10.spx,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 10.spx),
                                // 保存按钮
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 8.spx),
                                  child: Align(
                                    alignment: Alignment.centerRight,
                                    child: SizedBox(
                                      width: MediaQuery.of(context).size.width,
                                      child: ElevatedButton(
                                        onPressed: _updateName,
                                        style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 11.spx,
                                            vertical: 8,
                                          ),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12.spx),
                                          ),
                                          backgroundColor: Theme.of(context)
                                                      .brightness ==
                                                  Brightness.dark
                                              ? const Color(0xFF2B2B2B)
                                              : Theme.of(context).primaryColor,
                                          foregroundColor: Colors.white,
                                        ),
                                        child: Text(
                                          'personal_info_save'.tr,
                                          style: TextStyle(
                                            fontSize: 14.spx,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),

                                if (user.phone != null) ...[
                                  const Divider(indent: 16, endIndent: 16),
                                  ListTile(
                                    title: Text('personal_info_phone'.tr),
                                    subtitle: Text(user.phone!),
                                    leading: const Icon(Icons.phone),
                                  ),
                                ],
                                const SizedBox(height: 16),
                              ],
                            ),
                          ),

                          // 修改密码按钮
                          SizedBox(height: 10.spx),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 2.spx),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? const Color(0xFF3B3B3B)
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(8.spx),
                              ),
                              child: _buildSettingItem(
                                context,
                                icon: 'assets/icons/delete.svg',
                                title: 'change_password'.tr,
                                onTap: () {
                                  Get.toNamed(Routes.CHANGE_PASSWORD);
                                },
                              ),
                            ),
                          ),
                          // 注销账号
                          SizedBox(height: 10.spx),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 2.spx),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? const Color(0xFF3B3B3B)
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(8.spx),
                              ),
                              child: _buildSettingItem(
                                context,
                                icon: 'assets/icons/delete.svg',
                                title: 'cancel_account'.tr,
                                onTap: () {
                                  _cancelAccount();
                                },
                              ),
                            ),
                          ),

                          const SizedBox(height: 24),
                        ],
                      ),
                    );
            }),
    );
  }

  Widget _buildSettingItem(
    BuildContext context, {
    required String icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      // leading: SvgPicture.asset(
      //   icon,
      //   width: 30.spx,
      //   height: 30.spx,
      // ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontSize: 14.spx,
              fontWeight: FontWeight.w500,
            ),
      ),
      subtitle: null,
      // ? Text(
      //     subtitle,
      //     style: Theme.of(context).textTheme.bodySmall?.copyWith(
      //           fontSize: 14,
      //         ),
      //   )
      // : null,
      trailing: trailing ??
          Icon(
            Icons.chevron_right,
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFFD1D1D1)
                : const Color(0xFF999999),
            size: 20.spx,
          ),
      onTap: onTap,
    );
  }

  Future _cancelAccount() async {
    final HttpService httpService = HttpService();
    Get.dialog(
      Dialog(
        backgroundColor: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFFF7FAFF)
            : const Color(0xFF444444),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.info_rounded,
                color: const Color(0xFFFFC107),
                size: 50.spx,
              ),
              const SizedBox(height: 16),
              Text(
                'cancel_account_description'.tr,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontSize: 14.spx,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFFD1D1D1)
                          : const Color(0xFF999999),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        backgroundColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFF555555)
                                : const Color(0xFFE6E6E6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.spx),
                        ),
                        padding: EdgeInsets.symmetric(
                          vertical: 10.spx,
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFFD1D1D1)
                                  : const Color(0xFF999999),
                              fontWeight: FontWeight.w600,
                              fontSize: 14.spx,
                            ),
                      ),
                    ),
                  ),
                  SizedBox(width: 16.spx),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        final response = await _userRepository.deleteAccount();
                        if (response.isSuccess) {
                          Get.snackbar("Operation_successful".tr,
                              "account_delete_success".tr);
                          // 清除本地用户信息
                          try {
                            final prefs = await SharedPreferences.getInstance();
                            await prefs.remove('user_info');
                          } catch (e) {
                            print('清除用户信息失败: $e');
                          }
                          await httpService.clearToken();
                          Get.offAllNamed(Routes.LOGIN_SELECTION);
                        }
                        // Get.back(); // 关闭对话框
                        // await Get.find<UserController>().logout();
                        // Get.offAllNamed(Routes.LOGIN_SELECTION);
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: EdgeInsets.symmetric(
                          vertical: 10.spx,
                        ),
                      ),
                      child: Text(
                        'confirm'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontSize: 14.spx,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
