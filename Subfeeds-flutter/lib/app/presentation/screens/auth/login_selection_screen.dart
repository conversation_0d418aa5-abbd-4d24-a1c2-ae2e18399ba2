import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/gestures.dart';

/// 登录选择页面
class LoginSelectionScreen extends StatelessWidget {
  const LoginSelectionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取已初始化的UserController
    final userController = Get.find<UserController>();

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF3c3c3c)
            : const Color(0xFFf7faff),
        // decoration: BoxDecoration(
        //   image: DecorationImage(
        //     image: AssetImage('assets/images/login_bg.png'),
        //     fit: BoxFit.cover,
        //   ),
        // ),
        child: <PERSON><PERSON><PERSON>(
          child: SingleChildScrollView(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Logo
                  SizedBox(height: 116.spx),
                  Center(
                      child: Column(
                    children: [
                      Image.asset(
                        'assets/images/login_logo.png',
                        width: 66.spx,
                        height: 66.spx,
                      ),
                      SizedBox(height: 10.spx),
                      Text(
                        'SubFeeds',
                        style: TextStyle(
                          fontSize: 19.spx,
                          fontWeight: FontWeight.bold,
                          // 斜体
                          fontStyle: FontStyle.italic,
                          // 字体
                          fontFamily: 'Rubik',
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                        ),
                      ),
                    ],
                  )),
                  SizedBox(height: 130.spx),
                  // 登录选择按钮
                  _buildLoginButtons(context, userController),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建登录按钮
  Widget _buildLoginButtons(
      BuildContext context, UserController userController) {
    debugPrint('构建登录按钮');
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        children: [
          ElevatedButton(
            onPressed: () {
              debugPrint('点击注册按钮');
              try {
                Get.toNamed(Routes.REGISTER);
              } catch (e) {
                debugPrint('导航到注册页面失败: $e');
                Get.snackbar(
                  'error'.tr,
                  'register_failed'.tr,
                  snackPosition: SnackPosition.TOP,
                  icon: const Icon(Icons.error, color: Colors.red),
                  backgroundColor:
                      Theme.of(Get.context!).brightness == Brightness.dark
                          ? const Color(0xFF161617)
                          : const Color(0xFF161617),
                  colorText:
                      Theme.of(Get.context!).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.white,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              elevation: 0,
              minimumSize: const Size(double.infinity, 44),
            ),
            child: Text(
              'login_selection_sign_up'.tr,
              style: TextStyle(
                fontSize: 14.spx,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          SizedBox(height: 15.spx),
          // 登录按钮
          ElevatedButton(
            onPressed: () {
              debugPrint('点击登录按钮');
              try {
                Get.toNamed(Routes.LOGIN);
              } catch (e) {
                debugPrint('导航到登录页面失败: $e');

                Get.snackbar(
                  'login_selection_error'.tr,
                  'login_selection_login_error'.tr,
                  snackPosition: SnackPosition.TOP,
                  icon: const Icon(Icons.error, color: Colors.red),
                  backgroundColor:
                      Theme.of(Get.context!).brightness == Brightness.dark
                          ? const Color(0xFF161617)
                          : const Color(0xFF161617),
                  colorText:
                      Theme.of(Get.context!).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.white,
                );
              }
            },
            style: OutlinedButton.styleFrom(
              backgroundColor: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF565656)
                  : const Color(0xFFffffff),
              side: BorderSide(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF747474)
                      : const Color(0xFFD5D5D5),
                  width: 2),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              elevation: 0,
              minimumSize: const Size(double.infinity, 44),
            ),
            child: Text(
              'login_selection_login'.tr,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontSize: 14.spx,
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),

          // 带文字的分割线
          SizedBox(height: 20.spx),
          _buildDividerWithText(context, 'or'.tr),
          SizedBox(height: 20.spx),

          // Apple登录按钮
          _buildSocialButton(
            context: context,
            icon: 'assets/icons/apple.svg',
            text: defaultTargetPlatform == TargetPlatform.iOS
                ? 'login_apple'.tr
                : 'login_apple_not_available'.tr,
            onPressed: defaultTargetPlatform == TargetPlatform.iOS
                ? () async {
                    debugPrint('点击Apple登录按钮');
                    try {
                      final result = await userController.signInWithApple();
                      if (result != null && result is bool && result) {
                        // 登录成功，不需要任何提示，controller会处理导航
                        return;
                      }
                      // 其他所有情况都静默返回
                      debugPrint('Apple登录未完成');
                      return;
                    } catch (e) {
                      debugPrint('Apple登录错误: $e');
                      // 所有错误情况都静默返回
                      return;
                    }
                  }
                : null,
          ),
          SizedBox(height: 15.spx),
          // Google登录按钮
          _buildSocialButton(
            context: context,
            icon: 'assets/icons/google.svg',
            text: 'login_google'.tr,
            onPressed: () async {
              debugPrint('点击Google登录按钮');
              try {
                final result = await userController.signInWithGoogle();
                if (result != null && result is bool && result) {
                  return;
                }
                debugPrint('Google登录未完成');
                return;
              } catch (e) {
                debugPrint('Google登录错误: $e');
                return;
              }
            },
          ),
          SizedBox(height: 60.spx),
          // 同意      const SizedBox(height: 16),条款
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF757575),
              ),
              children: [
                const TextSpan(
                  text: 'By entering, you agree to SubFeeds ',
                ),
                TextSpan(
                  text: 'Privacy Policy',
                  style: TextStyle(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFFD1D1D1)
                        : const Color(0xFF999999),
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Get.toNamed(Routes.PRIVACY_POLICY);
                    },
                ),
                const TextSpan(
                  text: ' and ',
                ),
                TextSpan(
                  text: 'Terms of Service',
                  style: TextStyle(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFFD1D1D1)
                        : const Color(0xFF999999),
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Get.toNamed(Routes.TERMS_OF_SERVICE);
                    },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建社交登录按钮
  Widget _buildSocialButton({
    required BuildContext context,
    required String icon,
    required String text,
    VoidCallback? onPressed,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF565656)
            : const Color(0xFFffffff),
        foregroundColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFFD1D1D1)
            : const Color(0xFF999999),
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        side: BorderSide(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF747474)
                : const Color(0xFFD5D5D5),
            width: 2),
        elevation: 0,
        minimumSize: const Size(double.infinity, 44),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 图标
          Container(
            width: 16.spx,
            height: 16.spx,
            margin: EdgeInsets.only(right: 15.spx),
            child: SvgPicture.asset(icon, width: 20.spx, height: 20.spx),
          ),
          // 文字
          Text(
            text,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontSize: 14.spx,
                  fontWeight: FontWeight.w600,
                ),
          ),
        ],
      ),
    );
  }

  /// 构建带文字的分割线
  Widget _buildDividerWithText(BuildContext context, String text) {
    return Row(
      children: [
        // 左侧分割线
        Expanded(
          child: Container(
            height: 1,
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF747474)
                : const Color(0xFFD5D5D5),
          ),
        ),
        // 中间文字
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14.spx,
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFFD1D1D1)
                  : const Color(0xFF999999),
              fontWeight: FontWeight.w500,
              fontFamily: 'Inter',
            ),
          ),
        ),
        // 右侧分割线
        Expanded(
          child: Container(
            height: 1,
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF747474)
                : const Color(0xFFD5D5D5),
          ),
        ),
      ],
    );
  }
}
