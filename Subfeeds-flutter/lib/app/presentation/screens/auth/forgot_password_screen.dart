import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:form_field_validator/form_field_validator.dart';

/// 忘记密码页面
class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  // 表单Key
  final _formKey = GlobalKey<FormState>();

  // 控制器
  final _emailController = TextEditingController();
  final _codeController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // 是否显示密码
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // 步骤
  int _currentStep = 0;

  // 用户控制器
  late UserController _userController;

  // 验证码倒计时
  final RxInt _countdown = 0.obs;

  @override
  void initState() {
    super.initState();
    _userController = Get.find<UserController>();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black,
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      GestureDetector(
                        onTap: () => Get.back(),
                        child: SvgPicture.asset(
                          'assets/feeds/back.svg',
                          height: 14.spx,
                          width: 14.spx,
                          colorFilter: const ColorFilter.mode(
                            Colors.white,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ],
                  ),
                  // 标题
                  const SizedBox(height: 30),
                  Text(
                    'forgot_password_reset'.tr,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.w900,
                          color: Colors.white,
                          fontSize: 20,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'forgot_password_subtitle'.tr,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: const Color(0xFF72849c),
                          fontSize: 16,
                        ),
                  ),

                  const SizedBox(height: 30),

                  // 步骤指示器
                  Row(
                    children: [
                      _buildStepIndicator(0, 'forgot_password_step_1'.tr),
                      _buildStepConnector(_currentStep > 0),
                      _buildStepIndicator(1, 'forgot_password_step_2'.tr),
                    ],
                  ),

                  const SizedBox(height: 40),

                  // 步骤内容
                  _currentStep == 0 ? _buildEmailStep() : _buildPasswordStep(),

                  const SizedBox(height: 30),

                  // 按钮
                  Obx(() => ElevatedButton(
                        onPressed: _userController.isLoading.value
                            ? null
                            : _currentStep == 0
                                ? _verifyEmail
                                : _resetPassword,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          minimumSize: const Size(double.infinity, 56),
                        ),
                        child: _userController.isLoading.value
                            ? SizedBox(
                                width: 24.spx,
                                height: 24.spx,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : Text(
                                _currentStep == 0
                                    ? 'forgot_password_next'.tr
                                    : 'forgot_password_submit'.tr,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      )),

                  const SizedBox(height: 20),

                  // 返回登录
                  Center(
                    child: TextButton(
                      onPressed: () => Get.toNamed(Routes.LOGIN),
                      child: Text('forgot_password_back_login'.tr),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }

  /// 构建步骤指示器
  Widget _buildStepIndicator(int step, String label) {
    final isActive = _currentStep >= step;

    return Expanded(
      child: Column(
        children: [
          Container(
            width: 40.spx,
            height: 40.spx,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color:
                  isActive ? Theme.of(context).primaryColor : Colors.grey[300],
            ),
            child: Center(
              child: Text(
                '${step + 1}',
                style: TextStyle(
                  color: isActive ? Colors.white : Colors.grey[600],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color:
                  isActive ? Theme.of(context).primaryColor : Colors.grey[600],
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建步骤连接器
  Widget _buildStepConnector(bool isActive) {
    return Container(
      width: 40.spx,
      height: 2.spx,
      color: isActive ? Theme.of(context).primaryColor : Colors.grey[300],
    );
  } 

  /// 构建邮箱验证步骤
  Widget _buildEmailStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 邮箱输入框
        TextFormField(
          style: const TextStyle(color: Colors.white),
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          decoration: InputDecoration(
            labelText: 'forgot_password_email'.tr,
            hintText: 'forgot_password_email_hint'.tr,
            prefixIcon: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: SvgPicture.asset(
                'assets/icons/email.svg',
                width: 16.spx,
                height: 16.spx,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
            prefixIconConstraints: BoxConstraints(
              minWidth: 48.spx,
              maxHeight: 48.spx,
            ),
            fillColor: Colors.transparent,
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.error, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF393939), width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.error, width: 1),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF424242), width: 1),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'forgot_password_email_hint'.tr;
            }
            if (!GetUtils.isEmail(value)) {
              return 'forgot_password_email_hint'.tr;
            }
            return null;
          },
        ),

        const SizedBox(height: 20),

        // 验证码输入框
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: TextFormField(
                style: const TextStyle(color: Colors.white),
                controller: _codeController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'forgot_password_verify_code'.tr,
                  hintText: 'forgot_password_verify_code_hint'.tr,
                  prefixIcon: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    child: SvgPicture.asset(
                      'assets/icons/code.svg',
                      width: 20.spx,
                      height: 20.spx,
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  prefixIconConstraints: BoxConstraints(
                    minWidth: 48.spx,
                    maxHeight: 48.spx,
                  ),
                  fillColor: Colors.transparent,
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.error, width: 1),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: const Color(0xFF393939), width: 1),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.error, width: 1),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: const Color(0xFF323232),
                    ),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'forgot_password_verify_code_hint'.tr;
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Obx(() => ElevatedButton(
                  onPressed:
                      _countdown.value > 0 || _userController.isLoading.value
                          ? null
                          : _sendVerificationCode,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    minimumSize: const Size(120, 56),
                  ),
                  child: Text(
                    _countdown.value > 0
                        ? 'forgot_password_retry_seconds'
                            .tr
                            .replaceAll('%s', _countdown.value.toString())
                        : 'forgot_password_get_code'.tr,
                  ),
                )),
          ],
        ),
      ],
    );
  }

  /// 构建密码重置步骤
  Widget _buildPasswordStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 密码输入框
        TextFormField(
          controller: _passwordController,
          obscureText: _obscurePassword,
          decoration: InputDecoration(
            labelText: 'forgot_password_new_password'.tr,
            hintText: 'forgot_password_new_password_hint'.tr,
            prefixIcon: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: SvgPicture.asset(
                'assets/icons/key.svg',
                width: 20.spx,
                height: 20.spx,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
            prefixIconConstraints: BoxConstraints(
              minWidth: 48.spx,
              maxHeight: 48.spx,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword
                    ? Icons.visibility_outlined
                    : Icons.visibility_off_outlined,
                color: Colors.white,
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
            fillColor: Colors.transparent,
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.error, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF393939), width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.error, width: 1),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: const Color(0xFF323232),
              ),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'forgot_password_new_password_hint'.tr;
            }
            if (value.length < 8) {
              return 'personal_info_password_rule'.tr;
            }
            
            return null;
          },
        ),

        const SizedBox(height: 20),

        // 确认密码输入框
        TextFormField(
          controller: _confirmPasswordController,
          obscureText: _obscureConfirmPassword,
          decoration: InputDecoration(
            labelText: 'forgot_password_confirm_password'.tr,
            hintText: 'forgot_password_confirm_password_hint'.tr,
            prefixIcon: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: SvgPicture.asset(
                'assets/icons/key.svg',
                width: 20.spx,
                height: 20.spx,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
            prefixIconConstraints: BoxConstraints(
              minWidth: 48.spx,
              maxHeight: 48.spx,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscureConfirmPassword
                    ? Icons.visibility_outlined
                    : Icons.visibility_off_outlined,
                color: Colors.white,
              ),
              onPressed: () {
                setState(() {
                  _obscureConfirmPassword = !_obscureConfirmPassword;
                });
              },
            ),
            fillColor: Colors.transparent,
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.error, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF393939), width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.error, width: 1),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: const Color(0xFF323232),
              ),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'forgot_password_confirm_password_hint'.tr;
            }
            if (value != _passwordController.text) {
              return 'personal_info_password_not_match'.tr;
            }
            return null;
          },
        ),
      ],
    );
  }

  /// 发送验证码
  Future<void> _sendVerificationCode() async {
    // 验证邮箱
    if (_emailController.text.isEmpty ||
        !GetUtils.isEmail(_emailController.text)) {
      Get.snackbar('error'.tr, 'forgot_password_email_hint'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white);
      return;
    }

    final email = _emailController.text.trim();
    final result = await _userController.sendVerificationCode(email);

    if (result) {
      // 发送成功，开始倒计时
      _countdown.value = 60;
      _startCountdown();
    } else {
      // 发送失败
      Get.snackbar(
        'error'.tr,
        'forgot_password_failed'.tr,
        snackPosition: SnackPosition.TOP,
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        icon: const Icon(Icons.error, color: Colors.red),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 开始倒计时
  void _startCountdown() {
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      _countdown.value--;
      return _countdown.value > 0;
    });
  }

  /// 验证邮箱
  void _verifyEmail() {
    // 隐藏键盘
    FocusScope.of(context).unfocus();

    // 验证表单
    if (_formKey.currentState?.validate() ?? false) {
      // 进入下一步
      setState(() {
        _currentStep = 1;
      });
    }
  }

  /// 重置密码
  Future<void> _resetPassword() async {
    // 隐藏键盘
    FocusScope.of(context).unfocus();

    // 验证表单
    if (_formKey.currentState?.validate() ?? false) {
      final email = _emailController.text.trim();
      final code = _codeController.text.trim();
      final password = _passwordController.text.trim();

      final result = await _userController.resetPassword(email, code, password);

      if (result) {
        // 延迟跳转，让用户看到成功提示
        Future.delayed(const Duration(seconds: 1), () {
          Get.toNamed(Routes.LOGIN);
        });
      } else {
        // 重置失败
        Get.snackbar(
          'forgot_password_error'.tr,
          'forgot_password_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    }
  }
}
