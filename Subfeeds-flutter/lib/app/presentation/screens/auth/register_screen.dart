import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/routes/app_pages.dart';

import 'dart:async';
import 'package:flutter/gestures.dart';

/// 注册页面
class RegisterScreen extends StatefulWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  // 表单Key
  final _formKey = GlobalKey<FormState>();

  // 控制器
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // 是否显示密码
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // 是否同意条款
  bool _agreeToTerms = false;

  // 用户控制器
  late UserController _userController;

  // 添加验证码控制器
  final _verifyCodeController = TextEditingController();

  // 倒计时相关
  final RxInt _countdown = 0.obs;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _userController = Get.find<UserController>();
  }

  @override
  void dispose() {
    // _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _verifyCodeController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  // 开始倒计时
  void _startCountdown() {
    _countdown.value = 60;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown.value > 0) {
        _countdown.value--;
      } else {
        timer.cancel();
      }
    });
  }

  // 获取验证码按钮的文本
  String _getVerifyCodeButtonText() {
    if (_countdown.value > 0) {
      return _countdown.value.toString();
    }
    return 'register_get_code'.tr;
  }

  // 判断验证码按钮是否禁用
  bool _isVerifyCodeButtonDisabled() {
    return _countdown.value > 0 || _emailController.text.isEmpty;
  }

  // 发送验证码
  Future<void> _sendVerifyCode() async {
    if (_countdown.value > 0) {
      return;
    }

    if (_emailController.text.isEmpty) {
      Get.snackbar(
        'error'.tr,
        'please_input_email'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFFD0452F),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      return;
    }

    // if (_nameController.text.isEmpty) {
    //   Get.snackbar(
    //     'error'.tr,
    //     'please_input_name'.tr,
    //     snackPosition: SnackPosition.TOP,
    //     icon: const Icon(Icons.error, color: Colors.red),
    //     backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
    //         ? const Color(0xFF161617)
    //         : const Color(0xFF161617),
    //     colorText: Theme.of(Get.context!).brightness == Brightness.dark
    //         ? Colors.white
    //         : Colors.white,
    //   );
    //   return;
    // }

    if (!GetUtils.isEmail(_emailController.text)) {
      Get.snackbar(
        'error'.tr,
        'please_input_email'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      return;
    }

    final result = await _userController.sendVerifyCodeForRegister(
      _emailController.text.trim(),
      _nameController.text.trim(),
      0, // type: 0 表示注册验证码
      0, // channel: 0
    );

    if (result) {
      _startCountdown();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF3c3c3c)
          : const Color(0xFFf7faff),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF3c3c3c)
            : const Color(0xFFf7faff),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 返回按钮
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () => Get.back(),
                          child: SvgPicture.asset(
                            'assets/feeds/back.svg',
                            height: 14,
                            width: 14,
                            colorFilter: ColorFilter.mode(
                              Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFF72849c)
                                  : const Color(0xFF72849c),
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ],
                    ),
                    // 标题
                    const SizedBox(height: 30),
                    Text(
                      'register_create_account'.tr,
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                fontSize: 20,
                              ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'register_subtitle'.tr,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: const Color(0xFF72849c),
                            fontSize: 16,
                          ),
                    ),

                    const SizedBox(height: 15),

                    // // 姓名输入框
                    // TextFormField(
                    //   controller: _nameController,
                    //   decoration: InputDecoration(
                    //     labelText: 'register_name'.tr,
                    //     hintText: 'register_name_hint'.tr,
                    //     prefixIcon: const Icon(Icons.person_outline),
                    //     border: OutlineInputBorder(
                    //       borderRadius: BorderRadius.circular(12),
                    //     ),
                    //   ),
                    //   validator: (value) {
                    //     if (value == null || value.isEmpty) {
                    //       return 'register_name_hint'.tr;
                    //     }
                    //     return null;
                    //   },
                    // ),

                    const SizedBox(height: 20),

                    // 邮箱输入框
                    TextFormField(
                      controller: _emailController,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? const Color(0xFFFFFFFF)
                                    : const Color(0xFF333333),
                          ),
                      keyboardType: TextInputType.emailAddress,
                      decoration: InputDecoration(
                        labelText: 'register_email'.tr,
                        hintText: 'register_email_hint'.tr,
                        prefixIcon: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: SvgPicture.asset(
                            'assets/icons/email.svg',
                            width: 16,
                            height: 16,
                            colorFilter: ColorFilter.mode(
                              Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFF72849c)
                                  : const Color(0xFF72849c),
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                        prefixIconConstraints: const BoxConstraints(
                          minWidth: 48,
                          maxHeight: 48,
                        ),
                        fillColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFF5b5b5b)
                                : const Color(0xFFE5E9F1),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'register_email_hint'.tr;
                        }
                        if (!GetUtils.isEmail(value)) {
                          return 'register_email_hint'.tr;
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // 密码输入框
                    TextFormField(
                      controller: _passwordController,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? const Color(0xFFFFFFFF)
                                    : const Color(0xFF333333),
                          ),
                      obscureText: _obscurePassword,
                      decoration: InputDecoration(
                        labelText: 'register_password'.tr,
                        hintText: 'register_password_hint'.tr,
                        prefixIcon: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: SvgPicture.asset(
                            'assets/icons/key.svg',
                            width: 20,
                            height: 20,
                            colorFilter: ColorFilter.mode(
                              Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFF72849c)
                                  : const Color(0xFF72849c),
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                        fillColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFF5b5b5b)
                                : const Color(0xFFE5E9F1),
                        prefixIconConstraints: const BoxConstraints(
                          minWidth: 48,
                          maxHeight: 48,
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscurePassword
                                ? Icons.visibility_outlined
                                : Icons.visibility_off_outlined,
                            color: const Color(0xFF72849c),
                          ),
                          onPressed: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'register_password_hint'.tr;
                        }
                        if (value.length < 8) {
                          return 'personal_info_password_rule'.tr;
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // 确认密码输入框
                    TextFormField(
                      controller: _confirmPasswordController,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? const Color(0xFFFFFFFF)
                                    : const Color(0xFF333333),
                          ),
                      obscureText: _obscureConfirmPassword,
                      decoration: InputDecoration(
                        labelText: 'register_confirm_password'.tr,
                        hintText: 'register_confirm_password_hint'.tr,
                        prefixIcon: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: SvgPicture.asset(
                            'assets/icons/key.svg',
                            width: 20,
                            height: 20,
                            colorFilter: ColorFilter.mode(
                              Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFF72849c)
                                  : const Color(0xFF72849c),
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                        fillColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFF5b5b5b)
                                : const Color(0xFFE5E9F1),
                        prefixIconConstraints: const BoxConstraints(
                          minWidth: 48,
                          maxHeight: 48,
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscureConfirmPassword
                                ? Icons.visibility_outlined
                                : Icons.visibility_off_outlined,
                            color: const Color(0xFF72849c),
                          ),
                          onPressed: () {
                            setState(() {
                              _obscureConfirmPassword =
                                  !_obscureConfirmPassword;
                            });
                          },
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'register_confirm_password_hint'.tr;
                        }
                        if (value != _passwordController.text) {
                          return 'personal_info_password_not_match'.tr;
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // 验证码输入框和发送按钮
                    TextFormField(
                      controller: _verifyCodeController,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? const Color(0xFFFFFFFF)
                                    : const Color(0xFF333333),
                          ),
                      decoration: InputDecoration(
                        labelText: 'register_verify_code'.tr,
                        hintText: 'register_verify_code_hint'.tr,
                        prefixIcon: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: SvgPicture.asset(
                            'assets/icons/code.svg',
                            width: 20,
                            height: 20,
                            colorFilter: ColorFilter.mode(
                              Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFF72849c)
                                  : const Color(0xFF72849c),
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                        prefixIconConstraints: const BoxConstraints(
                          minWidth: 48,
                          maxHeight: 48,
                        ),
                        suffixIcon: Obx(() => Padding(
                              padding: const EdgeInsets.only(
                                  right: 8.0, top: 4, bottom: 4),
                              child: TextButton(
                                onPressed: _isVerifyCodeButtonDisabled()
                                    ? null
                                    : _sendVerifyCode,
                                style: TextButton.styleFrom(
                                  foregroundColor: Colors.white,
                                  backgroundColor: _isVerifyCodeButtonDisabled()
                                      ? Theme.of(context)
                                          .colorScheme
                                          .primary
                                          .withAlpha(100)
                                      : Theme.of(context).colorScheme.primary,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12),
                                  minimumSize: const Size(100, 35),
                                  maximumSize: const Size(100, 35),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: Text(
                                  _getVerifyCodeButtonText(),
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.copyWith(
                                        fontSize: 12,
                                        color: Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? const Color(0xFFFFFFFF)
                                            : const Color(0xFFFFFFFF),
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                              ),
                            )),
                        fillColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFF5b5b5b)
                                : const Color(0xFFE5E9F1),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'register_verify_code_hint'.tr;
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 10),

                    // 同意条款
                    Row(
                      children: [
                        Checkbox(
                          value: _agreeToTerms,
                          checkColor: Colors.white,
                          activeColor: Theme.of(context).primaryColor,
                          onChanged: (value) {
                            setState(() {
                              _agreeToTerms = value ?? false;
                            });
                          },
                        ),
                        Expanded(
                          child: RichText(
                            text: TextSpan(
                              text: '${'register_terms_agree'.tr} ',
                              style: TextStyle(color: Colors.grey[700]),
                              children: [
                                TextSpan(
                                  text: 'register_terms'.tr,
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () =>
                                        Get.toNamed(Routes.TERMS_OF_SERVICE),
                                ),
                                TextSpan(
                                  text: ' ${'register_and'.tr} ',
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                TextSpan(
                                  text: 'register_privacy'.tr,
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () =>
                                        Get.toNamed(Routes.PRIVACY_POLICY),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 10),

                    // 注册按钮
                    Obx(() => ElevatedButton(
                          onPressed: (_userController.isLoading.value ||
                                  !_agreeToTerms)
                              ? null
                              : _handleRegister,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            minimumSize: const Size(double.infinity, 56),
                            disabledBackgroundColor: _agreeToTerms
                                ? null
                                : Theme.of(context)
                                    .colorScheme
                                    .primary
                                    .withAlpha(100),
                          ),
                          child: _userController.isLoading.value
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : Text(
                                  'register_button'.tr,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.copyWith(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? const Color(0xFFFFFFFF)
                                              : const Color(0xFFFFFFFF)),
                                ),
                        )),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 处理注册
  Future<void> _handleRegister() async {
    // 隐藏键盘
    FocusScope.of(context).unfocus();
    // 验证表单
    if (_formKey.currentState?.validate() ?? false) {
      final email = _emailController.text.trim();
      final password = _passwordController.text.trim();
      final verifyCode = _verifyCodeController.text.trim();
      final result =
          await _userController.register(email, password, verifyCode);
      if (!result) {
        // 注册失败，显示错误信息
        Get.snackbar(
          'register_error'.tr,
          _userController.errorMessage.value.isEmpty
              ? 'register_failed'.tr
              : _userController.errorMessage.value,
          snackPosition: SnackPosition.TOP,
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFFD0452F),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        // 注册成功，自动登录
        final loginResult = await _userController.login(
          email,
          password,
          false, // 不记住密码
          0, // 使用密码登录
        );

        if (!loginResult) {
          // 自动登录失败，跳转到登录页面
          Get.offAllNamed(Routes.LOGIN);
          Get.snackbar(
            'error'.tr,
            _userController.errorMessage.value.isEmpty
                ? 'login_failed'.tr
                : _userController.errorMessage.value,
            snackPosition: SnackPosition.TOP,
            icon: const Icon(Icons.error, color: Colors.red),
            backgroundColor:
                Theme.of(Get.context!).brightness == Brightness.dark
                    ? const Color(0xFF161617)
                    : const Color(0xFF161617),
            colorText: Theme.of(Get.context!).brightness == Brightness.dark
                ? Colors.white
                : Colors.white,
          );
        } else {
          // 自动登录成功，跳转到首页
          Get.offAllNamed(Routes.HOME);
        }
      }
    }
  }
}
