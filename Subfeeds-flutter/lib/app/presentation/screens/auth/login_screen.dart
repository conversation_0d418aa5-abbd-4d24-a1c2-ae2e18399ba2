import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/controllers/user_controller.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:form_field_validator/form_field_validator.dart';

/// 登录页面
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  // 表单Key
  final _formKey = GlobalKey<FormState>();

  // 控制器
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _verificationCodeController = TextEditingController();

  // 是否记住密码
  bool _rememberMe = false;

  // 是否显示密码
  bool _obscurePassword = true;

  // 是否使用验证码登录
  bool _isVerificationCodeLogin = false;

  // 用户控制器
  late UserController _userController;

  // 倒计时相关
  final RxInt _countdown = 0.obs;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _userController = Get.find<UserController>();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _verificationCodeController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  // 开始倒计时
  void _startCountdown() {
    _countdown.value = 60;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown.value > 0) {
        _countdown.value--;
      } else {
        timer.cancel();
      }
    });
  }

  // 获取验证码按钮的文本
  String _getVerifyCodeButtonText() {
    if (_countdown.value > 0) {
      return _countdown.value.toString();
    }
    return 'forgot_password_get_code'.tr;
  }

  // 判断验证码按钮是否禁用
  bool _isVerifyCodeButtonDisabled() {
    return _countdown.value > 0 || _emailController.text.isEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(
      //   title: Text('login_title'.tr),
      //   backgroundColor: Colors.transparent,
      //   centerTitle: true,
      // ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF3c3c3c)
            : const Color(0xFFf7faff),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(18.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 返回按钮
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () => Get.back(),
                          child: SvgPicture.asset(
                            'assets/feeds/back.svg',
                            height: 14,
                            width: 14,
                            colorFilter: ColorFilter.mode(
                              Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFF72849c)
                                  : const Color(0xFF72849c),
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ],
                    ),
                    // 标题
                    const SizedBox(height: 30),
                    Text(
                      'login_welcome_back'.tr,
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                fontSize: 20,
                              ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'login_subtitle'.tr,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: const Color(0xFF72849c),
                            fontSize: 16,
                          ),
                    ),

                    const SizedBox(height: 40),

                    // 邮箱输入框
                    TextFormField(
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? const Color(0xFFFFFFFF)
                                    : const Color(0xFF333333),
                          ),
                      decoration: InputDecoration(
                        labelText: 'login_email'.tr,
                        hintText: 'login_email_hint'.tr,
                        fillColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFF5b5b5b)
                                : const Color(0xFFe5e9f1),
                        labelStyle:
                            Theme.of(context).textTheme.bodyLarge?.copyWith(
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? const Color(0xFFFFFFFF)
                                      : const Color(0xFF333333),
                                ),
                        hintStyle:
                            Theme.of(context).textTheme.bodyLarge?.copyWith(
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? const Color(0xFFD1D1D1)
                                      : const Color(0xFFBCC2CC),
                                ),
                        prefixIcon: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: SvgPicture.asset(
                            'assets/icons/email.svg',
                            width: 16,
                            height: 16,
                            colorFilter: ColorFilter.mode(
                              const Color(0xFF72849c),
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                        prefixIconConstraints: const BoxConstraints(
                          minWidth: 48,
                          maxHeight: 48,
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'login_email'.tr;
                        }
                        if (!GetUtils.isEmail(value)) {
                          return 'login_email_hint'.tr;
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // 密码输入框
                    if (!_isVerificationCodeLogin)
                      TextFormField(
                        controller: _passwordController,
                        obscureText: _obscurePassword,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFFFFFFFF)
                                  : const Color(0xFF333333),
                            ),
                        decoration: InputDecoration(
                          labelText: 'login_password'.tr,
                          hintText: 'login_password_hint'.tr,
                          labelStyle:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? const Color(0xFFFFFFFF)
                                        : const Color(0xFF333333),
                                  ),
                          hintStyle:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? const Color(0xFFD1D1D1)
                                        : const Color(0xFFBCC2CC),
                                  ),
                          prefixIcon: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0),
                            child: SvgPicture.asset(
                              'assets/icons/key.svg',
                              width: 20,
                              height: 20,
                              colorFilter: ColorFilter.mode(
                                const Color(0xFF72849c),
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                          prefixIconConstraints: const BoxConstraints(
                            minWidth: 48,
                            maxHeight: 48,
                          ),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword
                                  ? Icons.visibility_outlined
                                  : Icons.visibility_off_outlined,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFFD1D1D1)
                                  : const Color(0xFFBCC2CC),
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                          fillColor:
                              Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFF5b5b5b)
                                  : const Color(0xFFE5E9F1),
                          errorBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                        ),
                        validator: (value) {
                          if (!_isVerificationCodeLogin &&
                              (value == null || value.isEmpty)) {
                            return 'login_password_hint'.tr;
                          }
                          return null;
                        },
                      ),

                    // 验证码输入框
                    if (_isVerificationCodeLogin)
                      TextFormField(
                        controller: _verificationCodeController,
                        keyboardType: TextInputType.number,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(0xFFFFFFFF)
                                  : const Color(0xFF333333),
                            ),
                        decoration: InputDecoration(
                          labelText: 'register_verify_code'.tr,
                          hintText: 'register_verify_code_hint'.tr,
                          labelStyle:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? const Color(0xFFFFFFFF)
                                        : const Color(0xFF333333),
                                  ),
                          hintStyle:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? const Color(0xFFD1D1D1)
                                        : const Color(0xFFBCC2CC),
                                  ),
                          prefixIcon: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0),
                            child: SvgPicture.asset(
                              'assets/icons/code.svg',
                              width: 20,
                              height: 20,
                              colorFilter: ColorFilter.mode(
                                const Color(0xFF72849c),
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                          prefixIconConstraints: const BoxConstraints(
                            minWidth: 48,
                            maxHeight: 48,
                          ),
                          suffixIcon: Padding(
                            padding:
                                EdgeInsets.only(right: 5, top: 4, bottom: 4),
                            child: Obx(() => TextButton(
                                  onPressed: _isVerifyCodeButtonDisabled()
                                      ? null
                                      : _handleSendVerificationCode,
                                  style: TextButton.styleFrom(
                                    foregroundColor:
                                        _isVerifyCodeButtonDisabled()
                                            ? Colors.grey
                                            : Theme.of(context)
                                                .colorScheme
                                                .primary,
                                    backgroundColor:
                                        _isVerifyCodeButtonDisabled()
                                            ? Theme.of(context)
                                                .colorScheme
                                                .primary
                                                .withAlpha(100)
                                            : Theme.of(context)
                                                .colorScheme
                                                .primary,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8),
                                    minimumSize: const Size(100, 45),
                                    maximumSize: const Size(100, 45),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  child: Text(
                                    _getVerifyCodeButtonText(),
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                )),
                          ),
                          suffixIconConstraints: BoxConstraints(
                            maxHeight: 50,
                          ),
                          fillColor:
                              Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFF5b5b5b)
                                  : const Color(0xFFE5E9F1),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                        ),
                        validator: (value) {
                          if (_isVerificationCodeLogin &&
                              (value == null || value.isEmpty)) {
                            return 'forgot_password_verify_code_hint'.tr;
                          }
                          return null;
                        },
                      ),

                    const SizedBox(height: 0),
                    if (!_isVerificationCodeLogin)
                      // 记住密码和忘记密码
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // 忘记密码
                          TextButton(
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.zero,
                            ),
                            onPressed: () =>
                                Get.toNamed(Routes.FORGOT_PASSWORD),
                            child: Text(
                              'login_forgot_password'.tr,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w900,
                                  ),
                            ),
                          ),
                        ],
                      ),

                    const SizedBox(height: 16),

                    // 登录按钮
                    Obx(() => ElevatedButton(
                          onPressed: _userController.isLoading.value
                              ? null
                              : _handleLogin,
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            minimumSize: const Size(double.infinity, 56),
                          ),
                          child: _userController.isLoading.value
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : Text(
                                  'login_button'.tr,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        )),

                    const SizedBox(height: 0),

                    // 登录方式切换按钮
                    TextButton(
                      onPressed: () {
                        setState(() {
                          _isVerificationCodeLogin = !_isVerificationCodeLogin;
                        });
                      },
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.zero,
                        minimumSize: const Size(double.infinity, 56),
                      ),
                      child: Text(
                        _isVerificationCodeLogin
                            ? 'login_with_password'.tr
                            : 'login_with_verification_code'.tr,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Color(0XFF72849c),
                              fontSize: 14,
                              fontWeight: FontWeight.w900,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 处理登录
  Future<void> _handleLogin() async {
    // 隐藏键盘
    FocusScope.of(context).unfocus();
    final int type;
    // 验证表单
    if (_formKey.currentState?.validate() ?? false) {
      final email = _emailController.text.trim();

      bool result;
      if (_isVerificationCodeLogin) {
        final verificationCode = _verificationCodeController.text.trim();
        type = 1;
        result = await _userController.login(
            email, verificationCode, _rememberMe, type);
      } else {
        type = 0;
        final password = _passwordController.text.trim();
        result =
            await _userController.login(email, password, _rememberMe, type);
      }

      if (!result) {
        debugPrint('登录失败: ${result}');
        // 登录失败，显示错误信息
        Get.closeAllSnackbars(); // 关闭所有已存在的 snackbar
        Get.snackbar(
          'error'.tr,
          _userController.errorMessage.value.isEmpty
              ? 'login_failed'.tr
              : _userController.errorMessage.value,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        // 登录成功，跳转到首页
        Get.offAllNamed(Routes.HOME);
      }
    }
  }

  /// 处理发送验证码
  Future<void> _handleSendVerificationCode() async {
    final email = _emailController.text.trim();

    if (email.isEmpty || !GetUtils.isEmail(email)) {
      Get.closeAllSnackbars();
      Get.snackbar(
        'error'.tr,
        'invalid_email'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      return;
    }

    // 发送验证码请求，type值为4
    final result = await _userController.sendVerifyCode(email, 4, 0);

    // 如果发送成功，开始倒计时
    if (result) {
      _startCountdown();
    }
  }

  /// 处理Google登录
  Future<void> _handleGoogleLogin() async {
    final result = await _userController.signInWithGoogle();
    if (result != null && result is bool && result) {
      // 登录成功，不需要任何提示，controller会处理导航
      return;
    }
    // 其他所有情况都静默返回
    debugPrint('Google登录未完成');
    return;
  }

  /// 处理Apple登录
  Future<void> _handleAppleLogin() async {
    final result = await _userController.signInWithApple();

    if (!result) {
      // 登录失败，显示错误
      Get.snackbar(
        'error'.tr,
        'login_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    } else {
      // 登录成功，跳转到首页
      Get.offAllNamed(Routes.HOME);
    }
  }
}
