import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/data/repositories/article_repository.dart';
import 'dart:async';
import 'package:subfeeds/app/translations/app_translations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/presentation/screens/article/article_controller.dart';

/// 订阅源文章列表控制器
class FeedsArticlesController extends GetxController {
  final ArticleRepository _articleRepository = ArticleRepository();

  // 文章列表
  final articles = <Map<String, dynamic>>[].obs;

  // 订阅源信息
  final feedsInfo = Rx<Map<String, dynamic>>({});

  // 页面状态
  final isLoading = false.obs;
  final isRefreshing = false.obs;
  final hasError = false.obs;
  final errorMessage = ''.obs;
  final hasMore = true.obs;
  final total = 0.obs;

  // 分页参数
  int _currentPage = 1;
  final int _pageSize = 15;

  // 是否只显示未读文章
  final isUnreadOnly = false.obs;

  // 日期筛选参数 (0: 本周, 1: 上周, 2: 过去两周, 3: 过去一个月)
  final dateFilter = 0.obs;

  // 是否处于全部文章模式
  final isAllArticlesMode = false.obs;

  // 滚动控制器
  final ScrollController scrollController = ScrollController();

  // 添加防抖计时器
  Timer? _debounceTimer;
  static const _debounceDuration = Duration(milliseconds: 500);

  // 添加已读文章ID集合
  final Set<int> _readArticleIds = {};

  // 文章状态变更流的订阅
  StreamSubscription? _articleStatusSubscription;

  @override
  void onInit() {
    super.onInit();

    // 从路由参数获取订阅源信息
    if (Get.arguments != null && Get.arguments['feed'] != null) {
      feedsInfo.value = Map<String, dynamic>.from(Get.arguments['feed']);

      // 加载文章列表
      _loadArticles();
    } else {
      hasError.value = true;
      errorMessage.value = '未获取到订阅源信息';
    }

    // 添加滚动监听
    scrollController.addListener(_onScroll);

    // 监听文章状态变更事件
    _articleStatusSubscription = ArticleController.articleStatusChanged.stream
        .listen(_handleArticleStatusChanged);
  }

  @override
  void onClose() {
    scrollController.dispose();
    _debounceTimer?.cancel();
    _articleStatusSubscription?.cancel();
    super.onClose();
  }

  // 处理文章状态变更事件
  void _handleArticleStatusChanged(Map<String, dynamic> data) {
    if (data.containsKey('id')) {
      final String articleId = data['id'];

      // 更新文章列表中的状态
      final index = articles
          .indexWhere((article) => article['id'].toString() == articleId);

      if (index != -1) {
        // 创建文章的副本
        final updatedArticle = Map<String, dynamic>.from(articles[index]);

        // 更新收藏状态
        if (data.containsKey('isCollect')) {
          updatedArticle['isCollect'] = data['isCollect'];
        }

        // 更新稍后阅读状态
        if (data.containsKey('isLaterRead')) {
          updatedArticle['isLaterRead'] = data['isLaterRead'];
        }

        // 更新文章列表
        articles[index] = updatedArticle;
      }
    }
  }

  /// 滚动监听事件
  void _onScroll() {
    if (!scrollController.hasClients) return;

    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.offset;

    // 当滚动到底部90%位置时，加载更多
    if (currentScroll >= maxScroll * 0.9 && !isLoading.value && hasMore.value) {
      if (isAllArticlesMode.value) {
        _loadAllArticles();
      } else {
        _loadArticles();
      }
    }
  }

  /// 加载文章列表
  Future<void> _loadArticles() async {
    if (isLoading.value) return;

    isLoading.value = true;

    try {
      // 获取Feed ID - 需要使用feedsId而不是id
      final feedId = int.tryParse(feedsInfo.value['feedsId'].toString()) ?? -1;
      if (feedId == -1) {
        throw Exception('无效的订阅源ID');
      }

      debugPrint(
          '使用feedsId: $feedId 获取文章列表，状态过滤: ${isUnreadOnly.value ? 0 : 2}, 日期过滤: ${dateFilter.value}');

      // 调用API获取文章列表
      final response = await _articleRepository.getRssArticle(
        feedId,
        isUnreadOnly.value ? 0 : 2, // 2表示全部文章，0表示未读文章
        dateFilter.value, // 日期筛选参数
        _pageSize, // 每页数量
        _currentPage, // 当前页码
      );

      if (response.isSuccess && response.data != null) {
        final responseData = response.data;

        final data = responseData?['data'];
        final pageList = data?['pageList'] as List? ?? [];
        final total = data?['total'] as int? ?? 0;

        // 更新数据
        if (_currentPage == 1) {
          articles.clear();
        }

        articles.addAll(pageList.cast<Map<String, dynamic>>());
        this.total.value = total;
        _currentPage++;
        hasMore.value = articles.length < total;

        // 重置错误状态
        hasError.value = false;
        errorMessage.value = '';
      } else {
        if (_currentPage == 1) {
          hasError.value = true;
          errorMessage.value = response.msg ?? '加载失败';
        }
      }
    } catch (e) {
      debugPrint('加载文章失败: $e');
      if (_currentPage == 1) {
        hasError.value = true;
        errorMessage.value = '加载失败: $e';
      }
    } finally {
      isLoading.value = false;
    }
  }

  /// 刷新文章列表
  Future<void> refreshArticles() async {
    if (isRefreshing.value) return;

    _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDuration, () async {
      isRefreshing.value = true;
      _currentPage = 1;
      hasMore.value = true;

      if (isAllArticlesMode.value) {
        await _loadAllArticles();
      } else {
        await _loadArticles();
      }

      isRefreshing.value = false;
    });
  }

  /// 设置日期筛选并刷新文章
  Future<void> setDateFilter(int filter) async {
    if (dateFilter.value == filter && !isAllArticlesMode.value) return;

    // 切换回正常模式
    isAllArticlesMode.value = false;
    dateFilter.value = filter;
    _currentPage = 1;
    hasMore.value = true;
    await _loadArticles();
  }

  /// 加载全部文章
  Future<void> loadAllArticles() async {
    if (isAllArticlesMode.value) return;

    isAllArticlesMode.value = true;
    _currentPage = 1;
    hasMore.value = true;
    await _loadAllArticles();
  }

  /// 加载全部文章的实现
  Future<void> _loadAllArticles() async {
    if (isLoading.value) return;

    isLoading.value = true;

    try {
      // 获取Feed ID
      final feedId = int.tryParse(feedsInfo.value['feedsId'].toString()) ?? -1;
      if (feedId == -1) {
        throw Exception('无效的订阅源ID');
      }

      debugPrint('使用feedsId: $feedId 获取全部文章，页码：$_currentPage');

      // 调用API获取全部文章列表
      final response = await _articleRepository.getAllArticle(
        feedId,
        _pageSize, // 每页数量
        _currentPage, // 当前页码
      );

      if (response.isSuccess && response.data != null) {
        final responseData = response.data;

        final data = responseData?['data'];
        final pageList = data?['pageList'] as List? ?? [];
        final total = data?['total'] as int? ?? 0;

        // 更新数据
        if (_currentPage == 1) {
          articles.clear();
        }

        articles.addAll(pageList.cast<Map<String, dynamic>>());
        this.total.value = total;
        _currentPage++;
        hasMore.value = articles.length < total;

        // 重置错误状态
        hasError.value = false;
        errorMessage.value = '';
      } else {
        if (_currentPage == 1) {
          hasError.value = true;
          errorMessage.value = response.msg ?? '加载失败';
        }
      }
    } catch (e) {
      debugPrint('加载全部文章失败: $e');
      if (_currentPage == 1) {
        hasError.value = true;
        errorMessage.value = '加载失败: $e';
      }
    } finally {
      isLoading.value = false;
    }
  }

  /// 标记文章为已读
  Future<void> markArticleAsRead(
      dynamic articleId, String feedsId, String suffixTable) async {
    // 转换 articleId 为 int 类型
    final int id = articleId is String ? int.parse(articleId) : articleId;

    // 如果已经标记为已读，则不重复发送请求
    if (_readArticleIds.contains(id)) return;

    try {
      final response = await _articleRepository.insertHistory(
        id,
        int.tryParse(feedsId) ?? -1,
        suffixTable,
      );
      if (response.isSuccess) {
        _readArticleIds.add(id);

        // 更新文章列表中的已读状态
        final index =
            articles.indexWhere((article) => article['id'] == articleId);
        if (index != -1) {
          articles[index]['isRead'] = 1;
          articles.refresh();
        }
      }
    } catch (e) {
      debugPrint('标记已读失败: $e');
    }
  }

  /// 将所有文章标记为已读
  Future<void> markAllAsRead() async {
    try {
      final feedId = int.tryParse(feedsInfo.value['feedsId'].toString()) ?? -1;
      if (feedId == -1) return;

      debugPrint('使用feedsId: $feedId 标记所有文章为已读');

      // 显示进度对话框
      Get.dialog(
        Dialog(
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.light
              ? const Color(0xFFF7FAFF)
              : const Color(0xFF444444),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text('marking_all_as_read'.tr),
              ],
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 使用updateUserFeedsIsReadStatus接口一次性标记所有文章为已读
      final response =
          await _articleRepository.updateUserFeedsIsReadStatus([feedId]);

      // 关闭对话框
      Get.back();

      if (response.isSuccess) {
        // 更新所有文章的已读状态
        for (var article in articles) {
          final articleId = int.tryParse(article['id'].toString());
          if (articleId != null) {
            _readArticleIds.add(articleId);
            article['isRead'] = 1;
          }
        }

        // 通知UI更新
        articles.refresh();

        Get.snackbar(
          'success'.tr,
          'mark_all_as_read_success'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        Get.snackbar(
          'error'.tr,
          'mark_all_as_read_failed'.tr,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      }
    } catch (e) {
      // 关闭对话框
      Get.back();

      debugPrint('标记全部已读失败: $e');
      Get.snackbar(
        'error'.tr,
        'mark_all_as_read_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 切换未读/全部文章的显示状态
  Future<void> toggleUnreadFilter() async {
    isUnreadOnly.toggle();
    _currentPage = 1;
    hasMore.value = true;
    await _loadArticles();
  }

  /// 显示日期筛选底部菜单
  void showDateFilterBottomSheet() {
    Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: Theme.of(Get.context!).brightness == Brightness.light
              ? const Color(0xFFF7FAFF)
              : const Color(0xFF444444),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'filter_by_date'.tr,
                        style: Theme.of(Get.context!)
                            .textTheme
                            .titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                      ),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: Icon(
                          Icons.close,
                          color: Colors.grey[600],
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // 选项列表
                _buildDateFilterOption(0, 'filter_current_week'),
                _buildDateFilterOption(1, 'filter_last_week'),
                _buildDateFilterOption(2, 'filter_two_weeks'),
                _buildDateFilterOption(3, 'filter_one_month'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建日期筛选选项
  Widget _buildDateFilterOption(int value, String titleKey) {
    return Obx(() => InkWell(
          onTap: () {
            setDateFilter(value);
            Get.back();
          },
          child: Container(
            width: double.infinity,
            padding:
                const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
            color: dateFilter.value == value
                ? Theme.of(Get.context!).brightness == Brightness.light
                    ? const Color(0xFFFFFFFF)
                    : const Color(0xFF3b3b3b)
                : Colors.transparent,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  titleKey.tr,
                  style: Theme.of(Get.context!).textTheme.bodyMedium,
                ),
                if (dateFilter.value == value)
                  Icon(
                    Icons.check,
                    color: Theme.of(Get.context!).colorScheme.primary,
                    size: 20,
                  ),
              ],
            ),
          ),
        ));
  }
}
