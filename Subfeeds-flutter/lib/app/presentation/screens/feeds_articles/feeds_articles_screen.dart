import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/feeds_articles/feeds_articles_controller.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:subfeeds/app/data/repositories/article_repository.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/new_widget/article_item.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/presentation/screens/home/<USER>/new_widget/news_skeleton.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/share_bottom_sheet.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 订阅源文章列表页面
class FeedsArticlesScreen extends GetView<FeedsArticlesController> {
  FeedsArticlesScreen({Key? key}) : super(key: key);

  // 添加一个文章仓库实例作为私有属性
  final ArticleRepository _articleRepository = ArticleRepository();

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDarkMode ? const Color(0xFF1e2020) : const Color(0xffeef2f9);
    return Scaffold(
      backgroundColor: backgroundColor,
      // 添加底部导航栏
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, -1),
            ),
          ],
          border: Border(
            top: BorderSide(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Color(0xff393d4c)
                  : Color(0xffe3e7ff),
              width: 1,
            ),
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 左侧操作按钮（从顶部移动过来的）
                // 将左侧三个按钮包裹在一个容器中
                Container(
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 文章日期选择
                      Obx(() => Container(
                            margin: const EdgeInsets.symmetric(horizontal: 2),
                            width: 38,
                            height: 28,
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: IconButton(
                                icon: SvgPicture.asset(
                                  'assets/icons/date.svg',
                                  width: 16,
                                  height: 16,
                                  colorFilter: ColorFilter.mode(
                                    controller.isUnreadOnly.value
                                        ? Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.white
                                            : const Color(0xFF333333)
                                        : Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.white
                                            : const Color(0xFF333333),
                                    BlendMode.srcIn,
                                  ),
                                ),
                                onPressed: () {
                                  controller.showDateFilterBottomSheet();
                                },
                                padding: EdgeInsets.zero,
                              ),
                            ),
                          )),
                      Obx(() => Container(
                            margin: const EdgeInsets.symmetric(horizontal: 1),
                            width: 38,
                            height: 28,
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: IconButton(
                                icon: SvgPicture.asset(
                                  'assets/icons/filter-dot.svg',
                                  width: 16,
                                  height: 16,
                                  colorFilter: ColorFilter.mode(
                                    controller.isUnreadOnly.value
                                        ? Theme.of(context).colorScheme.primary
                                        : Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.white
                                            : const Color(0xFF333333),
                                    BlendMode.srcIn,
                                  ),
                                ),
                                onPressed: controller.toggleUnreadFilter,
                                padding: EdgeInsets.zero,
                              ),
                            ),
                          )),

                      // 刷新按钮
                      Obx(() => Container(
                            width: 38,
                            height: 28,
                            margin: const EdgeInsets.only(left: 2),
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: controller.isRefreshing.value
                                ? const Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Material(
                                    color: Colors.transparent,
                                    child: IconButton(
                                      icon: SvgPicture.asset(
                                        'assets/icons/refresh.svg',
                                        width: 18,
                                        height: 18,
                                        colorFilter: ColorFilter.mode(
                                          Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Colors.white
                                              : const Color(0xFF333333),
                                          BlendMode.srcIn,
                                        ),
                                      ),
                                      onPressed: controller.refreshArticles,
                                      padding: EdgeInsets.zero,
                                    ),
                                  ),
                          )),
                    ],
                  ),
                ),

                Spacer(), // 用于将右侧按钮推到最右边
                // 标记全部已读按钮
                Container(
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(9.5),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: IconButton(
                        icon: SvgPicture.asset(
                          'assets/icons/marked-all.svg',
                          colorFilter: ColorFilter.mode(
                            Theme.of(context).primaryColor,
                            BlendMode.srcIn,
                          ),
                        ),
                        onPressed: () => _showMarkAllAsReadDialog(context),
                        padding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ),

                // 这里可以添加右侧功能按钮，如果需要的话
              ],
            ),
          ),
        ),
      ),
      body: Container(
        color: backgroundColor,
        child: SafeArea(
          child: Column(
            children: [
              // 自定义 AppBar
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 16),
                child: Stack(
                  children: [
                    // 返回按钮
                    Align(
                      alignment: Alignment.centerLeft,
                      child: InkWell(
                        onTap: () => Get.back(),
                        borderRadius: BorderRadius.circular(16),
                        child: Container(
                          width: 25,
                          height: 25,
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.arrow_back,
                            size: 25,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white
                                    : Colors.black,
                          ),
                        ),
                      ),
                    ),

                    // 使用Row替换Align，并添加左右padding确保不会遮盖返回按钮
                    Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 40.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                              child: Align(
                                child: Text(
                                  controller.feedsInfo.value['feedsName'] ??
                                      "SubFeeds",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                  maxLines: 1,
                                  textAlign: TextAlign.center,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                            // Padding(
                            //   padding: EdgeInsets.symmetric(horizontal: 2),
                            //   child: Container(
                            //     padding: const EdgeInsets.symmetric(
                            //         horizontal: 16, vertical: 2),
                            //     decoration: BoxDecoration(
                            //       color: Theme.of(context).colorScheme.primary,
                            //       borderRadius: BorderRadius.circular(12),
                            //     ),
                            //     child: Text(
                            //       '${controller.feedsInfo.value['unreadCount'] ?? 0}',
                            //       style: TextStyle(
                            //         color: Colors.white,
                            //         fontWeight: FontWeight.bold,
                            //         fontSize: 12,
                            //       ),
                            //     ),
                            //   ),
                            // ),
                          ],
                        )),
                    //展示未读数量
                  ],
                ),
              ),
              SizedBox(height: 6),

              ///分隔线
              Divider(
                height: 1,
                thickness: 0.5,
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF434756)
                    : Color(0xffdfe3ff),
              ),
              // Feeds信息头部
              // Padding(
              //   padding:
              //       const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              //   child: _buildFeedsHeader(context),
              // ),

              // 文章列表
              Expanded(
                child: Obx(() {
                  if (controller.isLoading.value &&
                      controller.articles.isEmpty) {
                    return _buildSkeletonLoading(context);
                  }

                  if (controller.hasError.value &&
                      controller.articles.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.error_outline,
                              size: 48, color: Colors.red),
                          const SizedBox(height: 16),
                          Text(controller.errorMessage.value),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: controller.refreshArticles,
                            child: Text('retry'.tr),
                          ),
                        ],
                      ),
                    );
                  }

                  if (controller.articles.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/article_empty.png',
                            width: 120.spx,
                            height: 120.spx,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'read_all_articles_weeks'.tr,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      fontSize: 16,
                                    ),
                          ),
                          const SizedBox(height: 16),
                          TextButton(
                            onPressed: () {
                              controller.loadAllArticles();
                            },
                            style: TextButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              foregroundColor: Colors.white,
                            ),
                            child: Text(
                              'check_all_articles'.tr,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return Container(
                    margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                    ),
                    child: _buildArticlesList(context),
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建Feeds头部信息
  Widget _buildFeedsHeader(BuildContext context) {
    return Obx(() {
      final feed = controller.feedsInfo.value;
      final rssFeeds = feed['rssFeeds'] as Map<String, dynamic>? ?? {};

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
        decoration: BoxDecoration(
          // color: Theme.of(context).brightness == Brightness.dark
          //     ? const Color(0xFF25293b)
          //     : Colors.white,
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            // Feeds信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    feed['feedsName']?.trim() ?? 'Unknown',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_formatDateTime(feed['createTime'] ?? feed['updateTime'] ?? '')} • ${feed['unreadCount'] ?? 0} ${'unread'.tr}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withOpacity(0.6)
                              : const Color.fromARGB(255, 188, 188, 194),
                        ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Feeds Logo (移动到右侧)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                feed['rssFeeds']['img'] ??
                    'https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${rssFeeds['originUrl'] ?? ''}',
                width: 40,
                height: 40,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 40,
                  height: 40,
                  color: _getSourceColor(feed['feedsName'].trim()),
                  child: Center(
                    child: Text(
                      (feed['feedsName']?.trim() ?? 'F').isNotEmpty
                          ? feed['feedsName']!
                              .trim()
                              .substring(0, 1)
                              .toUpperCase()
                          : 'F',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 根据来源名称生成颜色
  Color _getSourceColor(String? source) {
    if (source == null || source.isEmpty) return Colors.grey;

    final List<Color> colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
      Colors.cyan,
    ];

    return colors[source.hashCode % colors.length];
  }

  /// 格式化时间戳为日期字符串
  String _formatDateTime(dynamic timestamp) {
    if (timestamp == null || timestamp.toString().isEmpty) return '';
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(
        int.parse(timestamp.toString()) * 1000,
      );
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  /// 显示标记全部已读对话框
  void _showMarkAllAsReadDialog(BuildContext context) {
    Get.dialog(
      Dialog(
        backgroundColor: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFFF7FAFF)
            : const Color(0xFF444444),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'mark_all_as_read'.tr,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              SizedBox(height: 16.spx),
              Text(
                'are_you_sure_to_mark_all_as_read'.tr,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14.spx,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Poppins',
                      color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFFD1D1D1)
                          : const Color(0xFF999999),
                    ),
              ),
              SizedBox(height: 16.spx),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        backgroundColor: const Color(0xFFE6E6E6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.spx),
                        ),
                        padding: EdgeInsets.symmetric(
                          vertical: 8.spx,
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: const Color(0xFF999999),
                              fontSize: 14.spx,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Roboto',
                            ),
                      ),
                    ),
                  ),
                  SizedBox(width: 16.spx),
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Get.back();
                        controller.markAllAsRead();
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: EdgeInsets.symmetric(
                          vertical: 8.spx,
                        ),
                      ),
                      child: Text(
                        'confirm'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontSize: 14.spx,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Roboto',
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建骨架屏加载状态
  Widget _buildSkeletonLoading(BuildContext context) {
    final skeletonData = _generateSkeletonItems(8);
    final baseColor = NewsSkeleton.getSkeletonColor(context);

    return Container(
      margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1e2020)
            : const Color(0xffeef2f9),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Skeletonizer(
        enabled: true,
        effect: ShimmerEffect(
          baseColor: baseColor ?? Colors.grey[300]!,
          highlightColor: const Color.fromRGBO(245, 245, 245, 1),
          duration: const Duration(seconds: 1),
        ),
        child: ListView.builder(
          padding: EdgeInsets.symmetric(horizontal: 10.spx),
          itemCount: 8,
          itemBuilder: (context, index) {
            final article = skeletonData[index];
            final feed = {
              'id': article['feedsId'],
              'feedsName': article['feedsName'],
              'img': article['feedsImg'] ?? '',
              'link': article['link'],
            };

            return ArticleItem(
              article: article,
              feed: feed,
              isDarkMode: Theme.of(context).brightness == Brightness.dark,
              onTap: () {},
              isShowLaterRead: false,
              isShowBookmark: false,
              onReadLater: () {},
              onBookmark: () {},
              onShare: () {},
            );
          },
        ),
      ),
    );
  }

  /// 构建文章列表
  Widget _buildArticlesList(BuildContext context) {
    final articles = controller.articles;
    final isLoadingMore = controller.isLoading.value && articles.isNotEmpty;

    // 计算总项目数
    final totalItemCount = articles.length + (isLoadingMore ? 5 : 0);

    return RefreshIndicator(
      onRefresh: () => controller.refreshArticles(),
      child: ListView.builder(
        controller: controller.scrollController,
        padding: EdgeInsets.symmetric(horizontal: 10.spx, vertical: 10.spx),
        itemCount: totalItemCount,
        itemBuilder: (context, index) {
          return _buildListItem(context, index, articles, isLoadingMore);
        },
      ),
    );
  }

  Widget _buildListItem(
    BuildContext context,
    int index,
    RxList<Map<String, dynamic>> articles,
    bool isLoadingMore,
  ) {
    // 显示实际文章
    if (index < articles.length) {
      final article = articles[index];
      return _buildArticleItem(context, article);
    }

    // 显示加载更多的骨架屏
    if (isLoadingMore &&
        index >= articles.length &&
        index < articles.length + 5) {
      return _buildLoadMoreSkeletonItem(context);
    }

    return const SizedBox.shrink();
  }

  Widget _buildArticleItem(BuildContext context, Map<String, dynamic> article) {
    final feed = {
      'id': article['feedsId'],
      'feedsName': controller.feedsInfo.value['feedsName'] ??
          article['creator'] ??
          'SubFeeds',
      'img': controller.feedsInfo.value['rssFeeds']?['img'] ?? '',
      'link': article['link'],
    };

    return ArticleItem(
      article: article,
      feed: feed,
      isDarkMode: Theme.of(context).brightness == Brightness.dark,
      onTap: () => _handleArticleTap(article),
      isShowLaterRead: false,
      isShowBookmark: false,
      onReadLater: () => _handleReadLater(article),
      onBookmark: () => _handleBookmark(article),
      onShare: () => _handleShare(context, article),
    );
  }

  Widget _buildLoadMoreSkeletonItem(BuildContext context) {
    final skeletonArticle = {
      'id': 'skeleton_loading_${DateTime.now().millisecondsSinceEpoch}',
      'title': 'Loading Article Title',
      'description':
          'This is a loading description for the article that will be replaced with real content.',
      'feedsName': 'Loading Feed Name',
      'feedsImg': '',
      'img': '',
      'pubDate': DateTime.now().millisecondsSinceEpoch,
      'createTime': DateTime.now().millisecondsSinceEpoch,
      'creator': 'Loading Author',
      'link': '',
      'isCollect': 0,
      'isLaterRead': 0,
      'isRead': 0,
      'feedsId': 'skeleton_feed',
      'suffixTable': '',
      'isSkeleton': true,
    };

    final feed = {
      'id': skeletonArticle['feedsId'],
      'feedsName': skeletonArticle['feedsName'],
      'img': skeletonArticle['feedsImg'] ?? '',
      'link': skeletonArticle['link'],
    };

    final baseColor = NewsSkeleton.getSkeletonColor(context);

    return Container(
      margin: EdgeInsets.only(bottom: 2.spx),
      child: Skeletonizer(
        enabled: true,
        effect: ShimmerEffect(
          baseColor: baseColor ?? Colors.grey[300]!,
          highlightColor: const Color.fromRGBO(245, 245, 245, 1),
          duration: const Duration(seconds: 1),
        ),
        child: ArticleItem(
          article: skeletonArticle,
          feed: feed,
          isDarkMode: Theme.of(context).brightness == Brightness.dark,
          onTap: () {},
          isShowLaterRead: false,
          isShowBookmark: false,
          onReadLater: () {},
          onBookmark: () {},
          onShare: () {},
        ),
      ),
    );
  }

  /// 生成骨架屏数据
  List<Map<String, dynamic>> _generateSkeletonItems(int count) {
    return List.generate(
      count,
      (index) => {
        'id': 'skeleton_$index',
        'title': 'Loading Article Title ${index + 1}',
        'description':
            'This is a loading description for the article that will be replaced with real content when the data loads from the server.',
        'feedsName': 'Loading Feed Name',
        'feedsImg': '',
        'img': '',
        'pubDate': DateTime.now().millisecondsSinceEpoch,
        'createTime': DateTime.now().millisecondsSinceEpoch,
        'creator': 'Loading Author',
        'link': '',
        'isCollect': 0,
        'isLaterRead': 0,
        'isRead': 0,
        'feedsId': 'skeleton_feed_$index',
        'suffixTable': '',
        'isSkeleton': true,
      },
    );
  }

  /// 处理文章点击
  void _handleArticleTap(Map<String, dynamic> article) async {
    final articleId = article['id'];
    final feedsId = article['feedsId'] ?? '';
    final suffixTable = article['suffixTable'] ?? '';

    // 标记文章为已读
    await controller.markArticleAsRead(
      articleId,
      feedsId,
      suffixTable,
    );

    // 构建文章详情所需的参数
    final articleData = {
      'id': articleId,
      'title': article['title'] ?? '无标题',
      'pubDate': article['pubDate'],
      'createTime': article['createTime'],
      'suffixTable': article['suffixTable'] ?? '',
      'feedsId': controller.feedsInfo.value['feedsId'] ?? '',
      'creator': article['creator'],
      'description': article['description'] ?? article['content'] ?? '暂无内容',
      'link': article['link'] ?? article['url'] ?? '',
      'language': article['language'] ?? 'en',
      'feedsName': controller.feedsInfo.value['feedsName'] ??
          article['creator'] ??
          '未知来源',
      'isCollect': article['isCollect'] ?? 0,
      'isLaterRead': article['isLaterRead'] ?? 0,
      'img': controller.feedsInfo.value['rssFeeds']?['img'],
      'isRead': 1, // 设置为已读
    };

    // 跳转到文章详情页
    Get.toNamed(
      Routes.ARTICLE,
      arguments: {
        'article': articleData,
      },
    );
  }

  /// 处理稍后阅读
  void _handleReadLater(Map<String, dynamic> article) async {
    final int articleId = int.parse(article['id'].toString());
    final bool isCurrentlyLaterRead = article['isLaterRead'] == 1;

    try {
      if (isCurrentlyLaterRead) {
        final response = await _articleRepository.deleteLaterRead([articleId]);
        if (response.isSuccess) {
          article['isLaterRead'] = 0;
          controller.articles.refresh();
          Get.snackbar('success'.tr, 'removed_from_read_later'.tr,
              icon: const Icon(Icons.check, color: Colors.green),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              snackPosition: SnackPosition.TOP);
        } else {
          Get.snackbar('error'.tr, response.msg,
              icon: const Icon(Icons.error, color: Colors.red),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              snackPosition: SnackPosition.TOP);
        }
      } else {
        final response = await _articleRepository.setReadLater(
            articleId,
            int.tryParse(article['feedsId'].toString()) ?? -1,
            article['suffixTable'] ?? '');
        if (response.isSuccess) {
          article['isLaterRead'] = 1;
          controller.articles.refresh();
          Get.snackbar('success'.tr, 'added_to_read_later'.tr,
              icon: const Icon(Icons.check, color: Colors.green),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              snackPosition: SnackPosition.TOP);
        } else {
          Get.snackbar('error'.tr, response.msg,
              icon: const Icon(Icons.error, color: Colors.red),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              snackPosition: SnackPosition.TOP);
        }
      }
    } catch (e) {
      debugPrint('稍后阅读操作失败: $e');
      Get.snackbar('error'.tr, 'operation_failed'.tr,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          snackPosition: SnackPosition.TOP);
    }
  }

  /// 处理收藏
  void _handleBookmark(Map<String, dynamic> article) async {
    final int articleId = int.parse(article['id'].toString());
    final bool isCurrentlyCollect = article['isCollect'] == 1;

    try {
      if (isCurrentlyCollect) {
        final response = await _articleRepository.deleteCollect([articleId]);
        if (response.isSuccess) {
          article['isCollect'] = 0;
          controller.articles.refresh();
          Get.snackbar('success'.tr, 'removed_from_bookmarks'.tr,
              icon: const Icon(Icons.check, color: Colors.green),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              snackPosition: SnackPosition.TOP);
        } else {
          Get.snackbar('error'.tr, response.msg,
              icon: const Icon(Icons.error, color: Colors.red),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              snackPosition: SnackPosition.TOP);
        }
      } else {
        final response = await _articleRepository.insertCollect(
            articleId,
            int.tryParse(article['feedsId'].toString()) ?? -1,
            article['suffixTable'] ?? '');
        if (response.isSuccess) {
          article['isCollect'] = 1;
          controller.articles.refresh();
          Get.snackbar('success'.tr, 'added_to_bookmarks'.tr,
              icon: const Icon(Icons.check, color: Colors.green),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              snackPosition: SnackPosition.TOP);
        } else {
          Get.snackbar('error'.tr, response.msg,
              icon: const Icon(Icons.error, color: Colors.red),
              backgroundColor:
                  Theme.of(Get.context!).brightness == Brightness.dark
                      ? const Color(0xFF161617)
                      : const Color(0xFF161617),
              snackPosition: SnackPosition.TOP);
        }
      }
    } catch (e) {
      debugPrint('收藏操作失败: $e');
      Get.snackbar('error'.tr, 'operation_failed'.tr,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          snackPosition: SnackPosition.TOP);
    }
  }

  /// 处理分享
  void _handleShare(BuildContext context, Map<String, dynamic> article) {
    final shareData = {
      'name': article['title'] ?? 'Article',
      'link': article['link'] ?? '',
      'description': article['description'] ?? '',
    };
    ShareBottomSheet.show(context, shareData);
  }
}
