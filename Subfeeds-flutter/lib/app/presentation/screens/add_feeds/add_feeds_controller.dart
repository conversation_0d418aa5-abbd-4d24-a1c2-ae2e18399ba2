import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:subfeeds/app/data/repositories/feeds_repository.dart' as feeds;

class AddFeedsController extends GetxController {
  final TextEditingController searchController = TextEditingController();
  final RxList<String> searchHistory = <String>[].obs;
  final RxList<Map<String, dynamic>> searchResults =
      <Map<String, dynamic>>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool hasSearched = false.obs;
  final RxBool hasMore = true.obs;
  final RxString currentQuery = ''.obs;
  final RxBool hasSearchText = false.obs;
  final RxInt totalResults = 0.obs;
  final RxBool hasLoadedAll = false.obs;

  int currentPage = 1;
  final int pageSize = 10;
  final GetStorage storage = GetStorage();
  final feeds.ArticleRepository _feedsRepository = feeds.ArticleRepository();

  Timer? _debounceTimer;

  @override
  void onInit() {
    super.onInit();
    _loadSearchHistory();
    searchController.addListener(_onSearchTextChanged);
  }

  @override
  void onClose() {
    searchController.removeListener(_onSearchTextChanged);
    searchController.dispose();
    _debounceTimer?.cancel();
    super.onClose();
  }

  /// 搜索框内容变化监听
  void _onSearchTextChanged() {
    hasSearchText.value = searchController.text.isNotEmpty;
  }

  /// 加载搜索历史记录
  void _loadSearchHistory() {
    final history = storage.read<List>('add_feeds_search_history') ?? [];
    searchHistory.value = history.cast<String>();
  }

  /// 保存搜索历史记录
  void _saveSearchHistory() {
    storage.write('add_feeds_search_history', searchHistory.toList());
  }

  /// 添加搜索历史记录
  void addSearchHistory(String query) {
    if (query.trim().isEmpty) {
      return;
    }

    // 移除已存在的相同查询
    searchHistory.remove(query);
    // 添加到最前面
    searchHistory.insert(0, query);
    // 限制历史记录数量
    if (searchHistory.length > 10) {
      searchHistory.removeRange(10, searchHistory.length);
    }
    _saveSearchHistory();
  }

  /// 清除搜索历史记录
  void clearSearchHistory() {
    searchHistory.clear();
    storage.remove('add_feeds_search_history');
  }

  /// 删除单个历史记录
  void removeSearchHistory(String query) {
    searchHistory.remove(query);
    _saveSearchHistory();
  }

  /// 执行搜索
  void performSearch(String query) {
    final trimmedQuery = query.trim();
    if (trimmedQuery.isEmpty) return;

    currentQuery.value = trimmedQuery;
    currentPage = 1;
    hasMore.value = true;
    hasLoadedAll.value = false;
    searchResults.clear();
    totalResults.value = 0;
    addSearchHistory(trimmedQuery);

    _search();
  }

  Future<void> _search() async {
    if (isLoading.value) return;

    if (currentPage == 1) {
      isLoading.value = true;
      hasSearched.value = true; // 搜索过
    } else {
      isLoadingMore.value = true;
    }

    try {
      // 使用真实的RSS源搜索API（关键词搜索，type=1）
      final response = await _feedsRepository.searchFeeds({
        'search': currentQuery.value,
        'pageNum': currentPage,
        'pageSize': pageSize,
      });

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final List<dynamic> pageList = data['pageList'] as List<dynamic>? ?? [];
        final results =
            pageList.map((item) => item as Map<String, dynamic>).toList();

        if (currentPage == 1) {
          searchResults.assignAll(results);
        } else {
          searchResults.addAll(results);
        }

        totalResults.value = (data['total'] as num?)?.toInt() ?? 0;
        hasMore.value = searchResults.length < totalResults.value;
        hasLoadedAll.value = searchResults.length >= totalResults.value;

        if (results.isNotEmpty) {
          currentPage++;
        }
      } else {
        if (currentPage == 1) {
          searchResults.clear();
          totalResults.value = 0;
        }
        hasMore.value = false;
        hasLoadedAll.value = true;
      }
    } catch (e) {
      debugPrint('搜索失败: $e');
      if (Get.isSnackbarOpen != true) {
        Get.snackbar(
          '提示',
          '搜索失败，请重试',
          duration: const Duration(seconds: 2),
        );
      }
      if (currentPage == 1) {
        searchResults.clear();
        totalResults.value = 0;
      }
      hasMore.value = false;
      hasLoadedAll.value = true;
    } finally {
      isLoading.value = false;
      isLoadingMore.value = false;
    }
  }

  /// 加载更多
  Future<void> loadMore() async {
    if (isLoadingMore.value ||
        !hasMore.value ||
        currentQuery.value.isEmpty ||
        hasLoadedAll.value) {
      return;
    }

    await _search();
  }

  /// 刷新搜索结果
  @override
  Future<void> refresh() async {
    if (currentQuery.value.isEmpty) return;

    currentPage = 1;
    hasLoadedAll.value = false;
    hasMore.value = true;
    await _search();
  }

  /// 订阅RSS源
  Future<void> subscribeFeed(Map<String, dynamic> feed) async {
    try {
      final response = await _feedsRepository.subscribeRss({
        'searchValue': feed['id'],
        'type': feed['type'],
      });

      if (response.isSuccess) {
        // 更新本地状态
        final index =
            searchResults.indexWhere((item) => item['id'] == feed['id']);
        if (index != -1) {
          final updatedFeed = Map<String, dynamic>.from(searchResults[index]);
          updatedFeed['isSub'] = 1;
          searchResults[index] = updatedFeed;
        }
      } else {}
    } catch (e) {
      debugPrint('订阅失败: $e');
      Get.snackbar('错误', '订阅失败，请重试');
    }
  }

  /// 取消订阅RSS源
  Future<void> unsubscribeFeed(Map<String, dynamic> feed) async {
    try {
      final response =
          await _feedsRepository.deleteRss([int.parse(feed['id'])]);

      if (response.isSuccess) {
        final index =
            searchResults.indexWhere((item) => item['id'] == feed['id']);
        if (index != -1) {
          final updatedFeed = Map<String, dynamic>.from(searchResults[index]);
          updatedFeed['isSub'] = 0;
          searchResults[index] = updatedFeed;
        }
      } else {
        Get.snackbar('失败', response.msg);
      }
    } catch (e) {
      debugPrint('取消订阅失败: $e');
      Get.snackbar('错误', '取消订阅失败，请重试');
    }
  }

  /// 重置搜索状态
  void resetSearch() {
    searchController.clear();
    hasSearchText.value = false;
    currentQuery.value = '';
    searchResults.clear();
    hasSearched.value = false;
    isLoading.value = false;
    isLoadingMore.value = false;
    hasMore.value = true;
    hasLoadedAll.value = false;
    totalResults.value = 0;
    currentPage = 1;
  }
}
