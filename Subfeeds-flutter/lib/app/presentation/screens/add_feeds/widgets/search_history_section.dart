import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 搜索历史记录组件
class SearchHistorySection extends StatelessWidget {
  final List<String> searchHistory;
  final Function(String) onHistoryTap;
  final Function(String) onHistoryRemove;
  final VoidCallback onClearAll;

  const SearchHistorySection({
    super.key,
    required this.searchHistory,
    required this.onHistoryTap,
    required this.onHistoryRemove,
    required this.onClearAll,
  });

  @override
  Widget build(BuildContext context) {
    if (searchHistory.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        SizedBox(height: 12.spx),
        _buildHistoryList(context),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'search_history'.tr,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontSize: 14.spx,
                fontWeight: FontWeight.w500,
              ),
        ),
        InkWell(
          onTap: onClearAll,
          borderRadius: BorderRadius.circular(8.spx),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                'assets/feeds/feeds_delete.svg',
                width: 20.spx,
                height: 20.spx,
                colorFilter: ColorFilter.mode(
                  Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFFFFFFFF)
                      : const Color(0xFF333333),
                  BlendMode.srcIn,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String capitalizeFirstLetter(String input) {
    if (input.isEmpty) return input;
    return input[0].toUpperCase() + input.substring(1).toLowerCase();
  }

  Widget _buildHistoryList(BuildContext context) {
    return SizedBox(
      height: 32.spx, // 固定高度，只显示一行
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: searchHistory.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.only(
              right: index < searchHistory.length - 1 ? 8.spx : 0,
            ),
            child: _buildHistoryItem(context, searchHistory[index]),
          );
        },
      ),
    );
  }

  Widget _buildHistoryItem(BuildContext context, String query) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFFE6E6E6)
            : const Color(0xFF555555),
        borderRadius: BorderRadius.circular(11.6.spx),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20.spx),
          onTap: () => onHistoryTap(query),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 11.spx, vertical: 7.spx),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Text(
                    capitalizeFirstLetter(query),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 10.spx,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFFD1D1D1)
                              : const Color(0xFF999999),
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // SizedBox(width: 6.spx),
                // InkWell(
                //   onTap: () => onHistoryRemove(query),
                //   borderRadius: BorderRadius.circular(10.spx),
                //   child: Padding(
                //     padding: EdgeInsets.all(2.spx),
                //     child: Icon(
                //       Icons.close,
                //       size: 14.spx,
                //       color: Theme.of(context).brightness == Brightness.dark
                //           ? const Color(0xFFD1D1D1)
                //           : const Color(0xFF999999),
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(32.spx),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.history,
              size: 48.spx,
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF404040)
                  : const Color(0xFFE0E0E0),
            ),
            SizedBox(height: 16.spx),
            Text(
              'no_search_history'.tr,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 14.spx,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFF7F8EA7)
                        : const Color(0xFF999999),
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
