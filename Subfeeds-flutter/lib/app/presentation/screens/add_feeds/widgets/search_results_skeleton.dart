import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/feed_item.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;

/// RSS源搜索结果骨架屏组件
class SearchResultsSkeleton extends StatelessWidget {
  const SearchResultsSkeleton({super.key});

  static Color? getSkeletonColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color.fromRGBO(97, 97, 97, 1)
        : const Color.fromRGBO(224, 224, 224, 1);
  }

  /// 生成骨架屏数据
  List<Map<String, dynamic>> generateSkeletonItems(int count) {
    return List.generate(
        count,
        (index) => {
              'id': 'skeleton_$index',
              'name': 'RSS Feed Loading Title ${index + 1}',
              'description':
                  'This is a loading description for RSS feed that will be replaced with real content when the data loads from the server.',
              'url': 'https://example.com/feed_$index.xml',
              'website': 'https://example.com',
              'category': 'Technology',
              'type': 'rss',
              'isSub': 0,
              'img': '',
              'createTime': DateTime.now().millisecondsSinceEpoch ~/ 1000,
              'articleList': [
                {
                  'id': 'article_${index}_1',
                  'title': 'Article Title Loading...',
                  'summary': 'Article summary loading placeholder text...',
                  'description':
                      'This is a skeleton article description with images <img src="https://via.placeholder.com/300x200/CCCCCC/969696?text=Loading+1" alt="Loading 1"> and more content <img src="https://via.placeholder.com/300x200/CCCCCC/969696?text=Loading+2" alt="Loading 2"> with additional images <img src="https://via.placeholder.com/300x200/CCCCCC/969696?text=Loading+3" alt="Loading 3"> and final image <img src="https://via.placeholder.com/300x200/CCCCCC/969696?text=Loading+4" alt="Loading 4"> for skeleton display.',
                  'publishTime': DateTime.now().millisecondsSinceEpoch,
                  'img': '',
                  'isSkeleton': true,
                },
                {
                  'id': 'article_${index}_2',
                  'title': 'Another Article Title Loading...',
                  'summary':
                      'Another article summary loading placeholder text...',
                  'description':
                      'Another skeleton article description with sample images <img src="https://via.placeholder.com/300x200/DDDDDD/AAAAAA?text=Sample+1" alt="Sample 1"> and more content <img src="https://via.placeholder.com/300x200/DDDDDD/AAAAAA?text=Sample+2" alt="Sample 2"> for demonstration.',
                  'publishTime': DateTime.now().millisecondsSinceEpoch,
                  'img': '',
                  'isSkeleton': true,
                },
              ],
              'isSkeleton': true, // 标记为骨架屏数据
            });
  }

  @override
  Widget build(BuildContext context) {
    final skeletonData = generateSkeletonItems(8);
    final baseColor = getSkeletonColor(context);

    // 创建一个临时的SearchController实例用于骨架屏
    final tempController = app_search.SearchController();

    return Skeletonizer(
      enabled: true,
      effect: ShimmerEffect(
        baseColor: baseColor ?? Colors.grey[300]!,
        highlightColor: const Color.fromRGBO(245, 245, 245, 1),
        duration: const Duration(seconds: 1),
      ),
      child: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.fromLTRB(9.spx, 0.spx, 9.spx, 0.spx),
              itemCount: skeletonData.length,
              itemBuilder: (context, index) {
                final feed = skeletonData[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: FeedItem(
                    feed: feed,
                    controller: tempController,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
