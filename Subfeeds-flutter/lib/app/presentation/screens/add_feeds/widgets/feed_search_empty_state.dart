import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// RSS源搜索空状态组件
class FeedSearchEmptyState extends StatelessWidget {
  final bool hasSearched;

  const FeedSearchEmptyState({
    super.key,
    required this.hasSearched,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/search-empty.png',
            width: 120.spx,
            height: 120.spx,
          ),
          const SizedBox(height: 16),
          Text(
            'no_results'.tr,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: const Color(0xFF72849c),
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
