import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/presentation/screens/add_feeds/add_feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/empty_results.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/feed_item.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 专门用于 AddFeeds 页面的搜索结果组件，使用 Sliver 避免双滚动
class AddFeedsSearchResults extends StatelessWidget {
  final AddFeedsController controller;

  const AddFeedsSearchResults({super.key, required this.controller});

  static Color? getSkeletonColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color.fromRGBO(97, 97, 97, 1)
        : const Color.fromRGBO(224, 224, 224, 1);
  }

  /// 生成骨架屏数据
  List<Map<String, dynamic>> generateSkeletonItems(int count) {
    return List.generate(
        count,
        (index) => {
              'id': 'skeleton_$index',
              'name': 'RSS Feed Loading Title ${index + 1}',
              'description':
                  'This is a loading description for RSS feed that will be replaced with real content when the data loads from the server.',
              'url': 'https://example.com/feed_$index.xml',
              'website': 'https://example.com',
              'category': 'Technology',
              'type': 'rss',
              'isSub': 0,
              'img': '',
              'createTime': DateTime.now().millisecondsSinceEpoch ~/ 1000,
              'articleList': [
                {
                  'id': 'article_${index}_1',
                  'title': 'Article Title Loading...',
                  'summary': 'Article summary loading placeholder text...',
                  'description':
                      'This is a skeleton article description with images <img src="https://via.placeholder.com/300x200/CCCCCC/969696?text=Loading+1" alt="Loading 1"> and more content <img src="https://via.placeholder.com/300x200/CCCCCC/969696?text=Loading+2" alt="Loading 2"> with additional images <img src="https://via.placeholder.com/300x200/CCCCCC/969696?text=Loading+3" alt="Loading 3"> and final image <img src="https://via.placeholder.com/300x200/CCCCCC/969696?text=Loading+4" alt="Loading 4"> for skeleton display.',
                  'publishTime': DateTime.now().millisecondsSinceEpoch,
                  'img': '',
                  'isSkeleton': true,
                },
                {
                  'id': 'article_${index}_2',
                  'title': 'Another Article Title Loading...',
                  'summary':
                      'Another article summary loading placeholder text...',
                  'description':
                      'Another skeleton article description with sample images <img src="https://via.placeholder.com/300x200/DDDDDD/AAAAAA?text=Sample+1" alt="Sample 1"> and more content <img src="https://via.placeholder.com/300x200/DDDDDD/AAAAAA?text=Sample+2" alt="Sample 2"> for demonstration.',
                  'publishTime': DateTime.now().millisecondsSinceEpoch,
                  'img': '',
                  'isSkeleton': true,
                },
              ],
              'isSkeleton': true, // 标记为骨架屏数据
            });
  }

  /// 构建搜索结果标题
  Widget _buildResultsHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(20.spx, 0.spx, 20.spx, 6.spx),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'search_results'.tr,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontSize: 14.spx,
                  fontWeight: FontWeight.w500,
                ),
          ),
          Text(
            '${controller.totalResults.value} ${'found'.tr}',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  /// 构建加载更多的骨架屏
  List<Widget> _buildLoadingMoreSkeleton(BuildContext context) {
    final skeletonData = generateSkeletonItems(3);
    final baseColor = getSkeletonColor(context);

    return skeletonData.map((feed) {
      return Skeletonizer(
        enabled: true,
        effect: ShimmerEffect(
          baseColor: baseColor ?? Colors.grey[300]!,
          highlightColor: const Color.fromRGBO(245, 245, 245, 1),
          duration: const Duration(seconds: 1),
        ),
        child: Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: FeedItem(feed: feed, controller: _createControllerAdapter()),
        ),
      );
    }).toList();
  }

  /// 创建适配器控制器
  app_search.SearchController _createControllerAdapter() {
    return _SearchControllerAdapter(controller);
  }

  @override
  Widget build(BuildContext context) {
    // 这个组件主要通过 buildSlivers 方法使用，build 方法不会被调用
    return const SizedBox.shrink();
  }

  /// 构建搜索结果的 Slivers
  List<Widget> buildSlivers(BuildContext context) {
    return [
      Obx(() {
        if (controller.searchResults.isEmpty) {
          return SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.only(top: 130.spx),
              child: EmptyResults(),
            ),
          );
        }

        // 结果标题
        // final headerSliver = SliverToBoxAdapter(
        //   child: _buildResultsHeader(context),
        // );

        // 结果列表
        final listSliver = SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              // 显示加载更多指示器或"No More"提示
              if (index == controller.searchResults.length) {
                if (controller.isLoadingMore.value) {
                  // 使用骨架屏替代CircularProgressIndicator
                  return Padding(
                    padding: EdgeInsets.symmetric(horizontal: 9.spx),
                    child: Column(
                      children: _buildLoadingMoreSkeleton(context),
                    ),
                  );
                } else if (controller.hasLoadedAll.value) {
                  return Container(
                    padding: EdgeInsets.symmetric(vertical: 16.spx),
                    child: Center(
                      child: Text(
                        'home_no_more'.tr,
                        style: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              }

              // 检查是否需要加载更多
              if (index >= controller.searchResults.length - 3 &&
                  !controller.isLoadingMore.value &&
                  !controller.hasLoadedAll.value) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  controller.loadMore();
                });
              }

              // 显示搜索结果项
              final feed = controller.searchResults[index];
              return Padding(
                padding:
                    EdgeInsets.only(left: 9.spx, bottom: 6.5.spx, right: 9.spx),
                child: FeedItem(
                    feed: feed, controller: _createControllerAdapter()),
              );
            },
            childCount: controller.searchResults.length +
                (controller.isLoadingMore.value || controller.hasLoadedAll.value
                    ? 1
                    : 0),
          ),
        );

        return SliverMainAxisGroup(
          slivers: [listSliver],
        );
      }),
    ];
  }
}

/// 适配器类，将AddFeedsController适配为SearchController接口
class _SearchControllerAdapter extends app_search.SearchController {
  final AddFeedsController _addFeedsController;

  _SearchControllerAdapter(this._addFeedsController);

  @override
  RxList<Map<String, dynamic>> get searchResults =>
      _addFeedsController.searchResults;

  @override
  RxInt get totalResults => _addFeedsController.totalResults;

  @override
  RxBool get isLoadingMore => _addFeedsController.isLoadingMore;

  @override
  RxBool get hasLoadedAll => _addFeedsController.hasLoadedAll;

  @override
  Future<void> loadMore() => _addFeedsController.loadMore();

  @override
  Future<void> subscribeFeed(Map<String, dynamic> feed) =>
      _addFeedsController.subscribeFeed(feed);

  @override
  Future<void> unsubscribeFeed(Map<String, dynamic> feed) =>
      _addFeedsController.unsubscribeFeed(feed);

  @override
  RxInt get searchType => 1.obs; // 固定为关键词搜索

  // 其他不需要的方法返回默认值或空实现
  @override
  Future<void> setRead(
      String articleId, String feedId, String suffixTable) async {}
}
