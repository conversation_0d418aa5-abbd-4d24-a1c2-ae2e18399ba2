import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/add_feeds/add_feeds_controller.dart';
import 'package:subfeeds/app/presentation/screens/add_feeds/widgets/feed_search_empty_state.dart';
import 'package:subfeeds/app/presentation/screens/add_feeds/widgets/search_history_section.dart';
import 'package:subfeeds/app/presentation/screens/add_feeds/widgets/search_results_skeleton.dart';
import 'package:subfeeds/app/presentation/screens/add_feeds/widgets/add_feeds_search_results.dart';
import 'package:flutter_svg/svg.dart';

class AddFeedsScreen extends StatefulWidget {
  const AddFeedsScreen({super.key});

  @override
  State<AddFeedsScreen> createState() => _AddFeedsScreenState();
}

class _AddFeedsScreenState extends State<AddFeedsScreen> {
  final FocusNode searchFocusNode = FocusNode();
  final RxString searchText = ''.obs;
  final ScrollController scrollController = ScrollController();
  final RxBool showFixedTitle = false.obs;

  @override
  void initState() {
    super.initState();
    _setupScrollController();
  }

  @override
  void dispose() {
    searchFocusNode.dispose();
    scrollController.dispose();
    super.dispose();
  }

  void _setupScrollController() {
    scrollController.addListener(() {
      // 当滚动到一定位置时显示固定标题
      // 这里假设当滚动超过300像素时显示固定标题
      if (scrollController.offset > 300 && !showFixedTitle.value) {
        showFixedTitle.value = true;
      } else if (scrollController.offset <= 300 && showFixedTitle.value) {
        showFixedTitle.value = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AddFeedsController());

    return Obx(() => Scaffold(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF3D3D3D)
              : const Color(0xFFF7FAFF),
          appBar: _buildSimpleAppBar(context),
          body: SafeArea(
            child: CustomScrollView(
              controller: scrollController,
              slivers: [
                // Header section
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.spx),
                    child: Column(
                      children: [
                        _buildHeader(context),
                        _buildSearchField(context, controller.searchController,
                            searchText, searchFocusNode),
                        SizedBox(height: 20.spx),
                        // 默认状态：显示搜索历史（始终显示）
                        Column(
                          children: _buildDefaultContent(
                            context,
                            controller,
                            controller.searchHistory,
                            controller.searchResults,
                            controller.isLoading.value,
                          ),
                        ),
                        SizedBox(height: 10.spx),
                      ],
                    ),
                  ),
                ),
                // Content section - 使用新的搜索结果组件
                ..._buildContentSlivers(context, controller),
              ],
            ),
          ),
        ));
  }

  PreferredSizeWidget _buildSimpleAppBar(BuildContext context) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Obx(() => AppBar(
            backgroundColor: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF3D3D3D)
                : const Color(0xFFF7FAFF),
            elevation: 0,
            leading: IconButton(
              onPressed: () => Get.back(),
              icon: Icon(
                Icons.arrow_back,
                color: const Color(0xFF7F8EA7),
                size: 20.spx,
              ),
            ),
            title: showFixedTitle.value
                ? Text(
                    'to_feeds'.tr,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.titleLarge?.color,
                      fontSize: 16.spx,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                : null,
            centerTitle: true,
          )),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(0.spx, 39.spx, 0.spx, 49.spx),
      child: Column(
        children: [
          Text(
            'to_feeds'.tr,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontSize: 28.spx,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF949494)
                      : const Color(0xFFA1B2FF).withValues(alpha: 0.7),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField(
      BuildContext context,
      TextEditingController searchController,
      RxString searchText,
      FocusNode focusNode) {
    final controller = Get.find<AddFeedsController>();
    return TextField(
      autofocus: false, // 防止自动获取焦点导致不必要的键盘弹出
      controller: searchController,
      focusNode: focusNode, // 关联焦点节点
      decoration: InputDecoration(
        isDense: true,
        filled: true,
        fillColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF5B5B5B)
            : const Color(0xFFE5E9F1),
        contentPadding:
            EdgeInsets.symmetric(horizontal: 12.spx, vertical: 12.spx),
        hintStyle: TextStyle(
          fontSize: 14.spx,
          fontWeight: FontWeight.w500,
          color: Theme.of(context).brightness == Brightness.dark
              ? Color(0XFF909090)
              : Color(0XFFBCC2CC),
        ),
        prefixIcon: Padding(
          padding: EdgeInsets.only(left: 13.spx, right: 4.spx),
          child: SvgPicture.asset(
            'assets/feeds/feeds_search.svg',
            width: 20.spx,
            height: 20.spx,
            colorFilter: ColorFilter.mode(
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0XFF909090)
                  : const Color(0XFFBCC2CC),
              BlendMode.srcIn,
            ),
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 20.spx,
          maxHeight: 20.spx,
        ),
        hintText: 'discover_search_hint'.tr,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(40.spx),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(40.spx),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(40.spx),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 1,
          ),
        ),
        suffixIcon: Obx(() => searchText.value.isNotEmpty
            ? IconButton(
                onPressed: () {
                  searchController.clear();
                  searchText.value = '';
                },
                icon: const Icon(Icons.clear, size: 20),
                color: Colors.grey,
              )
            : const SizedBox.shrink()),
      ),
      onChanged: (value) {
        searchText.value = value;
      },
      onSubmitted: (value) {
        if (value.isNotEmpty) {
          controller.performSearch(value);
        }
      },
    );
  }

  /// 构建内容区域的 slivers，避免双滚动问题
  List<Widget> _buildContentSlivers(
      BuildContext context, AddFeedsController controller) {
    return [
      Obx(() {
        if (!mounted) {
          return SliverToBoxAdapter(child: SizedBox(height: 20.spx));
        }

        try {
          final isLoading = controller.isLoading.value;
          final searchResults = controller.searchResults;

          // 如果正在加载且没有搜索结果，显示骨架屏
          if (isLoading && searchResults.isEmpty) {
            return SliverFillRemaining(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 0.spx),
                child: const SearchResultsSkeleton(),
              ),
            );
          }

          // 如果有搜索结果，使用新的搜索结果组件
          final searchResultsWidget =
              AddFeedsSearchResults(controller: controller);
          return SliverMainAxisGroup(
            slivers: searchResultsWidget.buildSlivers(context),
          );
        } catch (e) {
          // 如果出现任何错误，返回一个安全的默认组件
          return SliverToBoxAdapter(
            child: Center(
              child: Text('Loading...'),
            ),
          );
        }
      }),
    ];
  }

  /// 构建默认内容列表，适配横向滚动
  List<Widget> _buildDefaultContent(
    BuildContext context,
    AddFeedsController controller,
    RxList<String> searchHistory,
    RxList<Map<String, dynamic>> searchResults,
    bool isLoading,
  ) {
    final List<Widget> children = [];

    // 搜索历史始终显示
    if (searchHistory.isNotEmpty) {
      // 为横向滚动调整搜索历史组件
      children.add(SizedBox(
        width: MediaQuery.of(context).size.width, // 设置固定宽度
        child: SearchHistorySection(
          searchHistory: searchHistory,
          onHistoryTap: (query) {
            controller.searchController.text = query;
            controller.performSearch(query);
          },
          onHistoryRemove: controller.removeSearchHistory,
          onClearAll: controller.clearSearchHistory,
        ),
      ));
    }

    // 添加右侧间距
    children.add(SizedBox(width: 10.spx));

    // 确保至少有一个子组件
    if (children.isEmpty) {
      children.add(SizedBox(width: 20.spx));
    }

    return children;
  }
}
