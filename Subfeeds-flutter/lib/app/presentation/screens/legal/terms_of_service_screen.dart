import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('用户协议'.tr),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '用户协议'.tr,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '最后更新: ${DateTime.now().year}-${DateTime.now().month.toString().padLeft(2, '0')}-${DateTime.now().day.toString().padLeft(2, '0')}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            _buildSection(
              '接受条款'.tr,
              '欢迎使用SubFeeds应用（以下简称"我们"、"我们的"或"本应用"）。通过访问或使用我们的应用，您同意受本用户协议的约束。如果您不同意这些条款，请不要使用我们的应用。'
                  .tr,
            ),
            _buildSection(
              '服务描述'.tr,
              'SubFeeds是一个订阅内容管理应用，允许用户整合和管理来自各种来源的订阅内容。我们提供的服务可能会不时变更，我们保留修改、暂停或终止服务的权利，恕不另行通知。'
                  .tr,
            ),
            _buildSection(
              '用户账户'.tr,
              '1. 您可能需要创建一个账户才能使用我们的某些服务。您负责维护您的账户信息的机密性，并对发生在您账户下的所有活动负全部责任。\n\n'
                      '2. 您同意立即通知我们任何未经授权使用您账户的情况。我们不对因您未能保护您的账户信息而导致的任何损失负责。'
                  .tr,
            ),
            _buildSection(
              '用户行为'.tr,
              '您同意不会：\n\n'
                      '1. 违反任何适用的法律法规。\n'
                      '2. 侵犯他人的知识产权或其他权利。\n'
                      '3. 传播恶意软件或有害代码。\n'
                      '4. 干扰或破坏我们的服务或连接到我们服务的服务器和网络。\n'
                      '5. 收集或存储其他用户的个人数据，除非得到明确授权。\n'
                      '6. 冒充任何个人或实体，或虚假陈述您与任何个人或实体的关系。'
                  .tr,
            ),
            _buildSection(
              '知识产权'.tr,
              '1. 我们的应用及其内容（包括但不限于文本、图形、徽标、图标和软件）受版权、商标和其他知识产权法保护。\n\n'
                      '2. 未经我们明确书面许可，您不得复制、修改、分发、销售或出租我们的应用或其任何部分。'
                  .tr,
            ),
            _buildSection(
              '免责声明'.tr,
              '我们的服务按"现状"和"可用"基础提供，不提供任何形式的保证，无论是明示的还是暗示的。我们不保证我们的服务将不间断、及时、安全或无错误，也不保证通过使用我们的服务获得的结果将准确或可靠。'
                  .tr,
            ),
            _buildSection(
              '责任限制'.tr,
              '在法律允许的最大范围内，我们对因使用或无法使用我们的服务而导致的任何直接、间接、附带、特殊、惩罚性或后果性损害不承担责任。'
                  .tr,
            ),
            _buildSection(
              '协议修改'.tr,
              '我们可能会不时修改本用户协议。如有重大变更，我们会通过在应用中发布通知或发送电子邮件等方式通知您。您继续使用我们的服务将视为您接受修改后的条款。'
                  .tr,
            ),
            _buildSection(
              '终止'.tr,
              '我们可能会因任何原因，包括但不限于违反本用户协议，随时终止或暂停您对我们服务的访问，恕不另行通知。'.tr,
            ),
            _buildSection(
              '适用法律'.tr,
              '本用户协议受中华人民共和国法律管辖，并按其解释，不考虑法律冲突原则。'.tr,
            ),
            _buildSection(
              '联系我们'.tr,
              '如果您对本用户协议有任何疑问或建议，请通过以下方式联系我们：\n\<EMAIL>'.tr,
            ),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          content,
          style: const TextStyle(
            fontSize: 16,
            height: 1.5,
          ),
        ),
      ],
    );
  }
}
