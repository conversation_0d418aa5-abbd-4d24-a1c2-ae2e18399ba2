import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('隐私政策'.tr),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '隐私政策'.tr,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '最后更新: ${DateTime.now().year}-${DateTime.now().month.toString().padLeft(2, '0')}-${DateTime.now().day.toString().padLeft(2, '0')}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            _buildSection(
              '引言'.tr,
              '欢迎使用SubFeeds应用（以下简称"我们"、"我们的"或"本应用"）。我们非常重视您的隐私，并致力于保护您的个人信息。本隐私政策旨在向您说明我们如何收集、使用、存储和共享您的信息，以及您可以如何访问、更新、控制和保护您的信息。'
                  .tr,
            ),
            _buildSection(
              '信息收集'.tr,
              '我们可能收集以下类型的信息：\n\n'
                      '1. 您提供给我们的信息：包括但不限于注册账户时提供的姓名、电子邮件地址、电话号码等。\n'
                      '2. 设备信息：如设备型号、操作系统版本、唯一设备标识符等。\n'
                      '3. 日志信息：如IP地址、浏览器类型、访问日期和时间等。\n'
                      '4. 位置信息：经您授权后，我们可能会收集您的精确或大致位置信息。\n'
                      '5. 使用信息：如您使用的功能、访问的页面、点击的链接等。'
                  .tr,
            ),
            _buildSection(
              '信息使用'.tr,
              '我们使用收集的信息用于以下目的：\n\n'
                      '1. 提供、维护和改进我们的服务。\n'
                      '2. 开发新的服务或功能。\n'
                      '3. 个性化您的体验，包括提供定制内容和建议。\n'
                      '4. 进行数据分析，以改进我们的服务。\n'
                      '5. 与您沟通，包括提供客户支持、发送通知和更新。\n'
                      '6. 保护我们的用户和公众的安全。'
                  .tr,
            ),
            _buildSection(
              '信息共享'.tr,
              '我们不会出售您的个人信息。我们可能在以下情况下共享您的信息：\n\n'
                      '1. 经您同意。\n'
                      '2. 与我们的服务提供商共享，以便他们为我们提供服务。\n'
                      '3. 遵守法律法规、法院命令或其他法律程序。\n'
                      '4. 保护我们或他人的权利、财产或安全。\n'
                      '5. 与我们的关联公司共享。'
                  .tr,
            ),
            _buildSection(
              '信息安全'.tr,
              '我们采取合理的安全措施保护您的信息不被未经授权的访问、使用或披露。然而，请注意，尽管我们努力保护您的信息，但没有任何安全措施是完全不可破解的。'
                  .tr,
            ),
            _buildSection(
              '您的权利'.tr,
              '根据适用的法律，您可能拥有以下权利：\n\n'
                      '1. 访问您的个人信息。\n'
                      '2. 更正不准确的个人信息。\n'
                      '3. 删除您的个人信息。\n'
                      '4. 限制或反对处理您的个人信息。\n'
                      '5. 数据可携带性。\n'
                      '6. 撤回同意。'
                  .tr,
            ),
            _buildSection(
              '隐私政策更新'.tr,
              '我们可能会不时更新本隐私政策。如有重大变更，我们会通过在应用中发布通知或发送电子邮件等方式通知您。'.tr,
            ),
            _buildSection(
              '联系我们'.tr,
              '如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：\n\<EMAIL>'.tr,
            ),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          content,
          style: const TextStyle(
            fontSize: 16,
            height: 1.5,
          ),
        ),
      ],
    );
  }
}
