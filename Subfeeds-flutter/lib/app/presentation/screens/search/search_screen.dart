import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;
import 'package:subfeeds/app/presentation/screens/search/searchWidget/search_app_bar.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/category_tags.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/search_content.dart';

class SearchScreen extends GetView<app_search.SearchController> {
  const SearchScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF3c3c3c)
              : const Color(0xfff7faff),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部AppBar（已包含搜索框）
              SearchAppBar(controller: controller),

              // 二级分类标签列表（仅在分类搜索时显示）
              CategoryTags(controller: controller),

              // 搜索结果内容
              SearchContent(controller: controller),
            ],
          ),
        ),
      ),
    );
  }

  String formatTime(dynamic time) {
    try {
      if (time == null) return '';

      DateTime dateTime;
      if (time is String) {
        // 处理pubDate格式
        dateTime = DateTime.parse(time);
      } else if (time is int) {
        // 处理createTime时间戳
        dateTime = DateTime.fromMillisecondsSinceEpoch(time * 1000);
      } else {
        return '';
      }

      final now = DateTime.now();
      final difference = now.difference(dateTime);
      if (difference.inHours < 3) {
        return 'just_now'.tr;
      }
      // 1天以内，返回小时
      if (difference.inDays < 1) {
        final hours = difference.inHours;
        return '${hours}H';
      }
      // 30天以内，返回天数
      else if (difference.inDays < 30) {
        return '${difference.inDays}D';
      }
      // 超过30天，返回完整日期
      else {
        return DateFormat('yyyy-MM-dd').format(dateTime);
      }
    } catch (e) {
      debugPrint('时间格式化失败: $e');
      return '';
    }
  }
}
