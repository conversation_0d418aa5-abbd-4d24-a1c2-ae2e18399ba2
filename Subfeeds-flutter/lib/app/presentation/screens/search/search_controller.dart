import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/data/repositories/feeds_repository.dart' as feeds;
import 'package:subfeeds/app/data/repositories/article_repository.dart'
    as articles;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/presentation/screens/article/article_controller.dart';

/// 搜索页控制器
class SearchController extends GetxController {
  final feeds.ArticleRepository _feedsRepository = feeds.ArticleRepository();
  final articles.ArticleRepository _articleRepository =
      articles.ArticleRepository();

  // 搜索文本控制器
  late TextEditingController searchQueryController;
  // 是否是英语
  final isEnglish = false.obs;
  // 搜索类型 1:关键词搜索 2:分类搜索 3:Telegram搜索 4:Google News搜索
  final searchType = 1.obs;

  // 分类ID
  final categoryId = ''.obs;

  // 分类名称
  final categoryName = ''.obs;

  // 搜索关键词
  final searchQuery = ''.obs;

  // 加载状态
  final isLoading = false.obs;

  // 搜索结果列表
  final searchResults = <Map<String, dynamic>>[].obs;

  // 总结果数
  final totalResults = 0.obs;

  // 分页信息
  int _page = 1;
  final int _pageSize = 10;
  final hasMoreData = true.obs;
  final isLoadingMore = false.obs;
  final hasLoadedAll = false.obs;

  // 二级分类列表
  final subCategories = <Map<String, dynamic>>[].obs;

  // 选中的二级分类
  final selectedSubCategory = Rx<String?>(null);

  // Google News 搜索结果
  final googleNewsList = <Map<String, dynamic>>[].obs;
  // Google News 订阅状态 0:未订阅 1:已订阅
  final googleNewsStatus = 0.obs;

  // 文章状态变更流的订阅
  StreamSubscription? _articleStatusSubscription;

  @override
  void onInit() {
    super.onInit();
    _isEnglish();
    searchQueryController = TextEditingController();

    // 获取传递的参数
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      if (args.containsKey('type')) {
        searchType.value = args['type'] as int? ?? 1;
      }

      if (args.containsKey('query')) {
        searchQuery.value = args['query'] as String? ?? '';
        searchQueryController.text = searchQuery.value;
      }

      if (args.containsKey('categoryId')) {
        categoryId.value = args['categoryId'] as String? ?? '';
      }

      if (args.containsKey('categoryName')) {
        categoryName.value = args['categoryName'] as String? ?? '';
      }
    }

    // 如果是分类搜索，加载二级分类
    if (searchType.value == 2 && categoryId.value.isNotEmpty) {
      loadSubCategories();
    }

    // 如果是 Google News 搜索，发送空字符串搜索请求
    if (searchType.value == 4) {
      searchQuery.value = 'Google';
      searchQueryController.text = 'Google';
      search();
    } else {
      // 其他类型的搜索，保持原有逻辑
      search();
    }

    // 监听文章状态变更事件
    _articleStatusSubscription = ArticleController.articleStatusChanged.stream
        .listen(_handleArticleStatusChanged);
  }

  @override
  void onClose() {
    searchQueryController.dispose();
    // 取消文章状态变更监听
    _articleStatusSubscription?.cancel();
    super.onClose();
  }

  /// 加载二级分类
  Future<void> loadSubCategories() async {
    try {
      final response = await _feedsRepository.getFeedsCategory();
      if (response.isSuccess && response.data != null) {
        // 获取当前分类的二级分类
        subCategories.value = (response.data ?? [])
            .where((cat) => cat['parentId'] == categoryId.value)
            .toList();
      }
    } catch (e) {
      print('加载二级分类失败: $e');
    }
  }

  /// 选择二级分类
  void selectSubCategory(String? subCategoryId) {
    selectedSubCategory.value = subCategoryId;
    _page = 1;
    search();
  }

  // 从本地获取语言 判断是否是英语
  Future<void> _isEnglish() async {
    final prefs = await SharedPreferences.getInstance();
    final language = prefs.getString('locale') == 'en_US' ? 'English' : '简体中文';
    isEnglish.value = language == 'English';
  }

  /// 重置页码
  void resetPage() {
    _page = 1;
    hasLoadedAll.value = false;
  }

  /// 执行搜索
  Future<void> search() async {
    debugPrint('search${searchType.value},$_page');

    if (_page == 1) {
      isLoading.value = true;
      hasLoadedAll.value = false;
      searchResults.clear(); // 清空之前的搜索结果
      totalResults.value = 0; // 重置总数
    }

    try {
      if (searchType.value == 1) {
        // 关键词搜索
        searchQuery.value = searchQueryController.text.trim();
        if (searchQuery.value.isEmpty && _page == 1) {
          searchResults.clear();
          totalResults.value = 0;
          return;
        }

        final response = await _feedsRepository.searchFeeds({
          'search': searchQuery.value,
          'pageNum': _page,
          'pageSize': _pageSize,
        });

        handleSearchResponse(response);
      } else if (searchType.value == 2) {
        // 分类搜索
        final categoryList = selectedSubCategory.value ?? categoryId.value;
        searchQuery.value = searchQueryController.text.trim();

        final response = await _feedsRepository.searchCategoryFeeds({
          'search': searchQuery.value,
          'pageNum': _page,
          'pageSize': _pageSize,
          'categoryList': categoryList,
          'popular': 1,
          'articleList': 3,
        });

        handleSearchResponse(response);
      } else if (searchType.value == 3) {
        // Telegram 搜索
        searchQuery.value = searchQueryController.text.trim();
        if (searchQuery.value.isEmpty) {
          searchResults.clear();
          totalResults.value = 0;
          return;
        }

        final response = await _feedsRepository.searchTelegram({
          'search': searchQuery.value,
          'pageNum': _page,
          'pageSize': _pageSize,
        });

        if (response.isSuccess && response.data != null) {
          final data = response.data ?? {};
          final List<dynamic> results =
              data['pageList'] as List<dynamic>? ?? [];
          final total = (data['total'] as num?)?.toInt() ?? 0;

          if (_page == 1) {
            searchResults.assignAll(
                results.map((item) => item as Map<String, dynamic>).toList());
          } else {
            searchResults.addAll(
                results.map((item) => item as Map<String, dynamic>).toList());
          }

          totalResults.value = total;
          hasMoreData.value = searchResults.length < total;
          _page++;
        } else {
          if (_page == 1) {
            searchResults.clear();
            totalResults.value = 0;
          }
          hasMoreData.value = false;
          hasLoadedAll.value = true;
        }
      } else if (searchType.value == 5) {
        // YouTube 搜索
        searchQuery.value = searchQueryController.text.trim();
        if (searchQuery.value.isEmpty) {
          searchResults.clear();
          totalResults.value = 0;
          return;
        }

        final response = await _feedsRepository.searchYoutube({
          'search': searchQuery.value,
          'pageNum': _page,
          'pageSize': _pageSize,
        });

        if (response.isSuccess && response.data != null) {
          final data = response.data ?? {};
          final List<dynamic> results =
              data['pageList'] as List<dynamic>? ?? [];
          final total = (data['total'] as num?)?.toInt() ?? 0;

          if (_page == 1) {
            searchResults.assignAll(
                results.map((item) => item as Map<String, dynamic>).toList());
          } else {
            searchResults.addAll(
                results.map((item) => item as Map<String, dynamic>).toList());
          }

          totalResults.value = total;
          hasMoreData.value = searchResults.length < total;
          _page++;
        } else {
          if (_page == 1) {
            searchResults.clear();
            totalResults.value = 0;
          }
          hasMoreData.value = false;
          hasLoadedAll.value = true;
        }
      } else if (searchType.value == 6) {
        // Reddit 搜索
        searchQuery.value = searchQueryController.text.trim();
        if (searchQuery.value.isEmpty) {
          searchResults.clear();
          totalResults.value = 0;
          return;
        }

        final response = await _feedsRepository.searchReddit({
          'search': searchQuery.value,
          'pageNum': _page,
          'pageSize': _pageSize,
        });

        if (response.isSuccess && response.data != null) {
          final data = response.data ?? {};
          final List<dynamic> results =
              data['pageList'] as List<dynamic>? ?? [];
          final total = (data['total'] as num?)?.toInt() ?? 0;

          if (_page == 1) {
            searchResults.assignAll(
                results.map((item) => item as Map<String, dynamic>).toList());
          } else {
            searchResults.addAll(
                results.map((item) => item as Map<String, dynamic>).toList());
          }

          totalResults.value = total;
          hasMoreData.value = searchResults.length < total;
          _page++;
        } else {
          if (_page == 1) {
            searchResults.clear();
            totalResults.value = 0;
          }
          hasMoreData.value = false;
          hasLoadedAll.value = true;
        }
      } else if (searchType.value == 4) {
        // Google News 搜索
        searchQuery.value = searchQueryController.text.trim();
        final response = await _feedsRepository.searchGoogleNews({
          'search': searchQuery.value,
          'pageNum': 1,
          'pageSize': 10,
        });

        if (response.isSuccess && response.data != null) {
          final data = response.data ?? {};
          final List<dynamic> list = data['list'] as List<dynamic>? ?? [];
          googleNewsList.value =
              list.map((item) => item as Map<String, dynamic>).toList();
          // 更新 Google News 订阅状态
          googleNewsStatus.value = data['status'] as int? ?? 0;
          hasMoreData.value = false;
          hasLoadedAll.value = true;
        } else {
          googleNewsList.clear();
          googleNewsStatus.value = 0;
        }
      }
    } catch (e) {
      print('搜索失败: $e');
      if (searchType.value == 4) {
        googleNewsList.clear();
        googleNewsStatus.value = 0;
      } else {
        searchResults.clear();
        totalResults.value = 0;
      }
    } finally {
      isLoading.value = false;
    }
  }

  /// 处理搜索响应
  void handleSearchResponse(dynamic response) {
    if (response.isSuccess && response.data != null) {
      final data = response.data;
      final List<dynamic> pageList = data['pageList'] as List<dynamic>;
      final results =
          pageList.map((item) => item as Map<String, dynamic>).toList();

      if (_page == 1) {
        searchResults.assignAll(results);
      } else {
        searchResults.addAll(results);
      }

      totalResults.value = (data['total'] as num?)?.toInt() ?? 0;
      hasMoreData.value = searchResults.length < totalResults.value;
      _page++;
    } else {
      if (_page == 1) {
        searchResults.clear();
        totalResults.value = 0;
      }
      hasMoreData.value = false;
      hasLoadedAll.value = true;
    }
  }

  /// 加载更多
  Future<void> loadMore() async {
    if (!hasMoreData.value || isLoading.value || isLoadingMore.value) return;

    isLoadingMore.value = true;
    await search();
    isLoadingMore.value = false;

    // 如果没有更多数据，设置hasLoadedAll为true
    if (!hasMoreData.value) {
      hasLoadedAll.value = true;
    }
  }

  /// 刷新
  Future<void> refresh() async {
    _page = 1;
    hasLoadedAll.value = false;
    await search();
  }

  /// 订阅RSS源
  Future<void> subscribeFeed(Map<String, dynamic> feed) async {
    try {
      final response = await _feedsRepository.subscribeRss({
        'searchValue': feed['id'],
        'type': feed['type'],
      });

      if (response.isSuccess) {
        // 订阅成功，更新订阅状态
        final index =
            searchResults.indexWhere((item) => item['id'] == feed['id']);
        if (index != -1) {
          final updatedFeed = Map<String, dynamic>.from(searchResults[index]);
          updatedFeed['isSub'] = 1;
          searchResults[index] = updatedFeed;
        }

        Get.closeAllSnackbars();
        Get.snackbar(
          'success'.tr,
          'subscribe_success'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        Get.closeAllSnackbars();
        Get.snackbar(
          'failed'.tr,
          response.msg,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        throw Exception(response.msg);
      }
    } catch (e) {
      Get.closeAllSnackbars();
      Get.snackbar(
        'error'.tr,
        'subscribe_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
      throw Exception(e);
    }
  }

  /// 取消订阅RSS源
  Future<void> unsubscribeFeed(Map<String, dynamic> feed) async {
    try {
      final response =
          await _feedsRepository.deleteRss([int.parse(feed['id'])]);

      if (response.isSuccess) {
        // 更新订阅状态
        final index =
            searchResults.indexWhere((item) => item['id'] == feed['id']);
        if (index != -1) {
          final updatedFeed = Map<String, dynamic>.from(searchResults[index]);
          updatedFeed['isSub'] = 0;
          searchResults[index] = updatedFeed;
        }

        Get.closeAllSnackbars();
        Get.snackbar(
          'success'.tr,
          'unsubscribe_success'.tr,
          snackPosition: SnackPosition.TOP,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
      } else {
        Get.closeAllSnackbars();
        Get.snackbar(
          'failed'.tr,
          response.msg,
          snackPosition: SnackPosition.TOP,
          icon: const Icon(Icons.error, color: Colors.red),
          backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
              ? const Color(0xFF161617)
              : const Color(0xFF161617),
          colorText: Theme.of(Get.context!).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        );
        // 抛出异常
        throw Exception(response.msg);
      }
    } catch (e) {
      Get.closeAllSnackbars();
      Get.snackbar(
        'error'.tr,
        'unsubscribe_failed'.tr,
        snackPosition: SnackPosition.TOP,
        icon: const Icon(Icons.error, color: Colors.red),
        backgroundColor: Theme.of(Get.context!).brightness == Brightness.dark
            ? const Color(0xFF161617)
            : const Color(0xFF161617),
        colorText: Theme.of(Get.context!).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      );
    }
  }

  /// 设置文章为已读
  Future<void> setRead(
      String articleId, String feedId, String suffixTable) async {
    try {
      // 发送插入历史记录请求
      final response = await _articleRepository.insertHistory(
        int.parse(articleId),
        int.tryParse(feedId) ?? -1,
        suffixTable,
      );

      if (response.isSuccess) {
        // 更新搜索结果中的文章已读状态
        for (var feed in searchResults) {
          if (feed['id'] == feedId) {
            final articleList = feed['articleList'] as List;
            final articleIndex = articleList
                .indexWhere((article) => article['id'].toString() == articleId);
            if (articleIndex != -1) {
              final article = articleList[articleIndex];
              article['isRead'] = 1;
              searchResults.refresh();
            }
            break;
          }
        }
      }
    } catch (e) {
      print('设置文章已读失败: $e');
    }
  }

  // 处理文章状态变更事件
  void _handleArticleStatusChanged(Map<String, dynamic> data) {
    if (data.containsKey('id')) {
      final String articleId = data['id'];

      // 更新搜索结果中的文章状态
      final index = searchResults
          .indexWhere((article) => article['id'].toString() == articleId);

      if (index != -1) {
        // 创建文章的副本
        final updatedArticle = Map<String, dynamic>.from(searchResults[index]);

        // 更新收藏状态
        if (data.containsKey('isCollect')) {
          updatedArticle['isCollect'] = data['isCollect'];
        }

        // 更新稍后阅读状态
        if (data.containsKey('isLaterRead')) {
          updatedArticle['isLaterRead'] = data['isLaterRead'];
        }

        // 更新搜索结果列表
        searchResults[index] = updatedArticle;

        // 刷新搜索结果列表，触发UI更新
        searchResults.refresh();
      }

      // 如果有Google News搜索结果，也需要更新
      if (googleNewsList.isNotEmpty) {
        final googleNewsIndex = googleNewsList
            .indexWhere((article) => article['id'].toString() == articleId);

        if (googleNewsIndex != -1) {
          final updatedGoogleNewsArticle =
              Map<String, dynamic>.from(googleNewsList[googleNewsIndex]);

          // 更新收藏状态
          if (data.containsKey('isCollect')) {
            updatedGoogleNewsArticle['isCollect'] = data['isCollect'];
          }

          // 更新稍后阅读状态
          if (data.containsKey('isLaterRead')) {
            updatedGoogleNewsArticle['isLaterRead'] = data['isLaterRead'];
          }

          // 更新Google News列表
          googleNewsList[googleNewsIndex] = updatedGoogleNewsArticle;

          // 刷新Google News列表
          googleNewsList.refresh();
        }
      }
    }
  }
}
