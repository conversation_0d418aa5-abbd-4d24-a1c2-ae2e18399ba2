import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;
import 'package:subfeeds/app/presentation/screens/search/searchWidget/article_item.dart';
import 'package:subfeeds/app/presentation/screens/feeds/unfollow_feeds_screen.dart';
import 'package:cached_network_image/cached_network_image.dart';

class FeedItem extends StatefulWidget {
  final Map<String, dynamic> feed;
  final app_search.SearchController controller;

  const FeedItem({
    super.key,
    required this.feed,
    required this.controller,
  });

  @override
  State<FeedItem> createState() => _FeedItemState();
}

class _FeedItemState extends State<FeedItem> {
  bool _isLoading = false; // 是否正在加载中

  @override
  void initState() {
    super.initState();
  }

  /// 获取当前订阅状态
  int get _currentSubStatus => widget.feed['isSub'] as int? ?? 0;

  /// 处理订阅/取消订阅操作
  Future<void> _handleSubscriptionToggle() async {
    // 如果是骨架屏数据或正在加载中，不执行任何操作
    if (widget.feed['isSkeleton'] == true || _isLoading) return;

    // 获取当前订阅状态
    final currentStatus = _currentSubStatus;

    // 设置加载状态
    setState(() {
      _isLoading = true;
    });

    try {
      // 发送网络请求
      if (currentStatus == 1) {
        // 取消订阅
        await widget.controller.unsubscribeFeed(widget.feed);
      } else {
        // 订阅
        await widget.controller.subscribeFeed(widget.feed);
      }

      // 如果没有抛出异常，说明操作成功
      // SearchController 已经更新了 searchResults 中的状态
      // 这里只需要触发 UI 重建来反映最新状态
      setState(() {
        // 强制重建 UI，显示最新的订阅状态
      });
    } catch (error) {
      debugPrint('订阅操作失败: $error');

      // 这里只需要触发 UI 重建来反映回退后的状态
      setState(() {
        // 强制重建 UI，显示回退后的状态
      });
    } finally {
      // 重置加载状态
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 0.spx, horizontal: 12.spx),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF565656)
            : Colors.white,
        borderRadius: BorderRadius.circular(14.spx),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF747474)
              : const Color(0xFFD5D5D5),
          width: 1.spx,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // RSS源信息
          Row(
            children: [
              // // RSS源图标，添加点击事件
              GestureDetector(
                onTap: () {
                  // 如果是骨架屏数据，不执行任何操作
                  if (widget.feed['isSkeleton'] == true) return;
                  // 如果当前时subfeeds feeds 搜索结果，则跳转到subfeeds feeds详情页
                  if (widget.controller.searchType.value == 1 ||
                      widget.controller.searchType.value == 2) {
                    Get.to(() => UnfollowFeedsScreen(feed: widget.feed));
                  }
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(32.spx),
                  child: CachedNetworkImage(
                    imageUrl: widget.feed['img'] ?? '',
                    width: 20.spx,
                    height: 20.spx,
                    fit: BoxFit.cover,
                    errorWidget: (context, error, stackTrace) => Container(
                      width: 20.spx,
                      height: 20.spx,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                      child: const Icon(Icons.rss_feed, size: 20),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 8.spx),
              // RSS源信息，添加点击事件
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    // 如果是骨架屏数据，不执行任何操作
                    if (widget.feed['isSkeleton'] == true) return;

                    if (widget.controller.searchType.value == 1 ||
                        widget.controller.searchType.value == 2) {
                      Get.to(() => UnfollowFeedsScreen(feed: widget.feed));
                    }
                  },
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.feed['name'] ?? '',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w400, fontSize: 12.spx),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      // Text(
                      //   feed['createTime'] != null
                      //       ? DateTime.fromMillisecondsSinceEpoch(
                      //               feed['createTime'] * 1000)
                      //           .toString()
                      //           .substring(0, 10)
                      //       : '',
                      //   style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      //         fontSize: 12,
                      //       ),
                      // ),
                    ],
                  ),
                ),
              ),
              // 订阅按钮
              Container(
                margin: EdgeInsets.only(left: 10.spx),
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _handleSubscriptionToggle,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                        horizontal: 8.spx, vertical: 4.spx),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6.spx),
                    ),
                    backgroundColor: _isLoading
                        ? Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFF555555)
                            : const Color(0xFFCCCCCC)
                        : _currentSubStatus == 1
                            ? Theme.of(context).brightness == Brightness.dark
                                ? const Color.fromARGB(255, 112, 112, 112)
                                : const Color(0xFFE6E6E6)
                            : Theme.of(context).brightness == Brightness.dark
                                ? const Color(0xFF2B2B2B)
                                : Theme.of(context).primaryColor,
                    elevation: 0,
                    maximumSize: Size(105.spx, 25.spx),
                    minimumSize: Size(105.spx, 25.spx),
                  ),
                  child: _isLoading
                      ? SizedBox(
                          width: 16.spx,
                          height: 16.spx,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.0,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white70
                                  : Colors.black54,
                            ),
                          ),
                        )
                      : Text(
                          _currentSubStatus == 1
                              ? 'discover_unsubscribe'.tr
                              : 'discover_subscribe'.tr,
                          style: TextStyle(
                            color: _currentSubStatus == 1
                                ? Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? const Color(0xFFD1D1D1)
                                    : const Color(0xFF999999)
                                : Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? const Color(0xFFFFFFFF)
                                    : const Color(0xFFFFFFFF),
                            fontSize: 13.spx,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                ),
              ),
            ],
          ),

          // 文章列表
          if (widget.feed['articleList'] != null &&
              (widget.feed['articleList'] as List).isNotEmpty)
            // ListView.builder(
            //   scrollDirection: Axis.horizontal,
            //   itemCount: 1,
            //   itemBuilder: (context, index) {
            //     final article = (widget.feed['articleList'] as List)[index];
            //     return ArticleItem(
            //       article: {
            //         ...article,
            //       },
            //       feed: widget.feed,
            //       controller: widget.controller,
            //     );
            //   },
            // ),
            ArticleItem(
              article: {
                ...(widget.feed['articleList'] as List)[0],
              },
              feed: widget.feed,
              controller: widget.controller,
            ),
        ],
      ),
    );
  }
}
