import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;

class SearchAppBar extends StatelessWidget {
  final app_search.SearchController controller;

  const SearchAppBar({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // 返回按钮
          InkWell(
            onTap: () => Get.back(),
            borderRadius: BorderRadius.circular(16),
            child: Container(
              width: 32.spx,
              height: 32.spx,
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF5B5B5B)
                    : const Color(0xFFE5E9F1),
                shape: BoxShape.circle,
              ),
              child: SvgPicture.asset(
                'assets/feeds/back.svg',
                width: 15.spx,
                height: 15.spx,
                colorFilter: ColorFilter.mode(
                  const Color(0xFF7F8EA7),
                  BlendMode.srcIn,
                ),
                fit: BoxFit.none,
              ),
            ),
          ),

          // 搜索框
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 12),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextField(
                controller: controller.searchQueryController,
                onChanged: (value) {
                  controller.searchQuery.value = value;
                  if (value.isEmpty && controller.searchType.value == 2) {
                    controller.resetPage();
                    controller.search();
                  }
                },
                decoration: InputDecoration(
                  isDense: true,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                  filled: true,
                  fillColor: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF5B5B5B)
                      : const Color(0xFFE5E9F1),
                  hintStyle: TextStyle(
                    fontSize: 14.spx,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Color(0XFF909090)
                        : Color(0XFFBCC2CC),
                  ),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 6.0),
                    child: SvgPicture.asset(
                      'assets/feeds/feeds_search.svg',
                      width: 20.spx,
                      height: 20.spx,
                      colorFilter: ColorFilter.mode(
                        Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFFb4bac5)
                            : const Color(0xFFb4bac5),
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  prefixIconConstraints: BoxConstraints(
                    minWidth: 36.spx,
                    maxHeight: 36.spx,
                  ),
                  hintText: 'search_hint'.tr,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(40.spx),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(40.spx),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(40.spx),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                      width: 1,
                    ),
                  ),
                ),
                onSubmitted: (_) {
                  controller.resetPage();
                  controller.search();
                },
                textInputAction: TextInputAction.search,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
