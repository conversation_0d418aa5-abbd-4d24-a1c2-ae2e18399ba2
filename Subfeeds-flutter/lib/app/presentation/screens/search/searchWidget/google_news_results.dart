import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;
import 'package:subfeeds/app/presentation/screens/search/searchWidget/empty_results.dart';
import 'package:url_launcher/url_launcher.dart';

class GoogleNewsResults extends StatelessWidget {
  final app_search.SearchController controller;

  const GoogleNewsResults({super.key, required this.controller});

  static Color? getSkeletonColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color.fromRGBO(97, 97, 97, 1)
        : const Color.fromRGBO(224, 224, 224, 1);
  }

  /// 生成骨架屏数据
  List<Map<String, dynamic>> generateSkeletonItems(int count) {
    return List.generate(
        count,
        (index) => {
              'id': 'skeleton_$index',
              'title': 'Google News Article Loading ${index + 1}',
              'description':
                  'This is a loading description for Google News article that will be replaced with real content when the data loads from the server.',
              'link': 'https://news.google.com/article_$index',
              'img': '',
              'createTime': DateTime.now().millisecondsSinceEpoch ~/ 1000,
              'publishTime': DateTime.now().millisecondsSinceEpoch,
              'isSkeleton': true, // 标记为骨架屏数据
            });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Google News RSS源卡片
        Padding(
          padding: const EdgeInsets.all(12),
          child: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF3f4458)
                  : const Color(0xFFeff1f4),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Google News',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'google_news_description'.tr,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                SvgPicture.asset(
                  'assets/share/google.svg',
                  width: 32.spx,
                  height: 32.spx,
                ),
              ],
            ),
          ),
        ),

        // Google News 文章列表
        Expanded(
          child: Obx(() {
            if (controller.googleNewsList.isEmpty) {
              return const EmptyResults();
            }

            return NotificationListener<ScrollNotification>(
              onNotification: (ScrollNotification scrollInfo) {
                // 当滚动到距离底部还有20%的位置时，提前加载更多
                if (!controller.isLoadingMore.value &&
                    controller.hasMoreData.value &&
                    scrollInfo.metrics.pixels > 0 &&
                    scrollInfo.metrics.pixels >=
                        scrollInfo.metrics.maxScrollExtent * 0.8) {
                  controller.loadMore();
                }
                return true;
              },
              child: ListView.builder(
                padding: const EdgeInsets.all(12),
                itemCount: controller.googleNewsList.length +
                    (controller.isLoadingMore.value ||
                            !controller.hasMoreData.value
                        ? 1
                        : 0),
                itemBuilder: (context, index) {
                  // 显示加载更多指示器或"No More"提示
                  if (index == controller.googleNewsList.length) {
                    if (controller.isLoadingMore.value) {
                      // 使用骨架屏替代CircularProgressIndicator
                      final skeletonData = generateSkeletonItems(3);
                      final baseColor = getSkeletonColor(context);
                      return Column(
                        children: skeletonData.map((article) {
                          return Skeletonizer(
                            enabled: true,
                            effect: ShimmerEffect(
                              baseColor: baseColor ?? Colors.grey[300]!,
                              highlightColor:
                                  const Color.fromRGBO(245, 245, 245, 1),
                              duration: const Duration(seconds: 1),
                            ),
                            child: _buildArticleItem(context, article),
                          );
                        }).toList(),
                      );
                    } else if (!controller.hasMoreData.value) {
                      return Container(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        child: Center(
                          child: Text(
                            'home_no_more'.tr,
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 14,
                            ),
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  }

                  // 显示文章项
                  final article = controller.googleNewsList[index];
                  return _buildArticleItem(context, article);
                },
              ),
            );
          }),
        ),

        // 订阅按钮
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: SizedBox(
            width: double.infinity,
            child: Obx(() => ElevatedButton(
                  onPressed: () {
                    controller.subscribeFeed({
                      'id': controller.searchQuery.value,
                      'type': 4,
                      'isSub': controller.googleNewsStatus.value,
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    backgroundColor: controller.googleNewsStatus.value == 1
                        ? Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFF212534)
                            : Colors.grey[300]
                        : Theme.of(context).primaryColor,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        controller.googleNewsStatus.value == 1
                            ? Icons.remove
                            : Icons.add,
                        size: 16,
                        color: controller.googleNewsStatus.value == 1
                            ? Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Colors.black87
                            : Colors.white,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        controller.googleNewsStatus.value == 1
                            ? 'unfollow'.tr
                            : 'follow'.tr,
                        style: TextStyle(
                          color: controller.googleNewsStatus.value == 1
                              ? Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black87
                              : Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )),
          ),
        ),
      ],
    );
  }

  /// 构建文章项
  Widget _buildArticleItem(BuildContext context, Map<String, dynamic> article) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12),
      leading: article['img'] != null && article['img'].toString().isNotEmpty
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                article['img'],
                width: 60.spx,
                height: 60.spx,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 60.spx,
                  height: 60.spx,
                  color: Colors.grey[200],
                  child: const Icon(Icons.image_not_supported),
                ),
              ),
            )
          : Container(
              width: 60.spx,
              height: 60.spx,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.article, color: Colors.grey),
            ),
      title: Text(
        article['title'] ?? '',
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: Theme.of(context).textTheme.titleSmall,
      ),
      subtitle: Text(
        article['createTime'] != null
            ? DateTime.fromMillisecondsSinceEpoch(article['createTime'] * 1000)
                .toString()
                .substring(0, 10)
            : '',
        style: Theme.of(context).textTheme.bodySmall,
      ),
      onTap: () {
        // 直接打开浏览器
        if (article['link'] != null) {
          launchUrl(Uri.parse(article['link']));
        }
      },
    );
  }
}
