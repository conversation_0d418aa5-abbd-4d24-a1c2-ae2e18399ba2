import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EmptyResults extends StatelessWidget {
  const EmptyResults({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/search-empty.png',
            width: 120,
            height: 120,
          ),
          const SizedBox(height: 16),
          Text(
            'no_results'.tr,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: const Color(0xFF72849c),
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
