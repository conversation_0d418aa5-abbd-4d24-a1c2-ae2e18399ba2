import 'package:flutter/material.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;
import 'package:subfeeds/app/presentation/screens/search/searchWidget/google_news_results.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/search_results.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/telegram_results.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/youtube_results.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/reddit_results.dart';

/// 搜索结果工厂类
/// 通过工厂模式生成对应的搜索结果组件
class SearchFactory {
  /// 获取搜索结果组件
  static Widget getSearchResults(app_search.SearchController controller) {
    switch (controller.searchType.value) {
      case 1: // 关键词搜索
      case 2: // 分类搜索
        return SearchResults(controller: controller);
      case 3: // Telegram 搜索
        return TelegramResults(controller: controller);
      case 4: // Google News 搜索
        return GoogleNewsResults(controller: controller);
      case 5: // YouTube 搜索
        return YoutubeResults(controller: controller);
      case 6: // Reddit 搜索
        return RedditResults(controller: controller);
      default:
        return SearchResults(controller: controller);
    }
  }

  /// 注册新的搜索类型
  /// 实际上这个方法不需要实现，但为了后续扩展，提供一个接口
  static void registerSearchType(
      int type, Widget Function(app_search.SearchController) builder) {
    // 这里可以使用Map存储不同的构建器，但考虑到目前使用switch已经足够，暂不实现
    // 如果搜索类型增加到很多，可以考虑使用Map来存储不同类型的构建器
  }
}
