import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';

class ArticleItem extends StatefulWidget {
  final Map<String, dynamic> article;
  final Map<String, dynamic> feed;
  final app_search.SearchController controller;

  const ArticleItem({
    Key? key,
    required this.article,
    required this.feed,
    required this.controller,
  }) : super(key: key);

  @override
  State<ArticleItem> createState() => _ArticleItemState();
}

class _ArticleItemState extends State<ArticleItem> {
  List<String> _imageUrls = [];
  Set<String> _failedImages = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _extractImagesFromDescription();
  }

  /// 从文章描述中提取图片URL
  void _extractImagesFromDescription() {
    setState(() {
      _isLoading = true;
    });
    final description = widget.article['description'] ?? '';
    final RegExp imgRegExp =
        RegExp(r'<img[^>]+src="([^"]+)"[^>]*>', caseSensitive: false);
    final matches = imgRegExp.allMatches(description);

    final List<String> urls = [];
    for (final match in matches) {
      final url = match.group(1);
      if (url != null && url.isNotEmpty) {
        urls.add(url);
        if (urls.length >= 4) break; // 最多4张图片
      }
    }

    setState(() {
      _isLoading = false;
      _imageUrls = urls;
    });
  }

  /// 处理图片加载失败
  void _onImageLoadFailed(String imageUrl) {
    if (mounted && !_failedImages.contains(imageUrl)) {
      setState(() {
        _failedImages.add(imageUrl);
      });
    }
  }

  /// 获取有效的图片URL列表（排除加载失败的图片）
  List<String> get _validImageUrls {
    return _imageUrls.where((url) => !_failedImages.contains(url)).toList();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // 如果是骨架屏数据，不执行任何操作
        if (widget.feed['isSkeleton'] == true ||
            widget.article['isSkeleton'] == true) {
          return;
        }

        // 设置文章为已读
        widget.controller.setRead(
          widget.article['id'].toString(),
          widget.feed['id'].toString(),
          widget.feed['suffixTable'] ?? widget.article['suffixTable'] ?? '',
        );
        // print('feed[img]: ${widget.feed['img']}'); // 注释掉生产环境的print
        // 跳转到文章详情页
        Get.toNamed(Routes.ARTICLE, arguments: {
          'article': {
            'id': widget.article['id'],
            'feedsId': widget.feed['id'],
            'suffixTable': widget.article['suffixTable'],
            'title': widget.article['title'],
            'link': widget.article['link'],
            'description': widget.article['description'],
            'pubDate': widget.article['pubDate'],
            'createTime': widget.article['createTime'],
            'creator': widget.feed['name'],
            'img': widget.feed['img'],
            'feedsName': widget.feed['name'],
            'isCollect': widget.article['isCollect'],
            'isLaterRead': widget.article['isLaterRead'],
          }
        });
      },
      child: Container(
        width: MediaQuery.of(context).size.width,
        margin: const EdgeInsets.only(right: 6),
        padding: EdgeInsets.zero,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            // 上方：横向滚动的图片列表
            _buildImageList(context),
            // 下方：feeds标题
            _buildFeedTitle(context),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _buildFeedTime(context),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建横向滚动的图片列表
  Widget _buildImageList(BuildContext context) {
    final validUrls = _validImageUrls;
    final isSkeleton = widget.feed['isSkeleton'] == true ||
        widget.article['isSkeleton'] == true;

    // 如果是骨架屏模式，总是显示骨架屏
    if (isSkeleton) {
      return Container(
        height: 58.spx,
        margin: EdgeInsets.all(8.spx),
        child: _buildSkeletonImageList(context),
      );
    }

    // 如果正在加载，显示骨架屏
    if (_isLoading) {
      return Container(
        height: 58.spx,
        margin: EdgeInsets.all(8.spx),
        child: _buildSkeletonImageList(context),
      );
    }

    // 如果没有有效图片，返回空的SizedBox
    if (validUrls.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 58.spx,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: validUrls.length,
        itemBuilder: (context, index) {
          return _buildImageItem(context, validUrls[index]);
        },
      ),
    );
  }

  /// 构建单个图片项
  Widget _buildImageItem(BuildContext context, String imageUrl) {
    return Container(
      width: 71.spx,
      height: 58.spx,
      margin: EdgeInsets.only(right: 16.spx),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4.spx),
        child: CachedNetworkImage(
          imageUrl: imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            width: 71.spx,
            height: 58.spx,
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color.fromRGBO(97, 97, 97, 1)
                  : const Color.fromRGBO(224, 224, 224, 1),
              borderRadius: BorderRadius.circular(4.spx),
            ),
            child: LoadingIndicator(size: 80.spx),
          ),
          errorWidget: (context, error, stackTrace) {
            // 图片加载失败时，标记为失败并返回空组件
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _onImageLoadFailed(imageUrl);
            });
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  /// 构建骨架屏图片列表
  Widget _buildSkeletonImageList(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      effect: ShimmerEffect(
        baseColor: Theme.of(context).brightness == Brightness.dark
            ? const Color.fromRGBO(97, 97, 97, 1)
            : const Color.fromRGBO(224, 224, 224, 1),
        highlightColor: Theme.of(context).brightness == Brightness.dark
            ? const Color.fromRGBO(97, 97, 97, 1)
            : const Color.fromRGBO(224, 224, 224, 1),
        duration: const Duration(seconds: 1),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 4, // 显示4个骨架屏占位符
        itemBuilder: (context, index) {
          return Container(
            width: 71.spx,
            height: 58.spx,
            margin: EdgeInsets.only(right: 8.spx),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color.fromRGBO(97, 97, 97, 1)
                  : const Color.fromRGBO(224, 224, 224, 1),
              borderRadius: BorderRadius.circular(4.spx),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFeedTitle(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 4.spx, bottom: 8.spx),
      child: Text(
        widget.article['title'] ?? '',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 13.spx,
              fontWeight: FontWeight.w600,
            ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildFeedTime(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 0.spx, bottom: 8.spx),
      child: Text(
        formatTime(widget.article['pubDate']),
        textAlign: TextAlign.right,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: 10.spx,
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFFD1D1D1)
                : const Color(0xFF999999)),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  String formatTime(dynamic time) {
    try {
      if (time == null) return '';

      DateTime dateTime;
      if (time is String) {
        // 处理pubDate格式
        dateTime = DateTime.parse(time);
      } else if (time is int) {
        // 处理createTime时间戳
        dateTime = DateTime.fromMillisecondsSinceEpoch(time * 1000);
      } else {
        return '';
      }

      final now = DateTime.now();
      final difference = now.difference(dateTime);
      if (difference.inHours < 3) {
        return 'just_now'.tr;
      }
      // 1天以内，返回小时
      if (difference.inDays < 1) {
        final hours = difference.inHours;
        return '${hours}H';
      }
      // 30天以内，返回天数
      else if (difference.inDays < 30) {
        return '${difference.inDays}D';
      }
      // 超过30天，返回完整日期
      else {
        return DateFormat('yyyy-MM-dd').format(dateTime);
      }
    } catch (e) {
      debugPrint('时间格式化失败: $e');
      return '';
    }
  }
}
