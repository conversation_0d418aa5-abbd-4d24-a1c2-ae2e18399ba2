import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;
import 'package:subfeeds/app/presentation/screens/search/searchWidget/empty_results.dart';
import 'package:subfeeds/app/presentation/screens/search/searchWidget/feed_item.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

class TelegramResults extends StatelessWidget {
  final app_search.SearchController controller;

  const TelegramResults({super.key, required this.controller});

  static Color? getSkeletonColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color.fromRGBO(97, 97, 97, 1)
        : const Color.fromRGBO(224, 224, 224, 1);
  }

  /// 生成骨架屏数据
  List<Map<String, dynamic>> generateSkeletonItems(int count) {
    return List.generate(
        count,
        (index) => {
              'id': 'skeleton_$index',
              'name': 'Telegram Channel Loading ${index + 1}',
              'description':
                  'This is a loading description for Telegram channel that will be replaced with real content when the data loads from the server.',
              'url': 'https://t.me/channel_$index',
              'website': 'https://t.me',
              'category': 'Telegram',
              'type': 'telegram',
              'isSub': 0,
              'img': '',
              'createTime': DateTime.now().millisecondsSinceEpoch ~/ 1000,
              'articleList': [
                {
                  'id': 'message_${index}_1',
                  'title': 'Telegram Message Loading...',
                  'summary': 'Message summary loading placeholder text...',
                  'description':
                      'This is a skeleton telegram message description with sample content for loading display.',
                  'publishTime': DateTime.now().millisecondsSinceEpoch,
                  'img': '',
                  'isSkeleton': true,
                },
              ],
              'isSkeleton': true, // 标记为骨架屏数据
            });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.searchResults.isEmpty) {
        return const EmptyResults();
      }

      return Column(
        children: [
          // 结果列表
          Expanded(
            child: NotificationListener<ScrollNotification>(
              onNotification: (ScrollNotification scrollInfo) {
                // 当滚动到距离底部还有20%的位置时，提前加载更多
                if (!controller.isLoadingMore.value &&
                    controller.hasMoreData.value &&
                    scrollInfo.metrics.pixels > 0 &&
                    scrollInfo.metrics.pixels >=
                        scrollInfo.metrics.maxScrollExtent * 0.8) {
                  controller.loadMore();
                }
                return true;
              },
              child: ListView.builder(
                itemCount: controller.searchResults.length +
                    (controller.isLoadingMore.value ||
                            !controller.hasMoreData.value
                        ? 1
                        : 0),
                itemBuilder: (context, index) {
                  // 显示加载更多指示器或"No More"提示
                  if (index == controller.searchResults.length) {
                    if (controller.isLoadingMore.value) {
                      // 使用骨架屏替代CircularProgressIndicator
                      final skeletonData = generateSkeletonItems(3);
                      final baseColor = getSkeletonColor(context);
                      return Column(
                        children: skeletonData.map((feed) {
                          return Skeletonizer(
                            enabled: true,
                            effect: ShimmerEffect(
                              baseColor: baseColor ?? Colors.grey[300]!,
                              highlightColor:
                                  const Color.fromRGBO(245, 245, 245, 1),
                              duration: const Duration(seconds: 1),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child:
                                  FeedItem(feed: feed, controller: controller),
                            ),
                          );
                        }).toList(),
                      );
                    } else if (!controller.hasMoreData.value) {
                      return Container(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        child: Center(
                          child: Text(
                            'home_no_more'.tr,
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 14,
                            ),
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  }

                  // 显示搜索结果项
                  final feed = controller.searchResults[index];
                  return Padding(
                    padding: EdgeInsets.only(bottom: 6.5.spx),
                    child: FeedItem(feed: feed, controller: controller),
                  );
                },
              ),
            ),
          ),
        ],
      );
    });
  }
}
