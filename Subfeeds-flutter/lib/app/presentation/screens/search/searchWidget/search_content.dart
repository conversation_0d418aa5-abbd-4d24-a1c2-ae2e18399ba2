import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;
import 'package:subfeeds/app/presentation/screens/search/searchWidget/search_factory.dart';

class SearchContent extends StatelessWidget {
  final app_search.SearchController controller;

  const SearchContent({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Card(
        elevation: 0,
        color: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Obx(() {
          // 加载中状态
          if (controller.isLoading.value &&
              (controller.searchResults.isEmpty &&
                  controller.googleNewsList.isEmpty)) {
            return const Center(child: CircularProgressIndicator());
          }

          // 使用工厂类获取对应类型的搜索结果组件
          return SearchFactory.getSearchResults(controller);
        }),
      ),
    );
  }
}
