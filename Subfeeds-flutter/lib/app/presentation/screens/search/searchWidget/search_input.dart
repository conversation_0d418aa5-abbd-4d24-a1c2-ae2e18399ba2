import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;

class SearchInput extends StatelessWidget {
  final app_search.SearchController controller;

  const SearchInput({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 4, 16, 4),
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: TextField(
          controller: controller.searchQueryController,
          onChanged: (value) {
            controller.searchQuery.value = value;
            if (value.isEmpty && controller.searchType.value == 2) {
              controller.resetPage();
              controller.search();
            }
          },
          decoration: InputDecoration(
            isDense: true,
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
            filled: true,
            fillColor: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF1c1e31)
                : const Color(0xFFf0f1f7),
            hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Color(0XFF73839d),
                ),
            prefixIcon: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6.0),
              child: SvgPicture.asset(
                'assets/feeds/feeds_search.svg',
                width: 20,
                height: 20,
                colorFilter: ColorFilter.mode(
                  Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFFb4bac5)
                      : const Color(0xFFb4bac5),
                  BlendMode.srcIn,
                ),
              ),
            ),
            prefixIconConstraints: const BoxConstraints(
              minWidth: 36,
              maxHeight: 36,
            ),
            hintText: 'search_hint'.tr,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 1,
              ),
            ),
          ),
          onSubmitted: (_) {
            controller.resetPage();
            controller.search();
          },
          textInputAction: TextInputAction.search,
        ),
      ),
    );
  }
}
