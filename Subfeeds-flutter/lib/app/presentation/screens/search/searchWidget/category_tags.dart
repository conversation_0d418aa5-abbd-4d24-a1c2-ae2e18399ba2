import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/presentation/screens/search/search_controller.dart'
    as app_search;

class CategoryTags extends StatelessWidget {
  final app_search.SearchController controller;

  const CategoryTags({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.searchType.value == 2 &&
          controller.subCategories.isNotEmpty) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          margin: const EdgeInsets.only(bottom: 8),
          height: 36,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: controller.subCategories.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                return _buildCategoryTag(
                  context,
                  'All',
                  null,
                  controller.selectedSubCategory.value == null,
                );
              }

              final category = controller.subCategories[index - 1];
              return _buildCategoryTag(
                context,
                controller.isEnglish.value
                    ? category['name'] ?? ''
                    : category['nameCn'] ?? '',
                category['id'] as String?,
                controller.selectedSubCategory.value == category['id'],
              );
            },
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  Widget _buildCategoryTag(
    BuildContext context,
    String name,
    String? categoryId,
    bool isSelected,
  ) {
    return Obx(() => Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: InkWell(
            onTap: () => controller.selectSubCategory(categoryId),
            borderRadius: BorderRadius.circular(2),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: controller.selectedSubCategory.value == categoryId
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFF212534)
                        : Color(0xfff0f1f7),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                name,
                style: TextStyle(
                  color: controller.selectedSubCategory.value == categoryId
                      ? Colors.white
                      : Theme.of(context).textTheme.bodyMedium?.color,
                  fontSize: 13,
                  fontWeight: controller.selectedSubCategory.value == categoryId
                      ? FontWeight.w600
                      : FontWeight.normal,
                ),
              ),
            ),
          ),
        ));
  }
}
