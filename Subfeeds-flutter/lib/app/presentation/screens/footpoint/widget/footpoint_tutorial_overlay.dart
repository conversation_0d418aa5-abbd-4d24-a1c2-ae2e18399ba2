import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 足迹页面新手引导遮罩层
class FootpointTutorialOverlay extends StatefulWidget {
  final VoidCallback onDismiss;
  final bool isDarkMode;

  const FootpointTutorialOverlay({
    super.key,
    required this.onDismiss,
    required this.isDarkMode,
  });

  @override
  State<FootpointTutorialOverlay> createState() =>
      _FootpointTutorialOverlayState();
}

class _FootpointTutorialOverlayState extends State<FootpointTutorialOverlay>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _pulseController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    // 淡入动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // 滑动提示动画控制器
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    // 脉冲动画控制器
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // 淡入动画 - 使用更平滑的曲线
    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOutCubic,
    );

    // 滑动动画 - 更自然的滑动效果
    _slideAnimation = Tween<Offset>(
      begin: const Offset(-0.4, 0),
      end: const Offset(0.4, 0),
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOutSine,
    ));

    // 脉冲动画 - 更柔和的脉冲效果
    _pulseAnimation = Tween<double>(
      begin: 0.9,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOutSine,
    ));
  }

  void _startAnimations() {
    // 开始淡入动画
    _fadeController.forward();

    // 延迟开始滑动和脉冲动画
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _slideController.repeat(reverse: true);
        _pulseController.repeat(reverse: true);
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _handleDismiss() {
    _fadeController.reverse().then((_) {
      if (mounted) {
        widget.onDismiss();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Material(
        color: Colors.black.withValues(alpha: 0.8),
        child: GestureDetector(
          onTap: _handleDismiss,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Stack(
              children: [
                // 主要内容区域
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 手势动画指示器
                      _buildGestureIndicator(),

                      SizedBox(height: 40.spx),

                      // 说明文字
                      _buildInstructionText(),

                      SizedBox(height: 60.spx),

                      // 页面指示器
                      _buildPageIndicator(),
                    ],
                  ),
                ),

                // 关闭按钮
                _buildCloseButton(),

                // 底部按钮
                _buildBottomButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGestureIndicator() {
    return Container(
      width: 200.spx,
      height: 120.spx,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 手机屏幕框架
          Container(
            width: 180.spx,
            height: 100.spx,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(12.spx),
            ),
            child: Stack(
              children: [
                // 页面内容示意 - 优化主题适配
                Positioned(
                  left: 10.spx,
                  top: 10.spx,
                  child: Container(
                    width: 40.spx,
                    height: 60.spx,
                    decoration: BoxDecoration(
                      color: widget.isDarkMode
                          ? Colors.grey[800]!.withValues(alpha: 0.8)
                          : Colors.grey[200]!.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(4.spx),
                      border: Border.all(
                        color: widget.isDarkMode
                            ? Colors.grey[600]!.withValues(alpha: 0.5)
                            : Colors.grey[400]!.withValues(alpha: 0.5),
                        width: 0.5,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  right: 10.spx,
                  top: 10.spx,
                  child: Container(
                    width: 40.spx,
                    height: 60.spx,
                    decoration: BoxDecoration(
                      color: widget.isDarkMode
                          ? Colors.grey[700]!.withValues(alpha: 0.8)
                          : Colors.grey[300]!.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(4.spx),
                      border: Border.all(
                        color: widget.isDarkMode
                            ? Colors.grey[500]!.withValues(alpha: 0.5)
                            : Colors.grey[500]!.withValues(alpha: 0.5),
                        width: 0.5,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 滑动手势动画
          AnimatedBuilder(
            animation: _slideAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(_slideAnimation.value.dx * 50.spx, 0),
                child: AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        width: 30.spx,
                        height: 30.spx,
                        decoration: BoxDecoration(
                          color: widget.isDarkMode
                              ? Colors.white.withValues(alpha: 0.95)
                              : Colors.white.withValues(alpha: 0.95),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: widget.isDarkMode
                                  ? Colors.white.withValues(alpha: 0.4)
                                  : Colors.black.withValues(alpha: 0.2),
                              blurRadius: 12,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.touch_app,
                          color: widget.isDarkMode
                              ? Colors.black87
                              : Colors.black87,
                          size: 16.spx,
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionText() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.spx),
      child: Column(
        children: [
          Text(
            'tutorial_swipe_title'.tr,
            style: TextStyle(
              color: Colors.white,
              fontSize: 20.spx,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.spx),
          Text(
            'tutorial_swipe_description'.tr,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 14.spx,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildDot(true),
        SizedBox(width: 8.spx),
        _buildDot(false),
        SizedBox(width: 8.spx),
        _buildDot(false),
        SizedBox(width: 8.spx),
        _buildDot(false),
      ],
    );
  }

  Widget _buildDot(bool isActive) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Container(
          width: isActive ? 12.spx * _pulseAnimation.value : 8.spx,
          height: isActive ? 12.spx * _pulseAnimation.value : 8.spx,
          decoration: BoxDecoration(
            color:
                isActive ? Colors.white : Colors.white.withValues(alpha: 0.4),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }

  Widget _buildCloseButton() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16.spx,
      right: 16.spx,
      child: GestureDetector(
        onTap: _handleDismiss,
        child: Container(
          width: 36.spx,
          height: 36.spx,
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.3),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.close,
            color: Colors.white,
            size: 20.spx,
          ),
        ),
      ),
    );
  }

  Widget _buildBottomButton() {
    return Positioned(
      bottom: MediaQuery.of(context).padding.bottom + 40.spx,
      left: 40.spx,
      right: 40.spx,
      child: ElevatedButton(
        onPressed: _handleDismiss,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 16.spx),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25.spx),
          ),
        ),
        child: Text(
          'tutorial_got_it'.tr,
          style: TextStyle(
            fontSize: 16.spx,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
