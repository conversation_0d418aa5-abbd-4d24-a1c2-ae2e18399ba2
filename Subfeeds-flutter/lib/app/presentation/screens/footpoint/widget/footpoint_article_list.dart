import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:subfeeds/app/presentation/widgets/article_item.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/widget/footpoint_page_data.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/routes/app_pages.dart';
import 'package:subfeeds/app/data/repositories/article_repository.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/footpoint_screen.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/share_bottom_sheet.dart';

/// 足迹页面文章列表组件
class FootpointArticleList extends StatelessWidget {
  final FootpointPageData pageData;
  final FootpointType type;
  final Future<void> Function() onRefresh;
  final Function(dynamic) onMarkAsRead;
  final Function() onRemoveFromStarred;
  final Function() onRemoveFromReadLater;

  const FootpointArticleList({
    super.key,
    required this.pageData,
    required this.type,
    required this.onRefresh,
    required this.onMarkAsRead,
    required this.onRemoveFromStarred,
    required this.onRemoveFromReadLater,
  });

  @override
  Widget build(BuildContext context) {
    final isFirstTimeLoading = pageData.articles.isEmpty && pageData.isLoading;
    final articlesCount = pageData.articles.length;
    final skeletonCount = pageData.isLoadingMore ? pageData.pageSize : 0;
    final totalItemCount = isFirstTimeLoading
        ? 8 // 首次加载显示8个骨架屏条目
        : articlesCount +
            skeletonCount +
            (pageData.hasMore || pageData.isLoadingMore ? 0 : 1);

    return Container(
      margin: const EdgeInsets.fromLTRB(0, 0, 0, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF3b3b3b)
            : Colors.white,
      ),
      child: RefreshIndicator(
        onRefresh: onRefresh,
        color: Theme.of(context).colorScheme.primary,
        child: ListView.builder(
          controller: pageData.scrollController,
          padding: EdgeInsets.all(10.spx),
          itemCount: totalItemCount,
          itemBuilder: (context, index) {
            return _buildListItem(context, index, isFirstTimeLoading,
                articlesCount, skeletonCount);
          },
        ),
      ),
    );
  }

  Widget _buildListItem(BuildContext context, int index,
      bool isFirstTimeLoading, int articlesCount, int skeletonCount) {
    // 首次加载时显示骨架屏
    if (isFirstTimeLoading) {
      return _buildSkeletonItem(context);
    }

    // 显示实际文章
    if (index < articlesCount) {
      final article = pageData.articles[index];
      return _buildArticleItem(context, article);
    }

    // 显示加载更多的骨架屏
    if (pageData.isLoadingMore &&
        index >= articlesCount &&
        index < articlesCount + skeletonCount) {
      return _buildSkeletonItem(context);
    }

    // 显示"没有更多"指示器
    if (!pageData.hasMore && !pageData.isLoadingMore) {
      return _buildNoMoreIndicator(context);
    }

    return const SizedBox.shrink();
  }

  static Color? getSkeletonColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color.fromRGBO(97, 97, 97, 1)
        : const Color.fromRGBO(224, 224, 224, 1);
  }

  Widget _buildSkeletonItem(BuildContext context) {
    final skeletonArticle = pageData.generateSkeletonItems(1)[0];
    final baseColor = getSkeletonColor(context);
    return Skeletonizer(
      enabled: true,
      effect: ShimmerEffect(
        baseColor: baseColor ?? Colors.grey[300]!,
        highlightColor: Color.fromRGBO(245, 245, 245, 1),
        duration: Duration(seconds: 1),
      ),
      child: ArticleItem(
        article: skeletonArticle,
        feed: {
          'feedsName': skeletonArticle['feedsName'] ?? '未知来源',
          'img': skeletonArticle['img'] ?? '',
        },
        isDarkMode: Theme.of(context).brightness == Brightness.dark,
        isShowLaterRead: type == FootpointType.readLater,
        isShowBookmark: type == FootpointType.starred,
        onTap: () {},
        onBookmark: () {},
        onReadLater: () {},
      ),
    );
  }

  Widget _buildArticleItem(BuildContext context, Map<String, dynamic> article) {
    return ArticleItem(
      article: article,
      feed: {
        'feedsName': article['feedsName'] ?? '未知来源',
        'img': article['feedsImg'] ?? '',
      },
      isDarkMode: Theme.of(context).brightness == Brightness.dark,
      isShowLaterRead: type == FootpointType.readLater,
      isShowBookmark: type == FootpointType.starred,
      onTap: () => _handleArticleTap(context, article),
      onBookmark: () => _handleBookmark(context, article),
      onReadLater: () => _handleReadLater(context, article),
    );
  }

  Widget _buildNoMoreIndicator(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'no_more'.tr,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 14),
        ),
      ),
    );
  }

  void _handleArticleTap(BuildContext context, Map<String, dynamic> article) {
    if (article['isSkeleton'] == true) return;

    final articleId = article['articleId'];

    // 如果是收藏或稍后阅读列表，需要标记为已读
    if (type == FootpointType.starred || type == FootpointType.readLater) {
      onMarkAsRead(article);
    }

    // 构建文章详情所需的参数
    final articleData = {
      'id': articleId,
      'title': article['title'] ?? '无标题',
      'pubDate': article['pubDate'],
      'createTime': article['createTime'],
      'creator': article['creator'],
      'description': article['description'] ?? article['content'] ?? '暂无内容',
      'link': article['link'] ?? article['url'] ?? '',
      'language': article['language'] ?? 'en',
      'feedsName': article['feedsName'] ?? article['creator'] ?? '未知来源',
      'isCollect': article['isCollect'] ?? 0,
      'isLaterRead': article['isLaterRead'] ?? 0,
      'img': article['feedsImg'] ?? '',
      'isRead': article['isRead'] ?? 0,
      'suffixTable': article['suffixTable'] ?? '',
      'feedsId': article['feedsId'] ?? '',
    };

    Get.toNamed(
      Routes.ARTICLE,
      arguments: {'article': articleData},
    );
  }

  Future<void> _handleBookmark(
      BuildContext context, Map<String, dynamic> article) async {
    if (article['isSkeleton'] == true) return;

    final int articleId = int.parse(article['articleId'].toString());
    final bool isCurrentlyCollect = article['isCollect'] == 1;

    try {
      final response = isCurrentlyCollect
          ? await ArticleRepository().deleteCollect([articleId])
          : await ArticleRepository().insertCollect(
              articleId,
              int.tryParse(article['feedsId'].toString()) ?? -1,
              article['suffixTable'] ?? '');

      if (response.isSuccess) {
        article['isCollect'] = isCurrentlyCollect ? 0 : 1;

        // 如果当前页面是收藏页且用户取消了收藏，则从列表中移除该项
        if (type == FootpointType.starred && isCurrentlyCollect) {
          onRemoveFromStarred();
        }
      } else {
        _showErrorSnackbar('error'.tr, response.msg);
      }
    } catch (e) {
      debugPrint('收藏操作失败: $e');
      _showErrorSnackbar('error'.tr, 'operation_failed'.tr);
    }
  }

  Future<void> _handleReadLater(
      BuildContext context, Map<String, dynamic> article) async {
    if (article['isSkeleton'] == true) return;

    final int articleId = int.parse(article['articleId'].toString());
    final bool isCurrentlyLaterRead = article['isLaterRead'] == 1;

    try {
      final response = isCurrentlyLaterRead
          ? await ArticleRepository().deleteLaterRead([articleId])
          : await ArticleRepository().setReadLater(
              articleId,
              int.tryParse(article['feedsId'].toString()) ?? -1,
              article['suffixTable'] ?? '');

      if (response.isSuccess) {
        article['isLaterRead'] = isCurrentlyLaterRead ? 0 : 1;

        // 如果当前页面是稍后阅读页且用户取消了稍后阅读，则从列表中移除该项
        if (type == FootpointType.readLater && isCurrentlyLaterRead) {
          onRemoveFromReadLater();
        }
      } else {
        Get.snackbar('error'.tr, response.msg,
            snackPosition: SnackPosition.TOP);
      }
    } catch (e) {
      debugPrint('稍后阅读操作失败: $e');
      _showErrorSnackbar('error'.tr, 'operation_failed'.tr);
    }
  }

  void _showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      icon: const Icon(Icons.error, color: Colors.red),
      backgroundColor: const Color(0xFF161617),
      colorText: Colors.white,
    );
  }
}
