import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';

/// 足迹页面搜索栏组件
class FootpointSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final Function(String) onChanged;
  final VoidCallback onClear;
  final bool isVisible;
  final Animation<double> animation;

  const FootpointSearchBar({
    super.key,
    required this.controller,
    required this.onChanged,
    required this.onClear,
    required this.isVisible,
    required this.animation,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return ClipRect(
          child: SizeTransition(
            sizeFactor: animation,
            axisAlignment: -1.0,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, -1),
                end: Offset.zero,
              ).animate(animation),
              child: FadeTransition(
                opacity: animation,
                child: _buildSearch<PERSON>ield(context),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchField(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 10.spx, left: 10.spx, right: 10.spx),
      child: TextField(
        controller: controller,
        onChanged: onChanged,
        decoration: InputDecoration(
          hintText: 'search_feeds_placeholder'.tr,
          prefixIcon: Icon(
            Icons.search,
            color: const Color(0xFF7F8EA7),
            size: 20.spx,
          ),
          suffixIcon: controller.text.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: const Color(0xFF7F8EA7),
                    size: 20.spx,
                  ),
                  onPressed: onClear,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.spx),
            borderSide: BorderSide(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF434756)
                  : const Color(0xFFE0E0E0),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.spx),
            borderSide: BorderSide(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF434756)
                  : const Color(0xFFE0E0E0),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.spx),
            borderSide: BorderSide(
              color: Theme.of(context).primaryColor,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF5B5B5B)
              : const Color(0xFFE5E9F1),
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.spx,
            vertical: 12.spx,
          ),
        ),
        style: TextStyle(
          fontSize: 14.spx,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white
              : const Color(0xFF333333),
        ),
      ),
    );
  }
}
