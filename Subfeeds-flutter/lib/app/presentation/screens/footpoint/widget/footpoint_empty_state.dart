import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 足迹页面空状态组件
class FootpointEmptyState extends StatelessWidget {
  final bool isSearching;

  const FootpointEmptyState({
    super.key,
    required this.isSearching,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/article_empty.png',
            width: 200,
            height: 200,
          ),
          const SizedBox(height: 16),
          Text(
            isSearching ? 'no_results'.tr : 'Um... There\'s nothing here',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontSize: 16,
                ),
          ),
          if (isSearching) ...[
            const SizedBox(height: 8),
            Text(
              'Try different keywords',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontSize: 14,
                    color: const Color(0xFF7F8EA7),
                  ),
            ),
          ],
        ],
      ),
    );
  }
}
