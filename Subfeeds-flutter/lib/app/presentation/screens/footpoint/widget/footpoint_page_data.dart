import 'dart:async';
import 'package:flutter/material.dart';

/// 单个页面的数据管理类
class FootpointPageData {
  final List<Map<String, dynamic>> articles = [];
  final ScrollController scrollController = ScrollController();
  bool isLoading = false;
  bool isRefreshing = false;
  bool hasMore = true;
  int total = 0;
  int currentPage = 1;
  final int pageSize = 15;

  // 添加防抖计时器
  Timer? debounceTimer;
  static const debounceDuration = Duration(milliseconds: 500);

  // 添加请求标识变量
  int requestId = 0;

  // 添加已读文章ID集合
  final Set<int> readArticleIds = {};

  // 搜索相关状态
  String searchKeyword = '';
  bool isSearching = false;
  final TextEditingController searchController = TextEditingController();
  Timer? searchDebounceTimer;

  // 骨架屏相关状态
  bool isLoadingMore = false;
  List<Map<String, dynamic>> skeletonItems = [];

  void dispose() {
    scrollController.dispose();
    debounceTimer?.cancel();
    searchController.dispose();
    searchDebounceTimer?.cancel();
  }

  // 清空搜索
  void clearSearch() {
    searchKeyword = '';
    searchController.clear();
    isSearching = false;
    searchDebounceTimer?.cancel();
  }

  // 生成骨架屏数据
  List<Map<String, dynamic>> generateSkeletonItems(int count) {
    return List.generate(
        count,
        (index) => {
              'articleId': 'skeleton_$index',
              'title':
                  'This is a skeleton article title that will be replaced with real content',
              'description':
                  'This is a skeleton description that will be replaced with real content when the data loads from the server',
              'feedsName': 'Skeleton Feed Name',
              'pubDate': DateTime.now().toIso8601String(),
              'createTime': DateTime.now().toIso8601String(),
              'creator': 'Skeleton Author',
              'link': '',
              'img': '',
              'isCollect': 0,
              'isLaterRead': 0,
              'isRead': 0,
              'feedsId': 'skeleton_feed_$index',
              'suffixTable': '',
              'isSkeleton': true, // 标记为骨架屏数据
            });
  }

  // 重置页面数据
  void reset() {
    articles.clear();
    currentPage = 1;
    hasMore = true;
    total = 0;
    requestId++;
  }
}
