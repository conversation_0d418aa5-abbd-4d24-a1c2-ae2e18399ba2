import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:subfeeds/app/data/repositories/article_repository.dart';
import 'package:subfeeds/app/data/models/api_response.dart';
import 'dart:async';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/widget/footpoint_search_bar.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/widget/footpoint_empty_state.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/widget/footpoint_article_list.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/widget/footpoint_page_data.dart';
import 'package:subfeeds/app/presentation/screens/footpoint/widget/footpoint_tutorial_overlay.dart';
import 'package:subfeeds/app/data/services/tutorial_service.dart';

enum FootpointType {
  starred,
  readLater,
  history,
  annotated,
}

class FootpointScreen extends StatefulWidget {
  final FootpointType? initialType;

  const FootpointScreen({
    super.key,
    this.initialType,
  });

  @override
  State<FootpointScreen> createState() => _FootpointScreenState();
}

class _FootpointScreenState extends State<FootpointScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late TabController _tabController;
  late AnimationController _searchAnimationController;
  late Animation<double> _searchAnimation;

  int _currentIndex = 0;
  bool _isSearchMode = false;
  bool _showTutorial = false;
  final Map<FootpointType, FootpointPageData> _pageDataMap = {};

  static const List<FootpointType> _pageTypes = [
    FootpointType.starred,
    FootpointType.readLater,
    FootpointType.history,
    FootpointType.annotated,
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializePageData();
    _setupInitialPage();
    _setupScrollListeners();
    _loadCurrentPageData();
    _checkTutorial();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _tabController.dispose();
    _searchAnimationController.dispose();
    for (final pageData in _pageDataMap.values) {
      pageData.dispose();
    }
    super.dispose();
  }

  void _initializeControllers() {
    _pageController = PageController();
    _tabController = TabController(length: _pageTypes.length, vsync: this);
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _searchAnimation = CurvedAnimation(
      parent: _searchAnimationController,
      curve: Curves.easeInOut,
    );
  }

  void _initializePageData() {
    for (final type in _pageTypes) {
      _pageDataMap[type] = FootpointPageData();
    }
  }

  void _setupInitialPage() {
    if (widget.initialType != null) {
      _currentIndex = _pageTypes.indexOf(widget.initialType!);
      _pageController = PageController(initialPage: _currentIndex);
      _tabController = TabController(
          length: _pageTypes.length, vsync: this, initialIndex: _currentIndex);
    }
  }

  void _setupScrollListeners() {
    for (final entry in _pageDataMap.entries) {
      final type = entry.key;
      final pageData = entry.value;
      pageData.scrollController.addListener(() => _onScroll(pageData, type));
    }
  }

  FootpointType get _currentPageType => _pageTypes[_currentIndex];
  FootpointPageData get _currentPageData => _pageDataMap[_currentPageType]!;

  void _loadCurrentPageData() {
    final pageData = _currentPageData;
    if (pageData.articles.isEmpty && !pageData.isLoading) {
      _loadData(pageData, _currentPageType);
    }
  }

  void _onScroll(FootpointPageData pageData, FootpointType type) {
    if (!pageData.scrollController.hasClients) return;

    final maxScroll = pageData.scrollController.position.maxScrollExtent;
    final currentScroll = pageData.scrollController.offset;

    if (currentScroll >= maxScroll * 0.9 &&
        !pageData.isLoading &&
        pageData.hasMore) {
      _loadData(pageData, type);
    }
  }

  Future<void> _loadData(FootpointPageData pageData, FootpointType type) async {
    if (pageData.isLoading || !pageData.hasMore) return;

    final isLoadingMore = pageData.currentPage > 1;

    setState(() {
      pageData.isLoading = true;
      if (isLoadingMore) {
        pageData.isLoadingMore = true;
      }
    });

    final localRequestId = ++pageData.requestId;

    try {
      final params = <String, dynamic>{
        'pageNum': pageData.currentPage,
        'pageSize': pageData.pageSize,
      };

      if (pageData.searchKeyword.isNotEmpty) {
        params['feedsName'] = pageData.searchKeyword;
      }

      final response = await _getDataByType(params, type);

      if (localRequestId != pageData.requestId) return;

      if (response.isSuccess && response.data != null) {
        final responseData = response.data;
        final data = responseData?['data'];
        final pageList = data?['pageList'] as List? ?? [];
        final total = data?['total'] as int? ?? 0;

        if (localRequestId != pageData.requestId) return;

        setState(() {
          if (pageData.currentPage == 1) {
            pageData.articles.clear();
          }

          pageData.articles.addAll(pageList.cast<Map<String, dynamic>>());
          pageData.total = total;
          pageData.currentPage++;
          pageData.hasMore = pageData.articles.length < total;
        });
      }
    } catch (e, stackTrace) {
      if (localRequestId == pageData.requestId) {
        debugPrint('加载数据失败: $e');
        debugPrint('错误堆栈: $stackTrace');
      }
    } finally {
      if (localRequestId == pageData.requestId && mounted) {
        setState(() {
          pageData.isLoading = false;
          pageData.isLoadingMore = false;
        });
      }
    }
  }

  Future<void> _refreshData() async {
    final pageData = _currentPageData;
    final type = _currentPageType;

    if (pageData.isRefreshing) return;

    pageData.debounceTimer?.cancel();

    setState(() {
      pageData.isRefreshing = true;
      pageData.currentPage = 1;
      pageData.hasMore = true;
    });

    final localRequestId = ++pageData.requestId;

    try {
      await Future.delayed(FootpointPageData.debounceDuration);

      if (!mounted || localRequestId != pageData.requestId) return;

      await _loadData(pageData, type);

      if (localRequestId == pageData.requestId && mounted) {
        Get.closeAllSnackbars();
        Get.snackbar(
          'notice'.tr,
          'refresh_success_msg'.tr,
          icon: SvgPicture.asset('assets/feeds/right.svg'),
          backgroundColor: const Color(0xFF161617),
          colorText: Colors.white,
          duration: const Duration(seconds: 1),
        );
      }
    } catch (e) {
      debugPrint('刷新数据失败: $e');
    } finally {
      if (mounted && localRequestId == pageData.requestId) {
        setState(() => pageData.isRefreshing = false);
      }
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> _getDataByType(
      Map<String, dynamic> params, FootpointType type) {
    switch (type) {
      case FootpointType.starred:
        return ArticleRepository().getCollectList(params);
      case FootpointType.readLater:
        return ArticleRepository().getLaterReadList(params);
      case FootpointType.history:
        return ArticleRepository().getReadRecordList(params);
      case FootpointType.annotated:
        return ArticleRepository().getUserNoteList(params);
    }
  }

  Future<void> _markArticleAsRead(dynamic article) async {
    final int? id = int.tryParse(article['articleId'].toString());
    if (id == null) return;

    final pageData = _currentPageData;
    if (pageData.readArticleIds.contains(id)) return;

    try {
      final response = await ArticleRepository().insertHistory(
        id,
        int.tryParse(article['feedsId'].toString()) ?? -1,
        article['suffixTable'] ?? '',
      );
      if (response.isSuccess) {
        pageData.readArticleIds.add(id);
        final index = pageData.articles.indexWhere((a) => a['id'] == id);
        if (index != -1) {
          setState(() {
            pageData.articles[index]['isRead'] = 1;
          });
        }
      }
    } catch (e) {
      debugPrint('标记已读失败: $e');
    }
  }

  String get _screenTitle {
    switch (_currentPageType) {
      case FootpointType.starred:
        return 'feeds_starred'.tr;
      case FootpointType.readLater:
        return 'feeds_read_later'.tr;
      case FootpointType.history:
        return 'feeds_history'.tr;
      case FootpointType.annotated:
        return 'feeds_annotated'.tr;
    }
  }

  void _toggleSearchMode() {
    setState(() {
      _isSearchMode = !_isSearchMode;

      if (_isSearchMode) {
        _searchAnimationController.forward();
      } else {
        _searchAnimationController.reverse();
        for (var pageData in _pageDataMap.values) {
          if (pageData.searchKeyword.isNotEmpty) {
            pageData.clearSearch();
            pageData.reset();
          }
        }
      }
    });
  }

  void _performSearch(String keyword) {
    final pageData = _currentPageData;

    pageData.searchDebounceTimer?.cancel();
    pageData.searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          pageData.searchKeyword = keyword.trim();
          pageData.isSearching = keyword.trim().isNotEmpty;
        });

        pageData.reset();
        _loadData(pageData, _currentPageType);
      }
    });
  }

  void _onSearchClear() {
    final pageData = _currentPageData;
    pageData.searchController.clear();
    _performSearch('');
  }

  void _onPageChanged(int index) {
    if (mounted && _currentIndex != index) {
      setState(() {
        _currentIndex = index;
      });
      _tabController.animateTo(index);

      final pageData = _pageDataMap[_pageTypes[index]]!;
      if (pageData.articles.isEmpty && !pageData.isLoading) {
        _loadData(pageData, _pageTypes[index]);
      }
    }
  }

  void _onRemoveFromStarred() {
    final pageData = _currentPageData;
    setState(() {
      pageData.total--;
    });
  }

  void _onRemoveFromReadLater() {
    final pageData = _currentPageData;
    setState(() {
      pageData.total--;
    });
  }

  /// 检查是否需要显示新手引导
  Future<void> _checkTutorial() async {
    try {
      final tutorialShown =
          await TutorialService.instance.isFootpointTutorialShown();
      if (!tutorialShown && mounted) {
        // 延迟显示引导，确保页面完全加载
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            setState(() {
              _showTutorial = true;
            });
          }
        });
      }
    } catch (e) {
      debugPrint('检查新手引导失败: $e');
    }
  }

  /// 关闭新手引导
  void _dismissTutorial() {
    setState(() {
      _showTutorial = false;
    });
    // 标记引导已显示
    TutorialService.instance.markFootpointTutorialShown();
  }

  @override
  Widget build(BuildContext context) {
    final title = _screenTitle;
    final backgroundColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xff3b3b3b)
        : const Color(0xFFffffff);
    final appBarBackgroundColor =
        Theme.of(context).brightness == Brightness.dark
            ? const Color(0xff202020)
            : const Color(0xFFffffff);

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        backgroundColor: appBarBackgroundColor,
        iconTheme: IconThemeData(color: const Color(0xFF7F8EA7), size: 20.spx),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 16.spx,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : const Color(0xff333333),
              ),
        ),
        actions: [
          // 开发模式下添加重置引导按钮
          if (kDebugMode)
            IconButton(
              onPressed: () async {
                await TutorialService.instance.resetFootpointTutorial();
                setState(() {
                  _showTutorial = true;
                });
              },
              icon: Icon(
                Icons.help_outline,
                size: 20.spx,
                color: const Color(0xFF7F8EA7),
              ),
            ),
          IconButton(
            onPressed: _toggleSearchMode,
            icon: SvgPicture.asset(
              'assets/feeds/feeds_search.svg',
              width: 20.spx,
              height: 20.spx,
              colorFilter: ColorFilter.mode(
                _isSearchMode
                    ? Theme.of(context).primaryColor
                    : const Color(0xFF7F8EA7),
                BlendMode.srcIn,
              ),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          _buildBody(backgroundColor),
          // 新手引导遮罩层
          if (_showTutorial)
            FootpointTutorialOverlay(
              onDismiss: _dismissTutorial,
              isDarkMode: Theme.of(context).brightness == Brightness.dark,
            ),
        ],
      ),
    );
  }

  Widget _buildBody(Color backgroundColor) {
    return Container(
      decoration: BoxDecoration(color: backgroundColor),
      child: Column(
        children: [
          Divider(
            height: 1,
            thickness: 0.5,
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF434756)
                : const Color(0xffdfe3ff),
          ),
          FootpointSearchBar(
            controller: _currentPageData.searchController,
            onChanged: _performSearch,
            onClear: _onSearchClear,
            isVisible: _isSearchMode,
            animation: _searchAnimation,
          ),
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: _pageTypes.length,
              itemBuilder: (context, index) {
                final type = _pageTypes[index];
                final pageData = _pageDataMap[type]!;
                return _buildPageContent(pageData, type);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageContent(FootpointPageData pageData, FootpointType type) {
    if (pageData.articles.isEmpty && !pageData.isLoading) {
      return FootpointEmptyState(isSearching: pageData.isSearching);
    }

    return FootpointArticleList(
      pageData: pageData,
      type: type,
      onRefresh: _refreshData,
      onMarkAsRead: _markArticleAsRead,
      onRemoveFromStarred: _onRemoveFromStarred,
      onRemoveFromReadLater: _onRemoveFromReadLater,
    );
  }
}
