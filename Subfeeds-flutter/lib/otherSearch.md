# Google News 搜索
  ## 使用search_screen.dart 页面 
  ## 从discover_tab点击进去，并传递type = 4，
# 页面展示
 ## appbar：只显示back图标
 ## 搜索框
 ## rss源卡片：两侧布局
  - 左侧显示当前rss源：标题为google news，次级标题为（Focus on quality content with the powerful google news News feed））
  - 右侧显示对应源logo 
   - google news logo路径：assets/share/google.svg
 ## 文章列表
  - 采用lstTile 组件显示即可；无需滚动加载，只展示10条；
    listTile 左侧展示文章封面图片；右侧展示文章标题+文章创建时间
    listTile 点击事件：进入文章详情界面
 ## 添加订阅按钮：
  悬浮到页面底部（主题色）

# 相关接口
 ## Google news已经封装好在feeds_repository.dart中
 ## 接口的数据结构，示例：
  {
    "code": 200,
    "msg": null,
    "data": {
        "search": "1",
        "type": 4, //订阅类型
        "list": [ // 只有文章列表 
            {
                "id": null,
                "name": null,
                "enable": null,
                "creator": null,
                "createTime": 1741598389,
                "updator": null,
                "updateTime": null,
                "remark": null,
                "title": "Steelcase Series 1 Office Chair | Steelcase Store",
                "link": "https://store.steelcase.com/seating/office-chairs/steelcase-series-1",
                "guid": null,
                "description": "4 days ago <b>...</b> Standard height-adjustable lumbar support. Seat includes flexible edges and adaptive bolstering in the foam providing a pressure-free sit.",
                "pubDate": null,
                "status": null,
                "source": null,
                "groupHash": null,
                "img": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTPjtSLGwtXhgdo3NkGKhtyQ3GLxpNtIliRZdVSXHk3BL4KlUFd6X-gwg4&s",
                "feedsId": null,
                "feedsName": null,
                "userId": null,
                "hasImg": null,
                "isCollect": null,
                "isLaterRead": null,
                "isRead": null,
                "userFilter": null,
                "statusFilter": null,
                "popular": null
            },
        ],
        "status": 0
    },
    "timestamp": 1741598389
}