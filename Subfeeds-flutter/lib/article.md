# 完成feeds_tab主页的4个顶部标签按钮的跳转页面
 - 页面整体以assets/images/search-background.png为页面背景，深色模式下背景颜色为#141724，浅色模式下背景颜色为#f0f1f7
 - 你需要重建页面并添加路由绑定依赖
 ## 1.自定义appbar，样式参考： Padding(
                padding: const EdgeInsets.all(16.0),
                child: Stack(
                  children: [
                    // 标题居中
                    Align(
                      alignment: Alignment.center,
                      child: Obx(() => Text(
                            controller.searchType.value == 1
                                ? 'nav_discover'.tr
                                : controller.searchType.value == 3
                                    ? 'Telegram'
                                    : controller.searchType.value == 4
                                        ? 'Google News'
                                        : controller.categoryName.value,
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                            textAlign: TextAlign.center,
                          )),
                    ),
                    // 返回按钮
                    Align(
                      alignment: Alignment.centerLeft,
                      child: InkWell(
                        onTap: () => Get.back(),
                        borderRadius: BorderRadius.circular(16),
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? const Color(0xFF3f4458)
                                    : Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: SvgPicture.asset(
                            'assets/feeds/back.svg',
                            width: 15,
                            height: 15,
                            colorFilter: ColorFilter.mode(
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black,
                              BlendMode.srcIn,
                            ),
                            fit: BoxFit.none,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
## 2. 页面头部
 -  采用左右两侧布局
  - 左侧显示跳转进来的标题；如果通过feeds_tab顶部的started按钮进来，那么显示标题为started + 列表总数；
  - 右侧显示横向操作列表，每个列表条目由container + svg组成，按钮有9.5圆角，按钮的背景颜色在浅色模式下为#ced8e2，图标颜色为#333333；深色模式下背景颜色为#323643，图标颜色为#ffffff，总有2个按钮
    - 按钮1 ：删除操作按钮，icon地址：assets/icons/delete.svg
    - 按钮2:  刷新操作按钮，icon地址：assets/icons/reload.svg
## 文章列表：
 1. 文章列表是由ListTile组成的文章列表，文章列表背景颜色深色模式下为：#25293b，浅色模式下为：#ffffff
 2. ListTile展示文章图片，标题展示文章标题，次级标题展示文章创建时间，每个listTile之间有分割线，高度0.5，浅色模式下分割线颜色为#e5e6e9，深色模式下分割线颜色为#575b6b；
 3. 空状态:使用assets/images/article_empty.png作为空状态图片，以及加上文字“Um... There's nothing here（嗯...这里什么都没有）”
## 注意事项：
 1.本页面需要根据跳转进来的类型展示不同类型的标题，列表，总有4种类型：Stated,Read Later,History,Annotated
 2. 需要使用到4个列表接口，都位于article_repository.dart文件中，所有列表的数据结构都一致
  - 通过Stated按钮进来：发送getCollectList请求
  - 通过Read Later按钮进来：发送getLaterReadList请求
  - 通过history按钮进来，发送getReadRecordList请求
  - 通过Annotated按钮进来，发送getUserNoteList请求
 3.列表数据结构：
  {
    "code": 200,
    "msg": null,
    "data": {
        "total": 1, //总数
        "pageList": [
            {
                "id": "23", // 文章索引 后续发送请求不要使用这个
                "userId": null,
                "createTime": 1741776117, // 文章创建时间
                "enable": null,
                "articleId": "328123", //文章id
                "tableName": null,
                "content": "<img src=\"https://p6.focus.de/img/fotos/id_260756359/sondierungen1.jpg?im=Crop%3D%280%2C176%2C3464%2C1956%29%3BResize%3D%281200%29&impolicy=perceptual&quality=mediumHigh&hash=f2ac7197721aeb70589633989c306f1459ec124ba3f0dbaea8a2cc56a099d8da\" />Union und SPD haben die Sondierungen erfolgreich beendet. Die Vorstände von CSU und SPD haben Koalitionsverhandlungen zugestimmt, nun fehlt nur noch die CDU, die am Montag abstimmt. Alle Entwicklungen zur Bundestagswahl im Newsticker.Von FOCUS-online-Redakteur Peter Althaus, FOCUS-online-Redakteur Till Dörken, FOCUS-online-Redakteurin Laura Thiel, FOCUS-online-Autor Dominik Voss, FOCUS-online-Redakteur Maik Mosheim, FOCUS-online-Redakteur Stefan Huber",
                "title": "+++ Koalitionsgespräche im Newsticker +++ - Merz will mehr Zurückweisungen an den Grenzen und hofft auf Domino-Effekt",
                "startTime": null,
                "endTime": null,
                "isCollect": 0, // 是否收藏 0 = 未收藏 1 = 已收藏
                "isLaterRead": 1, //是否稍后阅读 0 = 不是 1 = 是
                "img": "https://p6.focus.de/img/fotos/id_260756359/sondierungen1.jpg?im=Crop%3D%280%2C176%2C3464%2C1956%29%3BResize%3D%281200%29&impolicy=perceptual&quality=mediumHigh&hash=f2ac7197721aeb70589633989c306f1459ec124ba3f0dbaea8a2cc56a099d8da", //文章封面图片
                "pubDate": "2025-03-10T09:06:39.000+00:00", //发布时间
                "feedsId": "3939",
                "feedsName": "\t\t\t\t\tFOCUS online",
                "feedsImg": "/img/bfef3b1a38f27adc885565a86855e6e1.jpg"
            }
        ],
        "pageNum": 1,
        "pageSize": 40,
        "sumPage": 1
    },
    "timestamp": 1741776121
}